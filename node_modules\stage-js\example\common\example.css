body {
  font-family: sans-serif;
  overflow: hidden;
}

.about,
.hint {
  position: absolute;
  padding: 0.7em 1em;
  background: rgba(240, 240, 240, 0.7);
}

.dark .about,
.dark .hint {
  background: rgba(32, 32, 32, 0.7);
}

.about {
  right: 0.5em;
  bottom: 0.5em;
}

.hint {
  left: 0.5em;
  bottom: 0.5em;
}

.loading {
  margin: 10% 10%;
  font-size: xx-large;
  position: absolute;
}

a {
  text-decoration: none;
  color: blue;
}

a:hover {
  text-decoration: underline;
}

.dark {
  color: #ccc;
  background: #222;
}

.dark a {
  color: #fff;
}

canvas {
  background: #fff;
}

.dark canvas {
  background: #222;
}
