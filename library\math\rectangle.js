// @ts-check

import { SharedPrimitive } from "../tuple/tuple.js";
import TypeBuilder, { Template } from "../type-builder/type-builder.js";
import Vector2Template from "./vector.js";

const Rect2Template = new TypeBuilder(
  [
    new Template(Array, Int8Array, Int16Array, Int32Array, Uint8Array, Uint16Array, Uint32Array, Float32Array, Float64Array),
    new Template(Number.prototype)
  ],
  function(T) {
    return function(ArrayLike, extraSize = 0) {
      const Vec2 = Vector2Template.from(ArrayLike, SharedPrimitive.create(extraSize.valueOf() + 2));
      return class Rect2 extends Vec2 {

        static Vec2 = Vec2;

        /**
         * @param {number} x
         * @param {number} y
         * @param {number} width
         * @param {number} height
        */
        constructor(x, y, width, height) {
          super(x, y);
          /** `width` component */
          this[2] = width;
          /** `height` component */
          this[3] = height;
        }

        get width() {
          return this[2];
        }
        get height() {
          return this[3];
        }
        set width(value) {
          this[2] = value;
        }
        set height(value) {
          this[3] = value;
        }

        /** @returns {[ x: number, y: number, width: number, height: number ]} */
        values() {
          return /** @type {[ x: number, y: number, width: number, height: number ]} */ (/** @type {unknown} */ (this));
        }

        /**
         * @param {number} x
         * @param {number} y
         * @param {number} width
         * @param {number} height
        */
        set(x, y, width = 0, height = 0) {
          super.set(x, y);
          this[2] = width;
          this[3] = height;
        }

        /** @param {Rect2} rect */
        setFrom(rect) {
          super.setFrom(rect);
          this[2] = rect[2];
          this[3] = rect[3];
        }

      };
    };
  }
);

export default Rect2Template;