// @ts-check

import Camera from "../library/canvas/camera.js";
import Loop from "../library/time/loop.js";
import PhysicsWorld from "./entities/physics-world/physics-world.js";
import Renderer from "./renderer.js";
import Vector2Template from "../library/math/vector.js";
import { Bot } from "./entities/bot.js";
import { SharedPrimitive } from "../library/tuple/tuple.js";

const Vector2 = Vector2Template.from(Float32Array, SharedPrimitive.create(0));
/** @typedef {InstanceType<typeof Vector2>} Vector2 */

class MiniMap {
  #game;
  camera;
  zoom = 4;
  position = Vector2.zero();

  /** @param {Game} game */
  constructor(game) {
    this.#game = game;
    this.camera = new Camera(game.renderer.context);
    this.camera.zoomTo(160);
  }

  /** @param {Renderer} renderer */
  render(renderer) {
    renderer.circle(this.position, 80, "#fff2");
    this.camera.begin();
    for(const bot of this.#game.bots) {
      bot.render(renderer);
    }
    this.camera.end();
  }
}

export default class Game extends Loop {
  #physicsWorld;
  /** @type {Bot[]} */
  #bots = [];

  /** @param {Renderer} renderer */
  constructor(renderer) {
    super();
    this.renderer = renderer;
    this.#physicsWorld = new PhysicsWorld;
    this.camera = new Camera(renderer.context);
    this.miniMap = new MiniMap(this);
    this.camera.moveTo(0, 0);
  }

  get bots() {
    return this.#bots;
  }

  /** @param {Bot} bot */
  addBot(bot) {
    this.#bots.push(bot);
  }

  /** @param {Bot} bot */
  removeBot(bot) {
    const index = this.#bots.indexOf(bot);
    if(index === -1) {
      return;
    }
    this.#bots.splice(index, 1);
  }

  /** @param {number} delta */
  update(delta) {
    this.#physicsWorld.update(delta);
    for(const bot of this.#bots) {
      bot.update(delta);
    }
  }

  render() {
    this.renderer.clear();
    this.camera.begin();
    this.renderer.line(new Vector2(0, -10), new Vector2(0, 10), 1, "#f00");
    this.renderer.line(new Vector2(-10, 0), new Vector2(10, 0), 1, "#00f");
    for(const bot of this.#bots) {
      bot.render(this.renderer);
    }
    this.camera.end();
    this.miniMap.render(this.renderer);
  }
};