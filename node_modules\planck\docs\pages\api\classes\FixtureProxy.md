# Class: FixtureProxy

This proxy is used internally to connect shape children to the broad-phase.

## Constructors

### new FixtureProxy()

> **new FixtureProxy**(`fixture`, `childIndex`): [`FixtureProxy`](/api/classes/FixtureProxy)

#### Parameters

• **fixture**: [`Fixture`](/api/classes/Fixture)

• **childIndex**: `number`

#### Returns

[`FixtureProxy`](/api/classes/FixtureProxy)

## Properties

### aabb

> **aabb**: [`AABB`](/api/classes/AABB)

***

### childIndex

> **childIndex**: `number`

***

### fixture

> **fixture**: [`Fixture`](/api/classes/Fixture)

***

### proxyId

> **proxyId**: `number`
