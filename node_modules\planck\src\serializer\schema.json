{"type": "array", "additionalProperties": false, "items": {"oneOf": [{"$ref": "#World"}, {"$ref": "#Body"}, {"$ref": "#Fixture"}, {"$ref": "#Shape"}, {"$ref": "#Joint"}]}, "definitions": {"ref": {"$id": "#Ref", "type": "object", "additionalProperties": false, "required": ["refIndex", "refType"], "properties": {"refIndex": {"type": "number"}, "refType": {"type": "string", "enum": ["World", "Body", "Fixture", "<PERSON><PERSON><PERSON>", "Joint"]}}}, "World": {"$id": "#World", "type": "object", "additionalProperties": false, "properties": {"gravity": {"$ref": "#Vec2"}, "bodies": {"type": "array", "items": {"$ref": "#Ref"}}, "joints": {"type": "array", "items": {"$ref": "#Ref"}}}}, "Body": {"$id": "#Body", "type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["static", "dynamic", "kinematic"]}, "bullet": {"type": "boolean"}, "position": {"$ref": "#Vec2"}, "angle": {"type": "number"}, "linearVelocity": {"$ref": "#Vec2"}, "angularVelocity": {"type": "number"}, "fixtures": {"type": "array", "items": {"$ref": "#Ref"}}}}, "Fixture": {"$id": "#Fixture", "type": "object", "additionalProperties": false, "properties": {"friction": {"type": "number"}, "restitution": {"type": "number"}, "density": {"type": "number"}, "isSensor": {"type": "boolean"}, "filterGroupIndex": {"type": "number"}, "filterCategoryBits": {"type": "number"}, "filterMaskBits": {"type": "number"}, "shape": {"$ref": "#Ref"}, "userData": {"type": "object"}}}, "Shape": {"$id": "#Shape", "type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["edge", "circle", "polygon", "chain", "box"]}, "vertices": {"type": "array", "items": {"$ref": "#Vec2"}}, "isLoop": {"type": "boolean"}, "prevVertex": {"anyOf": [{"$ref": "#Vec2"}, {"type": "null"}]}, "nextVertex": {"anyOf": [{"$ref": "#Vec2"}, {"type": "null"}]}, "hasPrevVertex": {"type": "boolean"}, "hasNextVertex": {"type": "boolean"}, "vertex1": {"$ref": "#Vec2"}, "vertex2": {"$ref": "#Vec2"}, "vertex0": {"$ref": "#Vec2"}, "vertex3": {"$ref": "#Vec2"}, "hasVertex0": {"type": "boolean"}, "hasVertex3": {"type": "boolean"}, "p": {"$ref": "#Vec2"}, "radius": {"type": "number"}}}, "Joint": {"$id": "#Joint", "type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["distance-joint", "friction-joint", "motor-joint", "prismatic-joint", "revolute-joint", "weld-joint", "wheel-joint"]}, "bodyA": {"$ref": "#Ref"}, "bodyB": {"$ref": "#Ref"}, "collideConnected": {"type": "boolean"}, "localAnchorA": {"$ref": "#Vec2"}, "localAnchorB": {"$ref": "#Vec2"}, "localAxis": {"$ref": "#Vec2"}, "localAxisA": {"$ref": "#Vec2"}, "frequencyHz": {"type": "number"}, "dampingRatio": {"type": "number"}, "length": {"type": "number"}, "enableLimit": {"type": "boolean"}, "lowerAngle": {"type": "number"}, "upperAngle": {"type": "number"}, "lowerTranslation": {"type": "number"}, "upperTranslation": {"type": "number"}, "enableMotor": {"type": "boolean"}, "maxMotorTorque": {"type": "number"}, "motorSpeed": {"type": "number"}, "maxMotorForce": {"type": "number"}, "referenceAngle": {"type": "number"}, "angularOffset": {"type": "number"}, "linearOffset": {"$ref": "#Vec2"}, "correctionFactor": {"type": "number"}, "maxForce": {"type": "number"}, "maxTorque": {"type": "number"}, "groundAnchorA": {"$ref": "#Vec2"}, "groundAnchorB": {"$ref": "#Vec2"}, "lengthA": {"type": "number"}, "lengthB": {"type": "number"}, "maxLength": {"type": "number"}, "ratio": {"type": "number"}, "impulse": {"type": "number"}, "gamma": {"type": "number"}, "bias": {"type": "number"}}}, "Vec2": {"$id": "#Vec2", "type": "object", "additionalProperties": false, "required": ["x", "y"], "properties": {"x": {"type": "number"}, "y": {"type": "number"}}}}}