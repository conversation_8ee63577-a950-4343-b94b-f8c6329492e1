<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Custom Script</title>
  <script type="module" src="./custom-script.js"></script>
  <script type="text/script">
class Parent {
  proctected valueForChild = 10;
  private valueForSelf = 20;
  public valueForAll = 30;
}

class Child extends Parent {
}

const parentInstance = new Parent;
const childInstance = new Child;
  </script>
</head>
<body>
  
</body>
</html>