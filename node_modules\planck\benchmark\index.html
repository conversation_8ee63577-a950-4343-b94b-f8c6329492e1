<html>
    <head>
        <meta charset="utf-8" />
        <title>Planck.js/Box2D Benchmark</title>
        <style>
            th, td {
                padding: 2px 5px;
            }
            table {
                border-collapse: collapse;
                margin-top: 5px;
            }
            td {
                border: 1px solid gray;
            }
            th:first-child {
                text-align: left;
            }
            progress {
                width: 200px;
            }
            textarea {
                width: 500px;
                height: 300px;
            }
        </style>
    </head>
    <body>
        <h3>Planck.js/Box2D Benchmark</h3>
        <button id="start">Start</button>
        <table>
            <thead>
                <tr>
                    <th>Name</th>
                    <th>avg ms/frame</th>
                    <th>5th %ile</th>
                    <th>95th %ile</th>
                    <th id="ratio">Ratio</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
        <script src="https://kripken.github.io/box2d.js/build/Box2D_v2.3.1_min.js"></script>
        <script type="module" src="./browser.ts"></script>
        <br />
        <br />
        <span id="branch-name"></span>
    </body>
</html>
