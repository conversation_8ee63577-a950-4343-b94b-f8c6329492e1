# Function: clipSegmentToLine()

> **clipSegmentToLine**(`vOut`, `vIn`, `normal`, `offset`, `vertexIndexA`): `number`

Clipping for contact manifolds. <PERSON><PERSON><PERSON><PERSON><PERSON> clipping.

## Parameters

• **vOut**: [`ClipVertex`](/api/classes/ClipVertex)[]

• **vIn**: [`ClipVertex`](/api/classes/ClipVertex)[]

• **normal**: [`Vec2Value`](/api/interfaces/Vec2Value)

• **offset**: `number`

• **vertexIndexA**: `number`

## Returns

`number`
