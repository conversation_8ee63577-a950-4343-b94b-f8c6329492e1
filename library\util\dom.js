export function elementsById(log = false) {
  /** @type {{ [id: string]: HTMLElement }} */
  const obj = {};
  let str = "/** @type {{ ";
  document.querySelectorAll("[id]").forEach(element => {
    const templatesString = "getTemplates" in element.constructor ? `<${ element.constructor.getTemplates(element).join(", ") }>` : "";
    str += `"${ element.id }": ${ element.constructor.name }${ templatesString }; `;
    obj[element.id] = element;
  });
  if(str.includes(";")){
    str = str.substring(0, str.length - 2);
  }
  if(log){
    console.log(str + " }} */");
  }
  Object.freeze(obj);
  Object.seal(obj);
  Object.preventExtensions(obj);
  return obj;
}