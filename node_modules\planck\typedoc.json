{"name": "API Reference", "readme": "none", "entryPoints": ["./src"], "out": "./docs/pages/api/", "basePath": "/api/", "publicPath": "/api/", "exclude": ["**/*.test-d.ts", "**/*.test.ts", "**/*.test.js", "**/index.ts", "**/main.ts", "src/util/options*", "src/util/Pool*", "src/util/Timer*", "src/common/Matrix*", "node_modules/*", "testbed/*", "docs/*", "dist/*"], "disableSources": true, "excludeExternals": true, "excludePrivate": true, "excludeInternal": true, "hideBreadcrumbs": true, "hidePageHeader": true, "mergeReadme": true, "plugin": ["typedoc-plugin-markdown"]}