// @ts-check

/** @template T */
export default class BiArray {

  /** @type {{ negative: T[]; positive: T[] }} */
  #data;

  /** @param {[iterable: Iterable<[number, T]>] | [size: number] | []} args */
  constructor(...args) {
    if(typeof args[0] === "number") {
      this.reset(args[0]);
      return this;
    }
    if(args.length === 0) {
      this.reset(0);
      return this;
    }
    this.#data = Object.freeze({ negative: [], positive: [] });
    for(const entry of args[0]) {
      this.set(...entry);
    }
  }

  get length() {
    return this.#data.negative.length + this.#data.positive.length;
  }

  /** @param {number} index */
  at(index) {
    if(index < 0) {
      return this.#data.negative[ Math.abs(index) - 1 ];
    }
    return this.#data.positive[index];
  }

  /**
   * @param {number} index
   * @param {T} value
   */
  set(index, value) {
    if(index < 0) {
      return this.#data.negative[ Math.abs(index) - 1 ] = value;
    }
    return this.#data.positive[index] = value;
  }

  /** @param {number} index */
  has(index) {
    if(index < 0) {
      return Math.abs(index) - 1 in this.#data.negative;
    }
    return index in this.#data.positive;
  }

  /** @param {number} index */
  delete(index) {
    if(index < 0) {
      return delete this.#data.negative[ Math.abs(index) - 1 ];
    }
    return delete this.#data.positive[index];
  }

  /** @param {T} value */
  fill(value) {
    this.#data.negative.fill(value);
    this.#data.positive.fill(value);
    return this;
  }

  /** @type {Array<T>["push"]} */
  push(...items) {
    return this.#data.positive.push(...items) + this.#data.negative.length;
  }

  /** @type {Array<T>["unshift"]} */
  unshift(...items) {
    return this.#data.negative.push(...items) + this.#data.positive.length;
  }

  /** @type {Array<T>["pop"]} */
  pop() {
    return this.#data.positive.pop();
  }

  /** @type {Array<T>["shift"]} */
  shift() {
    return this.#data.negative.pop();
  }

  /**
   * @param {number} index
   * @param {number} deleteCount
   */
  remove(index, deleteCount) {
    let negativeDeleteCount = 0;
    /** @type {BiArray<T>} */
    const result = new BiArray();
    if(index < 0) {
      negativeDeleteCount = Math.min(Math.abs(index), deleteCount);
      result.unshift(...this.#data.negative.splice(0, negativeDeleteCount));
    }
    const positiveDeleteCount = deleteCount - negativeDeleteCount;
    if(positiveDeleteCount !== 0) {
      result.push(...this.#data.positive.splice(Math.max(0, index), positiveDeleteCount));
    }
    return result;
  }

  /**
   * @param {number} index
   * @param {T[]} items
   */
  insert(index, ...items) {
    let negativeDeleteCount = 0;
    /** @type {BiArray<T>} */
    const result = new BiArray();
    const deleteCount = items.length;
    if(index < 0) {
      negativeDeleteCount = Math.min(Math.abs(index), deleteCount);
      result.unshift(...this.#data.negative.splice(0, negativeDeleteCount));
    }
    const positiveDeleteCount = deleteCount - negativeDeleteCount;
    if(positiveDeleteCount !== 0) {
      result.push(...this.#data.positive.splice(Math.max(0, index), positiveDeleteCount));
    }
    return result;
  }

  /** @param {number} newSize */
  reset(newSize) {
    this.#data = Object.freeze({
      negative: new Array(Math.floor(newSize / 2)),
      positive: new Array(Math.ceil(newSize / 2))
    });
  }

  /** @param {(element: T, index: number, array: this) => void} callbackfn */
  forEach(callbackfn) {
    for(const entry of this.#filterEntries(false)) {
      callbackfn(entry[1], entry[0], this);
    }
  }

  /** @param {(element: T, index: number, array: this) => boolean} callbackfn */
  filter(callbackfn) {
    /** @type {BiArray<T>} */
    const result = new BiArray(this.length);
    for(const entry of this.#filterEntries(false)) {
      if(callbackfn(entry[1], entry[0], this)) {
        result.set(entry[0], entry[1]);
      }
    }
    return result;
  }

  /**
   * @template U
   * @param {(element: T, index: number, array: this) => U} callbackfn
   */
  map(callbackfn) {
    /** @type {BiArray<U>} */
    const result = new BiArray();
    for(const entry of this.#filterEntries(false)) {
      result.set(entry[0], callbackfn(entry[1], entry[0], this));
    }
    return result;
  }

  *keys() {
    for(let index = -this.#data.negative.length; index < this.#data.positive.length; index++) {
      yield index;
    }
  }

  *values() {
    const negativeSize = this.#data.negative.length;
    for(let index = negativeSize - 1; index >= 0; index--) {
      yield this.#data.negative[index];
    }
    for(const entry of this.#data.positive.values()) {
      yield entry;
    }
  }

  *entries() {
    for(const entry of this.#filterEntries()) {
      yield entry;
    }
  }

  /** @returns {IterableIterator<[index: number, element: T]>} */
  *#filterEntries(includeNotDefined = true) {
    const negativeSize = this.#data.negative.length;
    for(let index = negativeSize - 1; index >= 0; index--) {
      if(includeNotDefined || index in this.#data.negative) {
        yield [ -index - 1, this.#data.negative[index] ];
      }
    }
    for(const entry of this.#data.positive.entries()) {
      if(includeNotDefined || entry[0] in this.#data.positive) {
        yield entry;
      }
    }
  }

  *[Symbol.iterator]() {
    for(const element of this.values()) {
      yield element;
    }
  }


  /**
   * @template T
   * @param {Iterable<[number, T]>} iterable
   */
  static from(iterable) {
    /** @type {BiArray2D<number, number, T>} */
    const instance = new BiArray();
    for(const entry of iterable) {
      instance.set(...entry);
    }
    return instance;
  }

};