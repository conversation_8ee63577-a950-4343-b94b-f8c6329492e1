class StringableText extends Text {
  constructor(data) {
    super(data);
  }
  [Symbol.toPrimitive]() {
    return this.data;
  }
};

/** @template {string} T */
export default class VariableManager {

  /** @type {Map<T, Text[]>} */
  #variables = new Map();

  /** @type {Map<Text, { attr: Attr; representation: (string | StringableText)[] }[]>} */
  #attributes = new WeakMap();

  /** @param {Document} document */
  constructor(document, log = false) {
    const nodes = VariableManager.evaluateAnyType(document, "//*[contains(text(), \"{{\")]");
    const attributeNodes = VariableManager.evaluateAnyType(document, "//*[@*[contains(., \"{{\")]]");

    for(const node of nodes) {
      const textNodes = this.asTextContent(node.textContent);
      node.textContent = "";
      node.append(...textNodes);
    }

    for(const node of attributeNodes) {
      for(const attr of node.attributes) {
        this.asAttribute(attr);
      }
    }

    if(log) {
      console.log(`/** @type {${ new.target.name }<"${ Array.from(this.#variables.keys()).join("\" | \"") }">} */`);
    }
  }

  createIfNotExist(name) {
    if(!this.#variables.has(name)) {
      this.#variables.set(name, []);
    }
  }

  addTextContent(name) {
    const node = new Text(`{{${ name }}}`);
    this.createIfNotExist(name);
    this.#variables.get(name).push(node);
    return node;
  }

  /** @param {Attr} attr */
  addAttribute(name, attr, representation) {
    const node = new StringableText(`{{${ name }}}`);
    this.createIfNotExist(name);
    this.#variables.get(name).push(node);
    if(!this.#attributes.has(node)) {
      this.#attributes.set(node, []);
    }
    this.#attributes.get(node).push({ attr, representation });
    return node;
  }

  /**
   * @param {T} name
   * @param {string} value
   */
  set(name, value) {
    if(!this.#variables.has(name)) {
      return false;
    }
    for(const node of this.#variables.get(name)) {
      node.textContent = value;
      if(this.#attributes.has(node)) {
        for(const attribute of this.#attributes.get(node)) {
          attribute.attr.value = attribute.representation.join("");
        }
      }
    }
    return true;
  }

  /** @param {{ [K in T]: string }} variables */
  setMany(variables) {
    for(const name in variables) {
      this.set(name, variables[name]);
    }
  }

  /**
   * @template R
   * @param {string} text
   * @param {(variableName: string) => R} cb
  */
  static parse(text, cb) {
    return text.split("{{").flatMap((text, index) => {
      const [ variableName, ...restOfText ] = text.split("}}");
      if(restOfText.length === 0) {
        return (index === 0 ? "" : "{{") + text;
      }
      return [ cb(variableName.trim()), restOfText.join("}}") ];
    }).filter(text => text !== "");
  }

  /** @param {string} text */
  asTextContent(text) {
    return VariableManager.parse(text, variableName => this.addTextContent(variableName));
  }

  /** @param {Attr} attr */
  asAttribute(attr) {
    const representation = [];
    const contents = VariableManager.parse(attr.value, variableName => this.addAttribute(variableName, attr, representation));
    representation.push(...contents);
  }

  /** @param {Document} document */
  static evaluateAnyType(document, expression) {
    const nodeResult = document.evaluate(expression, document, null, XPathResult.ANY_TYPE, null);
    let node = nodeResult.iterateNext();
    const nodes = [];
    while(node !== null) {
      nodes.push(node);
      node = nodeResult.iterateNext();
    }
    return nodes;
  }

};