body, html {
	margin: 0;
	padding: 0;
	height: 100%;
	width: 100%;
	font-family: sans-serif;
}

body {
	background: #ddd;
	font-family: <PERSON>bu<PERSON><PERSON>, Arial, sans-serif;
}

a {
	text-decoration: none;
	color: #1c83eb;
}

a:hover {
	text-decoration: underline;
}

.center {
	margin: 0 auto;
	max-width: 900px;
	position: relative;
}

.header {
	padding: 25px 0 15px;
}

.title {
	color: #444;
	font-size: 20px;
	text-shadow: 0 1px rgba(255, 255, 255, 0.5);
	margin: 6px;
}

.title a {
	text-decoration: none;
	color: inherit;
}

.title a:hover {
	font-style: italic;
}

.control {
	float: right;
	margin: 0;
	font-size: 0.9em;
}

.control a {
	color: #555;
	cursor: pointer;
}

.control .play .play, .control .pause .pause {
	display: none;
}

table.control tr, table.control td {
	margin: 0;
	padding: 0;
	border: 0 none;
	border-spacing: 0;
}

table.control td {
	vertical-align: center;
	padding: 2px 0 2px 10px;
}

select {
	vertical-align: top;
	line-height: 26px;
	height: 26px;
}

select {
	padding-left: 0.4em;
}

#stage {
	display: block;
	width: 100%;
	height: 600px;
	background: #333;
	border-radius: 2px;
}

#status {
	font-family: monospace;
	color: #333;
	margin: 1em 0;
}

#info {
	font-family: monospace;
	color: #333;
	margin: 1em 0;
}

#stage {
	display: block;
	width: 100%;
	height: 600px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}

.hide {
	display: none;
	visibility: collapse;
}

@media screen and (max-width: 920px) , screen and (max-height: 700px) {
	.header {
		padding: 6px 8px 6px;
		position: absolute;
		left: 0;
		right: 0;
		background: rgba(255, 255, 255, 0.2);
	}
	.title {
		color: #fff;
		font-size: 18px;
		font-weight: bold;
		text-shadow: none;
	}
	.center, #code, #stage {
		height: 100%;
		border-radius: 0;
	}
	.center {
		max-width: 100%;
	}
	select, button {
		border: 0px solid #666;
		border-radius: 0px;
		background: #333;
		color: #fff;
	}
	.control a {
		color: #ddd;
	}
	.output {
		position: absolute;
		bottom: 0;
		left: 0;
		padding: 1em 0.5em;
		color: #dddddd;
		pointer-events: none;
		font-family: 'Courier New', Courier, monospace;
	}
}