// @ts-check

import { fnBind } from "../util/function.js";

export default class Loop {

  /** @type {number | undefined} */
  #timeoutId;
  /** @type {number} */
  #lastFrameTime;
  #tickFrequency = Math.floor(1000 / 60);
  #lag = 0;
  #isRunning = false;
  #bindedLoop = fnBind(this.#loop, this);

  async start() {
    if(this.#isRunning) {
      return;
    }
    this.#isRunning = true;
    this.#lastFrameTime = Date.now();
    this.#loop();
  }

  async stop() {
    clearTimeout(this.#timeoutId);
    this.#isRunning = false;
  }

  /** @virtual @param {number} delta */
  update(delta) {}

  /**
   * gets called after `update
   * @virtual
  */
  render() {}

  #setTimeout() {
    clearTimeout(this.#timeoutId);
    this.#timeoutId = setTimeout(this.#bindedLoop, this.#tickFrequency);
  }

  #loop() {
    const currentTime = performance.now();
    const elapsedTime = currentTime - this.#lastFrameTime;
    this.#lastFrameTime = currentTime;
    this.#lag += elapsedTime;
    // console.log(this.#lag);
    // let updateIterations = Math.ceil(elapsedTime / this.#tickFrequency);
    while(this.#lag >= this.#tickFrequency) {
      const delta = this.#tickFrequency / 1000;
      this.update(delta);
      this.#lag -= this.#tickFrequency;
    }
    this.render();
    this.#setTimeout();
  }

};