export class Counter {

  time = 0;
  running = false;
  counts = 0;
  /** @type {((counter: Counter) => void)[]} */
  onDone = [];

  /** @param {number} maxTime in milliseconds */
  constructor(maxTime) {
    this.maxTime = maxTime;
  }

  get progress() {
    return this.time / this.maxTime;
  }

  start() {
    this.running = true;
  }

  pause() {
    this.running = false;
  }

  reset() {
    this.time = 0;
  }

  resetCounts() {
    this.counts = 0;
  }

  stop() {
    this.running = false;
    this.time = 0;
  }

  end() {
    if(!this.running) {
      return false;
    }
    for(const handler of this.onDone) {
      handler(this);
    }
    this.stop();
    return true;
  }

  update(delta) {
    if(!this.running) {
      return;
    }
    this.time += delta;
    if(this.time < this.maxTime) {
      return;
    }
    this.counts++;
    this.time = this.maxTime - this.time;
    for(const handler of this.onDone) {
      handler(this);
    }
  }

};