# Class: ShapeCastInput

Input parameters for ShapeCast

## Constructors

### new ShapeCastInput()

> **new ShapeCastInput**(): [`ShapeCastInput`](/api/classes/ShapeCastInput)

#### Returns

[`ShapeCastInput`](/api/classes/ShapeCastInput)

## Properties

### proxyA

> `readonly` **proxyA**: [`DistanceProxy`](/api/classes/DistanceProxy)

***

### proxyB

> `readonly` **proxyB**: [`DistanceProxy`](/api/classes/DistanceProxy)

***

### transformA

> `readonly` **transformA**: [`Transform`](/api/classes/Transform)

***

### transformB

> `readonly` **transformB**: [`Transform`](/api/classes/Transform)

***

### translationB

> `readonly` **translationB**: [`Vec2`](/api/classes/Vec2)

## Methods

### recycle()

> **recycle**(): `void`

#### Returns

`void`
