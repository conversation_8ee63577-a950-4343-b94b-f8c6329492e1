# Function: CollideEdgePolygon()

> **CollideEdgePolygon**(`manifold`, `edgeA`, `xfA`, `polygonB`, `xfB`): `void`

This function collides and edge and a polygon, taking into account edge
adjacency.

## Parameters

• **manifold**: [`Manifold`](/api/classes/Manifold)

• **edgeA**: [`EdgeShape`](/api/classes/EdgeShape)

• **xfA**: [`TransformValue`](/api/type-aliases/TransformValue)

• **polygonB**: [`PolygonShape`](/api/classes/PolygonShape)

• **xfB**: [`TransformValue`](/api/type-aliases/TransformValue)

## Returns

`void`
