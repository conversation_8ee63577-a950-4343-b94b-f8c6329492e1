{"version": 3, "file": "stage.js", "sources": ["../src/common/math.ts", "../src/common/matrix.ts", "../node_modules/tslib/tslib.es6.js", "../src/common/is.ts", "../src/common/stats.ts", "../src/common/uid.ts", "../src/texture/texture.ts", "../src/texture/image.ts", "../src/texture/pipe.ts", "../src/texture/atlas.ts", "../src/texture/selection.ts", "../src/texture/resizable.ts", "../src/common/browser.ts", "../src/core/pin.ts", "../src/core/easing.ts", "../src/core/transition.ts", "../src/core/component.ts", "../src/core/sprite.ts", "../src/texture/canvas.ts", "../src/core/pointer.ts", "../src/core/root.ts", "../src/core/anim.ts", "../src/core/monotype.ts"], "sourcesContent": ["/** @internal */ const math_random = Math.random;\n/** @internal */ const math_sqrt = Math.sqrt;\n\n/** @internal */\nexport function random(min?: number, max?: number): number {\n  if (typeof min === \"undefined\") {\n    max = 1;\n    min = 0;\n  } else if (typeof max === \"undefined\") {\n    max = min;\n    min = 0;\n  }\n  return min == max ? min : math_random() * (max - min) + min;\n}\n\n/** @internal */\nexport function wrap(num: number, min?: number, max?: number): number {\n  if (typeof min === \"undefined\") {\n    max = 1;\n    min = 0;\n  } else if (typeof max === \"undefined\") {\n    max = min;\n    min = 0;\n  }\n  if (max > min) {\n    num = (num - min) % (max - min);\n    return num + (num < 0 ? max : min);\n  } else {\n    num = (num - max) % (min - max);\n    return num + (num <= 0 ? min : max);\n  }\n}\n\n/** @internal */\nexport function clamp(num: number, min: number, max: number): number {\n  if (num < min) {\n    return min;\n  } else if (num > max) {\n    return max;\n  } else {\n    return num;\n  }\n}\n\n/** @internal */\nexport function length(x: number, y: number): number {\n  return math_sqrt(x * x + y * y);\n}\n\nexport const math = Object.create(Math);\n\nmath.random = random;\nmath.wrap = wrap;\nmath.clamp = clamp;\nmath.length = length;\n\n/** @hidden @deprecated @internal */\nmath.rotate = wrap;\n/** @hidden @deprecated @internal */\nmath.limit = clamp;\n", "export interface MatrixValue {\n  a: number;\n  b: number;\n  c: number;\n  d: number;\n  e: number;\n  f: number;\n}\n\nexport interface Vec2Value {\n  x: number;\n  y: number;\n}\n\nexport class Matrix {\n  /** x-scale */\n  a = 1;\n  b = 0;\n  c = 0;\n  /** y-scale */\n  d = 1;\n  /** x-translate */\n  e = 0;\n  /** y-translate */\n  f = 0;\n\n  /** @internal */ private _dirty: boolean;\n  /** @internal */ private inverted?: Matrix;\n\n  constructor(a: number, b: number, c: number, d: number, e: number, f: number);\n  constructor(m: MatrixValue);\n  constructor();\n  constructor(\n    a?: number | MatrixValue,\n    b?: number,\n    c?: number,\n    d?: number,\n    e?: number,\n    f?: number,\n  ) {\n    if (typeof a === \"object\") {\n      this.reset(a);\n    } else {\n      this.reset(a, b, c, d, e, f);\n    }\n  }\n\n  toString() {\n    return (\n      \"[\" +\n      this.a +\n      \", \" +\n      this.b +\n      \", \" +\n      this.c +\n      \", \" +\n      this.d +\n      \", \" +\n      this.e +\n      \", \" +\n      this.f +\n      \"]\"\n    );\n  }\n\n  clone() {\n    return new Matrix(this.a, this.b, this.c, this.d, this.e, this.f);\n  }\n\n  reset(a: number, b: number, c: number, d: number, e: number, f: number): this;\n  reset(m: MatrixValue): this;\n  reset(\n    a?: number | MatrixValue,\n    b?: number,\n    c?: number,\n    d?: number,\n    e?: number,\n    f?: number,\n  ): this {\n    this._dirty = true;\n    if (typeof a === \"object\") {\n      this.a = a.a;\n      this.d = a.d;\n      this.b = a.b;\n      this.c = a.c;\n      this.e = a.e;\n      this.f = a.f;\n    } else {\n      this.a = typeof a === \"number\" ? a : 1;\n      this.b = typeof b === \"number\" ? b : 0;\n      this.c = typeof c === \"number\" ? c : 0;\n      this.d = typeof d === \"number\" ? d : 1;\n      this.e = typeof e === \"number\" ? e : 0;\n      this.f = typeof f === \"number\" ? f : 0;\n    }\n    return this;\n  }\n\n  identity() {\n    this._dirty = true;\n    this.a = 1;\n    this.b = 0;\n    this.c = 0;\n    this.d = 1;\n    this.e = 0;\n    this.f = 0;\n    return this;\n  }\n\n  rotate(angle: number) {\n    if (!angle) {\n      return this;\n    }\n\n    this._dirty = true;\n\n    const u = angle ? Math.cos(angle) : 1;\n    // android bug may give bad 0 values\n    const v = angle ? Math.sin(angle) : 0;\n\n    const a = u * this.a - v * this.b;\n    const b = u * this.b + v * this.a;\n    const c = u * this.c - v * this.d;\n    const d = u * this.d + v * this.c;\n    const e = u * this.e - v * this.f;\n    const f = u * this.f + v * this.e;\n\n    this.a = a;\n    this.b = b;\n    this.c = c;\n    this.d = d;\n    this.e = e;\n    this.f = f;\n\n    return this;\n  }\n\n  translate(x: number, y: number) {\n    if (!x && !y) {\n      return this;\n    }\n    this._dirty = true;\n    this.e += x;\n    this.f += y;\n    return this;\n  }\n\n  scale(x: number, y: number) {\n    if (!(x - 1) && !(y - 1)) {\n      return this;\n    }\n    this._dirty = true;\n    this.a *= x;\n    this.b *= y;\n    this.c *= x;\n    this.d *= y;\n    this.e *= x;\n    this.f *= y;\n    return this;\n  }\n\n  skew(x: number, y: number) {\n    if (!x && !y) {\n      return this;\n    }\n    this._dirty = true;\n\n    const a = this.a + this.b * x;\n    const b = this.b + this.a * y;\n    const c = this.c + this.d * x;\n    const d = this.d + this.c * y;\n    const e = this.e + this.f * x;\n    const f = this.f + this.e * y;\n\n    this.a = a;\n    this.b = b;\n    this.c = c;\n    this.d = d;\n    this.e = e;\n    this.f = f;\n    return this;\n  }\n\n  concat(m: MatrixValue) {\n    this._dirty = true;\n\n    const a = this.a * m.a + this.b * m.c;\n    const b = this.b * m.d + this.a * m.b;\n    const c = this.c * m.a + this.d * m.c;\n    const d = this.d * m.d + this.c * m.b;\n    const e = this.e * m.a + m.e + this.f * m.c;\n    const f = this.f * m.d + m.f + this.e * m.b;\n\n    this.a = a;\n    this.b = b;\n    this.c = c;\n    this.d = d;\n    this.e = e;\n    this.f = f;\n\n    return this;\n  }\n\n  inverse() {\n    if (this._dirty) {\n      this._dirty = false;\n      if (!this.inverted) {\n        this.inverted = new Matrix();\n        // todo: link-back the inverted matrix\n        // todo: should the inverted be editable or readonly?\n      }\n      const z = this.a * this.d - this.b * this.c;\n      this.inverted.a = this.d / z;\n      this.inverted.b = -this.b / z;\n      this.inverted.c = -this.c / z;\n      this.inverted.d = this.a / z;\n      this.inverted.e = (this.c * this.f - this.e * this.d) / z;\n      this.inverted.f = (this.e * this.b - this.a * this.f) / z;\n    }\n    return this.inverted;\n  }\n\n  map(p: Vec2Value, q?: Vec2Value) {\n    q = q || { x: 0, y: 0 };\n    q.x = this.a * p.x + this.c * p.y + this.e;\n    q.y = this.b * p.x + this.d * p.y + this.f;\n    return q;\n  }\n\n  mapX(x: number | Vec2Value, y?: number) {\n    if (typeof x === \"object\") {\n      y = x.y;\n      x = x.x;\n    }\n    return this.a * x + this.c * y + this.e;\n  }\n\n  mapY(x: number | Vec2Value, y?: number) {\n    if (typeof x === \"object\") {\n      y = x.y;\n      x = x.x;\n    }\n    return this.b * x + this.d * y + this.f;\n  }\n}\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "/** @internal */\nconst objectToString = Object.prototype.toString;\n\n/** @internal */\nexport function isFn(value: any) {\n  const str = objectToString.call(value);\n  return (\n    str === \"[object Function]\" ||\n    str === \"[object GeneratorFunction]\" ||\n    str === \"[object AsyncFunction]\"\n  );\n}\n\n/** @internal */\nexport function isHash(value: any) {\n  return objectToString.call(value) === \"[object Object]\" && value.constructor === Object;\n  // return value && typeof value === 'object' && !Array.isArray(value) && !isFn(value);\n}\n", "/** @hidden */\nexport default {\n  create: 0,\n  tick: 0,\n  component: 0,\n  draw: 0,\n  fps: 0,\n};\n", "/** @internal */\nexport const uid = function () {\n  return Date.now().toString(36) + Math.random().toString(36).slice(2);\n};\n", "import { uid } from \"../common/uid\";\n\n/** @internal */\nexport interface TexturePrerenderContext {\n  pixelRatio: number;\n}\n\n// todo: unify 9-patch and padding?\n// todo: prerender is used to get texture width and height, replace it with a function to only get width and height\n\n// todo: add reusable shape textures\n\n/**\n * Textures are used to clip and resize image objects.\n */\nexport abstract class Texture {\n  /** @internal */ uid = \"texture:\" + uid();\n\n  /** @hidden */ sx = 0;\n  /** @hidden */ sy = 0;\n  /** @hidden */ sw: number;\n  /** @hidden */ sh: number;\n  /** @hidden */ dx = 0;\n  /** @hidden */ dy = 0;\n  /** @hidden */ dw: number;\n  /** @hidden */ dh: number;\n\n  // 9-patch resizing specification\n  // todo: rename to something more descriptive\n  /** @internal */ top: number;\n  /** @internal */ bottom: number;\n  /** @internal */ left: number;\n  /** @internal */ right: number;\n\n  // Geometrical values\n  setSourceCoordinate(x: number, y: number) {\n    this.sx = x;\n    this.sy = y;\n  }\n\n  // Geometrical values\n  setSourceDimension(w: number, h: number) {\n    this.sw = w;\n    this.sh = h;\n  }\n\n  // Geometrical values\n  setDestinationCoordinate(x: number, y: number) {\n    this.dx = x;\n    this.dy = y;\n  }\n\n  // Geometrical values\n  setDestinationDimension(w: number, h: number) {\n    this.dw = w;\n    this.dh = h;\n  }\n\n  abstract getWidth(): number;\n\n  abstract getHeight(): number;\n\n  /**\n   * @internal\n   * This is used by subclasses, such as CanvasTexture, to rerender offscreen texture if needed.\n   */\n  abstract prerender(context: TexturePrerenderContext): boolean;\n\n  /**\n   * Defer draw spec to texture config. This is used when a sprite draws its textures.\n   */\n  draw(context: CanvasRenderingContext2D): void;\n  /**\n   * This is probably unused.\n   * Note: dx, dy are added to this.dx, this.dy.\n   */\n  draw(context: CanvasRenderingContext2D, dx: number, dy: number, dw: number, dh: number): void;\n  /**\n   * This is used when a piped texture passes drawing to it backend.\n   * Note: sx, sy, dx, dy are added to this.sx, this.sy, this.dx, this.dy.\n   */\n  draw(\n    context: CanvasRenderingContext2D,\n    sx: number,\n    sy: number,\n    sw: number,\n    sh: number,\n    dx: number,\n    dy: number,\n    dw: number,\n    dh: number,\n  ): void;\n  draw(\n    context: CanvasRenderingContext2D,\n    x1?: number,\n    y1?: number,\n    w1?: number,\n    h1?: number,\n    x2?: number,\n    y2?: number,\n    w2?: number,\n    h2?: number,\n  ): void {\n    let sx: number, sy: number, sw: number, sh: number;\n    let dx: number, dy: number, dw: number, dh: number;\n\n    if (arguments.length > 5) {\n      // two sets of [x, y, w, h] arguments\n      sx = this.sx + x1;\n      sy = this.sy + y1;\n      sw = w1 ?? this.sw;\n      sh = h1 ?? this.sh;\n\n      dx = this.dx + x2;\n      dy = this.dy + y2;\n      dw = w2 ?? this.dw;\n      dh = h2 ?? this.dh;\n    } else if(arguments.length > 1) {\n      // one set of [x, y, w, h] arguments\n      sx = this.sx;\n      sy = this.sy;\n      sw = this.sw;\n      sh = this.sh;\n\n      dx = this.dx + x1;\n      dy = this.dy + y1;\n      dw = w1 ?? this.dw;\n      dh = h1 ?? this.dh;\n    } else {\n      // no additional arguments\n      sx = this.sx;\n      sy = this.sy;\n      sw = this.sw;\n      sh = this.sh;\n\n      dx = this.dx;\n      dy = this.dy;\n      dw = this.dw;\n      dh = this.dh;\n    }\n\n    this.drawWithNormalizedArgs(context, sx, sy, sw, sh, dx, dy, dw, dh);\n  }\n\n  /** @internal */\n  abstract drawWithNormalizedArgs(\n    context: CanvasRenderingContext2D,\n    sx: number,\n    sy: number,\n    sw: number,\n    sh: number,\n    dx: number,\n    dy: number,\n    dw: number,\n    dh: number,\n  ): void;\n}\n", "import stats from \"../common/stats\";\nimport { clamp } from \"../common/math\";\n\nimport { Texture, TexturePrerenderContext } from \"./texture\";\n\ntype TextureImageSource =\n  | HTMLImageElement\n  | HTMLVideoElement\n  | HTMLCanvasElement\n  | ImageBitmap\n  | OffscreenCanvas;\n\nexport class ImageTexture extends Texture {\n  /** @internal */ _source: TextureImageSource;\n\n  /** @internal */ _pixelRatio = 1;\n\n  /** @internal */ _draw_failed: boolean;\n\n  /** @internal */\n  private padding = 0;\n\n  constructor(source?: TextureImageSource, pixelRatio?: number) {\n    super();\n    if (typeof source === \"object\") {\n      this.setSourceImage(source, pixelRatio);\n    }\n  }\n\n  setSourceImage(image: TextureImageSource, pixelRatio = 1) {\n    this._source = image;\n    this._pixelRatio = pixelRatio;\n  }\n\n  /**\n   * Add padding to the image texture. Padding can be negative.\n   */\n  setPadding(padding: number): void {\n    this.padding = padding;\n  }\n\n  getWidth(): number {\n    return this._source.width / this._pixelRatio + (this.padding + this.padding);\n  }\n\n  getHeight(): number {\n    return this._source.height / this._pixelRatio + (this.padding + this.padding);\n  }\n\n  /** @internal */\n  prerender(context: TexturePrerenderContext): boolean {\n    return false;\n  }\n\n  /** @internal */\n  drawWithNormalizedArgs(\n    context: CanvasRenderingContext2D,\n    sx: number,\n    sy: number,\n    sw: number,\n    sh: number,\n    dx: number,\n    dy: number,\n    dw: number,\n    dh: number,\n  ): void {\n    const image = this._source;\n    if (image === null || typeof image !== \"object\") {\n      return;\n    }\n\n    sw = sw ?? this._source.width / this._pixelRatio;\n    sh = sh ?? this._source.height / this._pixelRatio;\n\n    dw = dw ?? sw;\n    dh = dh ?? sh;\n\n    dx += this.padding;\n    dy += this.padding;\n\n    const ix = sx * this._pixelRatio;\n    const iy = sy * this._pixelRatio;\n    const iw = sw * this._pixelRatio;\n    const ih = sh * this._pixelRatio;\n\n    try {\n      stats.draw++;\n\n      // sx = clamp(sx, 0, this._source.width);\n      // sy = clamp(sx, 0, this._source.height);\n      // sw = clamp(sw, 0, this._source.width - sw);\n      // sh = clamp(sh, 0, this._source.height - sh);\n\n      context.drawImage(image, ix, iy, iw, ih, dx, dy, dw, dh);\n    } catch (ex) {\n      if (!this._draw_failed) {\n        console.log(\"Unable to draw: \", image);\n        console.log(ex);\n        this._draw_failed = true;\n      }\n    }\n  }\n}\n", "import { Texture, TexturePrerenderContext } from \"./texture\";\n\nexport class PipeTexture extends Texture {\n  /** @internal */ _source: Texture;\n\n  constructor(source: Texture) {\n    super();\n    this._source = source;\n  }\n\n  setSourceTexture(texture: Texture) {\n    this._source = texture;\n  }\n\n  getWidth(): number {\n    return this.dw ?? this.sw ?? this._source.getWidth();\n  }\n\n  getHeight(): number {\n    return this.dh ?? this.sh ?? this._source.getHeight();\n  }\n\n  /** @internal */\n  prerender(context: TexturePrerenderContext): boolean {\n    return this._source.prerender(context);\n  }\n\n  /** @internal */\n  drawWithNormalizedArgs(\n    context: CanvasRenderingContext2D,\n    sx: number,\n    sy: number,\n    sw: number,\n    sh: number,\n    dx: number,\n    dy: number,\n    dw: number,\n    dh: number,\n  ): void {\n    const texture = this._source;\n    if (texture === null || typeof texture !== \"object\") {\n      return;\n    }\n\n    texture.draw(context, sx, sy, sw, sh, dx, dy, dw, dh);\n  }\n}\n", "import { isFn, isHash } from \"../common/is\";\n\nimport { Texture } from \"./texture\";\nimport { TextureSelection } from \"./selection\";\nimport { ImageTexture } from \"./image\";\nimport { PipeTexture } from \"./pipe\";\n\nexport interface AtlasTextureDefinition {\n  x: number;\n  y: number;\n  width: number;\n  height: number;\n\n  left?: number;\n  top?: number;\n  right?: number;\n  bottom?: number;\n}\n\ntype MonotypeAtlasTextureDefinition = Record<string, AtlasTextureDefinition | Texture | string>;\ntype AnimAtlasTextureDefinition = (AtlasTextureDefinition | Texture | string)[];\n\nexport interface AtlasDefinition {\n  name?: string;\n  image?:\n    | {\n        src: string;\n        ratio?: number;\n      }\n    | {\n        /** @deprecated Use src instead of url */\n        url: string;\n        ratio?: number;\n      };\n\n  ppu?: number;\n  textures?: Record<\n    string,\n    AtlasTextureDefinition | Texture | MonotypeAtlasTextureDefinition | AnimAtlasTextureDefinition\n  >;\n\n  map?: (texture: AtlasTextureDefinition) => AtlasTextureDefinition;\n\n  /** @deprecated Use map */\n  filter?: (texture: AtlasTextureDefinition) => AtlasTextureDefinition;\n\n  /** @deprecated */\n  trim?: number;\n  /** @deprecated Use ppu */\n  ratio?: number;\n\n  /** @deprecated Use map */\n  imagePath?: string;\n  /** @deprecated Use map */\n  imageRatio?: number;\n}\n\nexport class Atlas extends ImageTexture {\n  /** @internal */ name: any;\n\n  /** @internal */ _ppu: any;\n  /** @internal */ _trim: any;\n  /** @internal */ _map: any;\n  /** @internal */ _textures: any;\n  /** @internal */ _imageSrc: string;\n\n  constructor(def: AtlasDefinition = {}) {\n    super();\n\n    this.name = def.name;\n    this._ppu = def.ppu || def.ratio || 1;\n    this._trim = def.trim || 0;\n\n    this._map = def.map || def.filter;\n    this._textures = def.textures;\n\n    if (typeof def.image === \"object\" && isHash(def.image)) {\n      if (\"src\" in def.image) {\n        this._imageSrc = def.image.src;\n      } else if (\"url\" in def.image) {\n        this._imageSrc = def.image.url;\n      }\n      if (typeof def.image.ratio === \"number\") {\n        this._pixelRatio = def.image.ratio;\n      }\n    } else {\n      if (typeof def.imagePath === \"string\") {\n        this._imageSrc = def.imagePath;\n      } else if (typeof def.image === \"string\") {\n        this._imageSrc = def.image;\n      }\n      if (typeof def.imageRatio === \"number\") {\n        this._pixelRatio = def.imageRatio;\n      }\n    }\n\n    deprecatedWarning(def);\n  }\n\n  async load() {\n    if (this._imageSrc) {\n      const image = await asyncLoadImage(this._imageSrc);\n      this.setSourceImage(image, this._pixelRatio);\n    }\n  }\n\n  /**\n   * @internal\n   * Uses the definition to create a texture object from this atlas.\n   */\n  pipeSpriteTexture = (def: AtlasTextureDefinition): Texture => {\n    const map = this._map;\n    const ppu = this._ppu;\n    const trim = this._trim;\n\n    if (!def) {\n      return undefined;\n    }\n\n    def = Object.assign({}, def);\n\n    if (isFn(map)) {\n      def = map(def);\n    }\n\n    if (ppu != 1) {\n      def.x *= ppu;\n      def.y *= ppu;\n      def.width *= ppu;\n      def.height *= ppu;\n      def.top *= ppu;\n      def.bottom *= ppu;\n      def.left *= ppu;\n      def.right *= ppu;\n    }\n\n    if (trim != 0) {\n      def.x += trim;\n      def.y += trim;\n      def.width -= 2 * trim;\n      def.height -= 2 * trim;\n      def.top -= trim;\n      def.bottom -= trim;\n      def.left -= trim;\n      def.right -= trim;\n    }\n\n    const texture = new PipeTexture(this);\n    texture.top = def.top;\n    texture.bottom = def.bottom;\n    texture.left = def.left;\n    texture.right = def.right;\n    texture.setSourceCoordinate(def.x, def.y);\n    texture.setSourceDimension(def.width, def.height);\n    return texture;\n  };\n\n  /**\n   * @internal\n   * Looks up and returns texture definition.\n   */\n  findSpriteDefinition = (query: string) => {\n    const textures = this._textures;\n\n    if (textures) {\n      if (isFn(textures)) {\n        return textures(query);\n      } else if (isHash(textures)) {\n        return textures[query];\n      }\n    }\n  };\n\n  // returns Selection, and then selection.one/array returns actual texture/textures\n  select = (query?: string) => {\n    if (!query) {\n      // TODO: if `textures` is texture def, map or fn?\n      return new TextureSelection(new PipeTexture(this));\n    }\n    const textureDefinition = this.findSpriteDefinition(query);\n    if (textureDefinition) {\n      return new TextureSelection(textureDefinition, this);\n    }\n  };\n}\n\n/** @internal */\nfunction asyncLoadImage(src: string) {\n  console.debug && console.debug(\"Loading image: \" + src);\n  return new Promise<HTMLImageElement>(function (resolve, reject) {\n    const img = new Image();\n    img.onload = function () {\n      console.debug && console.debug(\"Image loaded: \" + src);\n      resolve(img);\n    };\n    img.onerror = function (error) {\n      console.error(\"Loading failed: \" + src);\n      reject(error);\n    };\n    img.src = src;\n  });\n}\n\n/** @internal */\nfunction deprecatedWarning(def: AtlasDefinition) {\n  if (\"filter\" in def) console.warn(\"'filter' field of atlas definition is deprecated\");\n\n  // todo: throw error here?\n  if (\"cutouts\" in def) console.warn(\"'cutouts' field of atlas definition is deprecated\");\n\n  // todo: throw error here?\n  if (\"sprites\" in def) console.warn(\"'sprites' field of atlas definition is deprecated\");\n\n  // todo: throw error here?\n  if (\"factory\" in def) console.warn(\"'factory' field of atlas definition is deprecated\");\n\n  if (\"ratio\" in def) console.warn(\"'ratio' field of atlas definition is deprecated\");\n\n  if (\"imagePath\" in def) console.warn(\"'imagePath' field of atlas definition is deprecated\");\n\n  if (\"imageRatio\" in def) console.warn(\"'imageRatio' field of atlas definition is deprecated\");\n\n  if (typeof def.image === \"object\" && \"url\" in def.image)\n    console.warn(\"'image.url' field of atlas definition is deprecated\");\n}\n", "import { isFn, isHash } from \"../common/is\";\n\nimport { Atlas, AtlasDefinition, AtlasTextureDefinition } from \"./atlas\";\nimport { Texture, TexturePrerenderContext } from \"./texture\";\n\nexport type TextureSelectionInputOne = Texture | AtlasTextureDefinition | string;\nexport type TextureSelectionInputMap = Record<string, TextureSelectionInputOne>;\nexport type TextureSelectionInputArray = TextureSelectionInputOne[];\nexport type TextureSelectionInputFactory = (subquery: string) => TextureSelectionInputOne;\n\n/**\n * Texture selection input could be one:\n * - texture\n * - sprite definition (and an atlas): atlas sprite texture\n * - string (with an atlas): string used as key to find sprite in the atlas, re-resolve\n * - hash object: use subquery as key, then re-resolve value\n * - array: re-resolve first item\n * - function: call function with subquery, then re-resolve\n */\nexport type TextureSelectionInput =\n  | TextureSelectionInputOne\n  | TextureSelectionInputMap\n  | TextureSelectionInputArray\n  | TextureSelectionInputFactory;\n\n/** @internal */\nfunction isAtlasSpriteDefinition(selection: any) {\n  return (\n    typeof selection === \"object\" &&\n    isHash(selection) &&\n    \"number\" === typeof selection.width &&\n    \"number\" === typeof selection.height\n  );\n}\n\n/**\n * TextureSelection holds reference to one or many textures or something that\n * can be resolved to one or many textures. This is used to decouple resolving\n * references to textures from rendering them in various ways.\n */\nexport class TextureSelection {\n  selection: TextureSelectionInput;\n  atlas: Atlas;\n  constructor(selection: TextureSelectionInput, atlas?: Atlas) {\n    this.selection = selection;\n    this.atlas = atlas;\n  }\n\n  /**\n   * @internal\n   * Resolves the selection to a texture.\n   */\n  resolve(selection: TextureSelectionInput, subquery?: string): Texture {\n    if (!selection) {\n      return NO_TEXTURE;\n    } else if (Array.isArray(selection)) {\n      return this.resolve(selection[0]);\n    } else if (selection instanceof Texture) {\n      return selection;\n    } else if (isAtlasSpriteDefinition(selection)) {\n      if (!this.atlas) {\n        return NO_TEXTURE;\n      }\n      return this.atlas.pipeSpriteTexture(selection as AtlasTextureDefinition);\n    } else if (\n      typeof selection === \"object\" &&\n      isHash(selection) &&\n      typeof subquery !== \"undefined\"\n    ) {\n      return this.resolve(selection[subquery]);\n    } else if (typeof selection === \"function\" && isFn(selection)) {\n      return this.resolve(selection(subquery));\n    } else if (typeof selection === \"string\") {\n      if (!this.atlas) {\n        return NO_TEXTURE;\n      }\n      return this.resolve(this.atlas.findSpriteDefinition(selection));\n    }\n  }\n\n  one(subquery?: string): Texture {\n    return this.resolve(this.selection, subquery);\n  }\n\n  array(arr?: Texture[]): Texture[] {\n    const array = Array.isArray(arr) ? arr : [];\n    if (Array.isArray(this.selection)) {\n      for (let i = 0; i < this.selection.length; i++) {\n        array[i] = this.resolve(this.selection[i]);\n      }\n    } else {\n      array[0] = this.resolve(this.selection);\n    }\n    return array;\n  }\n}\n\n/** @internal */\nconst NO_TEXTURE = new (class extends Texture {\n  getWidth(): number {\n    return 0;\n  }\n  getHeight(): number {\n    return 0;\n  }\n  prerender(context: TexturePrerenderContext): boolean {\n    return false;\n  }\n  drawWithNormalizedArgs(\n    context: CanvasRenderingContext2D,\n    sx: number,\n    sy: number,\n    sw: number,\n    sh: number,\n    dx: number,\n    dy: number,\n    dw: number,\n    dh: number,\n  ): void {}\n  constructor() {\n    super();\n    this.setSourceDimension(0, 0);\n  }\n  setSourceCoordinate(x: any, y: any): void {}\n  setSourceDimension(w: any, h: any): void {}\n  setDestinationCoordinate(x: number, y: number): void {}\n  setDestinationDimension(w: number, h: number): void {}\n  draw(): void {}\n})();\n\n/** @internal */\nconst NO_SELECTION = new TextureSelection(NO_TEXTURE);\n\n/** @internal */\nconst ATLAS_MEMO_BY_NAME: Record<string, Atlas> = {};\n\n/** @internal */\nconst ATLAS_ARRAY: Atlas[] = [];\n\n// TODO: print subquery not found error\n// TODO: index textures\n\nexport async function atlas(def: AtlasDefinition | Atlas): Promise<Atlas> {\n  // todo: where is this used?\n  let atlas: Atlas;\n  if (def instanceof Atlas) {\n    atlas = def;\n  } else {\n    atlas = new Atlas(def);\n  }\n\n  if (atlas.name) {\n    ATLAS_MEMO_BY_NAME[atlas.name] = atlas;\n  }\n  ATLAS_ARRAY.push(atlas);\n\n  await atlas.load();\n\n  return atlas;\n}\n\n/**\n * When query argument is string, this function parses the query; looks up registered atlases; and returns a texture selection object.\n *\n * When query argument is an object, the object is used to create a new selection.\n */\nexport function texture(query: string | TextureSelectionInput): TextureSelection {\n  if (\"string\" !== typeof query) {\n    return new TextureSelection(query);\n  }\n\n  let result: TextureSelection | undefined | null = null;\n\n  // parse query as atlas-name:texture-name\n  const colonIndex = query.indexOf(\":\");\n  if (colonIndex > 0 && query.length > colonIndex + 1) {\n    const atlas = ATLAS_MEMO_BY_NAME[query.slice(0, colonIndex)];\n    result = atlas && atlas.select(query.slice(colonIndex + 1));\n  }\n\n  if (!result) {\n    // use query as \"atlas-name\", return entire atlas\n    const atlas = ATLAS_MEMO_BY_NAME[query];\n    result = atlas && atlas.select();\n  }\n\n  if (!result) {\n    // use query as \"texture-name\", search over all atlases\n    for (let i = 0; i < ATLAS_ARRAY.length; i++) {\n      result = ATLAS_ARRAY[i].select(query);\n      if (result) {\n        break;\n      }\n    }\n  }\n\n  if (!result) {\n    console.error(\"Texture not found: \" + query);\n    result = NO_SELECTION;\n  }\n\n  return result;\n}\n", "import { Texture, TexturePrerenderContext } from \"./texture\";\n\nexport type ResizableTextureMode = \"stretch\" | \"tile\";\n\nexport class ResizableTexture extends Texture {\n  /** @internal */ _source: Texture;\n\n  /** @internal */ _resizeMode: ResizableTextureMode;\n  /** @internal */ _innerSize: boolean;\n\n  constructor(source: Texture, mode: ResizableTextureMode) {\n    super();\n    this._source = source;\n    this._resizeMode = mode;\n  }\n\n  getWidth(): number {\n    // this is the last known width\n    return this.dw ?? this._source.getWidth();\n  }\n\n  getHeight(): number {\n    // this is the last known height\n    return this.dh ?? this._source.getHeight();\n  }\n\n  /** @internal */\n  prerender(context: TexturePrerenderContext): boolean {\n    return false;\n  }\n\n  drawWithNormalizedArgs(\n    context: CanvasRenderingContext2D,\n    sx: number,\n    sy: number,\n    sw: number,\n    sh: number,\n    dx: number,\n    dy: number,\n    dw: number,\n    dh: number,\n  ): void {\n    const texture = this._source;\n    if (texture === null || typeof texture !== \"object\") {\n      return;\n    }\n\n    let outWidth = dw;\n    let outHeight = dh;\n\n    const left = Number.isFinite(texture.left) ? texture.left : 0;\n    const right = Number.isFinite(texture.right) ? texture.right : 0;\n    const top = Number.isFinite(texture.top) ? texture.top : 0;\n    const bottom = Number.isFinite(texture.bottom) ? texture.bottom : 0;\n\n    const width = texture.getWidth() - left - right;\n    const height = texture.getHeight() - top - bottom;\n\n    if (!this._innerSize) {\n      outWidth = Math.max(outWidth - left - right, 0);\n      outHeight = Math.max(outHeight - top - bottom, 0);\n    }\n\n    // corners\n    if (top > 0 && left > 0) {\n      texture.draw(context, 0, 0, left, top, 0, 0, left, top);\n    }\n    if (bottom > 0 && left > 0) {\n      texture.draw(context, 0, height + top, left, bottom, 0, outHeight + top, left, bottom);\n    }\n    if (top > 0 && right > 0) {\n      texture.draw(context, width + left, 0, right, top, outWidth + left, 0, right, top);\n    }\n    if (bottom > 0 && right > 0) {\n      texture.draw(\n        context,\n        width + left,\n        height + top,\n        right,\n        bottom,\n        outWidth + left,\n        outHeight + top,\n        right,\n        bottom,\n      );\n    }\n\n    if (this._resizeMode === \"stretch\") {\n      // sides\n      if (top > 0) {\n        texture.draw(context, left, 0, width, top, left, 0, outWidth, top);\n      }\n      if (bottom > 0) {\n        texture.draw(\n          context,\n          left,\n          height + top,\n          width,\n          bottom,\n          left,\n          outHeight + top,\n          outWidth,\n          bottom,\n        );\n      }\n      if (left > 0) {\n        texture.draw(context, 0, top, left, height, 0, top, left, outHeight);\n      }\n      if (right > 0) {\n        texture.draw(\n          context,\n          width + left,\n          top,\n          right,\n          height,\n          outWidth + left,\n          top,\n          right,\n          outHeight,\n        );\n      }\n      // center\n      texture.draw(context, left, top, width, height, left, top, outWidth, outHeight);\n    } else if (this._resizeMode === \"tile\") {\n      // tile\n      let l = left;\n      let r = outWidth;\n      let w: number;\n      while (r > 0) {\n        w = Math.min(width, r);\n        r -= width;\n        let t = top;\n        let b = outHeight;\n        let h: number;\n        while (b > 0) {\n          h = Math.min(height, b);\n          b -= height;\n          texture.draw(context, left, top, w, h, l, t, w, h);\n          if (r <= 0) {\n            if (left) {\n              texture.draw(context, 0, top, left, h, 0, t, left, h);\n            }\n            if (right) {\n              texture.draw(context, width + left, top, right, h, l + w, t, right, h);\n            }\n          }\n          t += h;\n        }\n        if (top) {\n          texture.draw(context, left, 0, w, top, l, 0, w, top);\n        }\n        if (bottom) {\n          texture.draw(context, left, height + top, w, bottom, l, t, w, bottom);\n        }\n        l += w;\n      }\n    }\n  }\n}\n", "/** @internal */\nexport function getDevicePixelRatio() {\n  // todo: do we need to divide by backingStoreRatio?\n  return typeof window !== \"undefined\" ? window.devicePixelRatio || 1 : 1;\n}\n", "import { Matrix, Vec2Value } from \"../common/matrix\";\nimport { uid } from \"../common/uid\";\n\nimport type { Component } from \"./component\";\n\n/**\n *  @hidden @deprecated\n * - 'in-pad': same as 'contain'\n * - 'in': similar to 'contain' without centering\n * - 'out-crop': same as 'cover'\n * - 'out': similar to 'cover' without centering\n */\nexport type LegacyFitMode = \"in\" | \"out\" | \"out-crop\" | \"in-pad\";\n\n/**\n * - 'contain': contain within the provided space, maintain aspect ratio\n * - 'cover': cover the provided space, maintain aspect ratio\n * - 'fill': fill provided space without maintaining aspect ratio\n */\nexport type FitMode = \"contain\" | \"cover\" | \"fill\" | LegacyFitMode;\n\n/** @internal */\nexport function isValidFitMode(value: string) {\n  return (\n    value &&\n    (value === \"cover\" ||\n      value === \"contain\" ||\n      value === \"fill\" ||\n      value === \"in\" ||\n      value === \"in-pad\" ||\n      value === \"out\" ||\n      value === \"out-crop\")\n  );\n}\n\n/** @internal */ let iid = 0;\n\n/** @hidden */\nexport interface Pinned {\n  pin(pin: object): this;\n  pin(key: string, value: any): this;\n  pin(key: string): any;\n\n  size(w: number, h: number): this;\n\n  width(): number;\n  width(w: number): this;\n\n  height(): number;\n  height(h: number): this;\n\n  offset(a: Vec2Value): this;\n  offset(a: number, b: number): this;\n\n  rotate(a: number): this;\n\n  skew(a: Vec2Value): this;\n  skew(a: number, b: number): this;\n\n  scale(a: Vec2Value): this;\n  scale(a: number, b: number): this;\n\n  alpha(a: number, ta?: number): this;\n}\n\nexport class Pin {\n  /** @internal */ uid = \"pin:\" + uid();\n\n  /** @internal */ _owner: Component;\n\n  // todo: maybe this should be a getter instead?\n  /** @internal */ _parent: Pin | null;\n\n  /** @internal */ _relativeMatrix: Matrix;\n  /** @internal */ _absoluteMatrix: Matrix;\n\n  /** @internal */ _x: number;\n  /** @internal */ _y: number;\n\n  /** @internal */ _unscaled_width: number;\n  /** @internal */ _unscaled_height: number;\n\n  /** @internal */ _width: number;\n  /** @internal */ _height: number;\n\n  /** @internal */ _textureAlpha: number;\n  /** @internal */ _alpha: number;\n\n  /** @internal */ _scaleX: number;\n  /** @internal */ _scaleY: number;\n\n  /** @internal */ _skewX: number;\n  /** @internal */ _skewY: number;\n  /** @internal */ _rotation: number;\n\n  /** @internal */ _pivoted: boolean;\n  /** @internal */ _pivotX: number;\n  /** @internal */ _pivotY: number;\n\n  /** @internal */ _handled: boolean;\n  /** @internal */ _handleX: number;\n  /** @internal */ _handleY: number;\n\n  /** @internal */ _aligned: boolean;\n  /** @internal */ _alignX: number;\n  /** @internal */ _alignY: number;\n\n  /** @internal */ _offsetX: number;\n  /** @internal */ _offsetY: number;\n\n  /** @internal */ _boxX: number;\n  /** @internal */ _boxY: number;\n  /** @internal */ _boxWidth: number;\n  /** @internal */ _boxHeight: number;\n\n  /** @internal */ _ts_transform: number;\n  /** @internal */ _ts_translate: number;\n  /** @internal */ _ts_matrix: number;\n\n  /** @internal */ _mo_handle: number;\n  /** @internal */ _mo_align: number;\n  /** @internal */ _mo_abs: number;\n  /** @internal */ _mo_rel: number;\n\n  /** @internal */ _directionX = 1;\n  /** @internal */ _directionY = 1;\n\n  /** @internal */\n  constructor(owner: Component) {\n    this._owner = owner;\n    this._parent = null;\n\n    // relative to parent\n    this._relativeMatrix = new Matrix();\n\n    // relative to stage\n    this._absoluteMatrix = new Matrix();\n\n    this.reset();\n  }\n\n  reset() {\n    this._textureAlpha = 1;\n    this._alpha = 1;\n\n    this._width = 0;\n    this._height = 0;\n\n    this._scaleX = 1;\n    this._scaleY = 1;\n    this._skewX = 0;\n    this._skewY = 0;\n    this._rotation = 0;\n\n    // scale/skew/rotate center\n    this._pivoted = false;\n    // todo: this used to be null\n    this._pivotX = 0;\n    this._pivotY = 0;\n\n    // self pin point\n    this._handled = false;\n    this._handleX = 0;\n    this._handleY = 0;\n\n    // parent pin point\n    this._aligned = false;\n    this._alignX = 0;\n    this._alignY = 0;\n\n    // as seen by parent px\n    this._offsetX = 0;\n    this._offsetY = 0;\n\n    this._boxX = 0;\n    this._boxY = 0;\n    this._boxWidth = this._width;\n    this._boxHeight = this._height;\n\n    // TODO: also set for owner\n    this._ts_translate = ++iid;\n    this._ts_transform = ++iid;\n    this._ts_matrix = ++iid;\n  }\n\n  /** @internal */\n  _update() {\n    this._parent = this._owner._parent && this._owner._parent._pin;\n\n    // if handled and transformed then be translated\n    if (this._handled && this._mo_handle != this._ts_transform) {\n      this._mo_handle = this._ts_transform;\n      this._ts_translate = ++iid;\n    }\n\n    if (this._aligned && this._parent && this._mo_align != this._parent._ts_transform) {\n      this._mo_align = this._parent._ts_transform;\n      this._ts_translate = ++iid;\n    }\n\n    return this;\n  }\n\n  toString() {\n    return this._owner + \" (\" + (this._parent ? this._parent._owner : null) + \")\";\n  }\n\n  // TODO: ts fields require refactoring\n  absoluteMatrix() {\n    this._update();\n    const ts = Math.max(\n      this._ts_transform,\n      this._ts_translate,\n      this._parent ? this._parent._ts_matrix : 0,\n    );\n    if (this._mo_abs == ts) {\n      return this._absoluteMatrix;\n    }\n    this._mo_abs = ts;\n\n    const abs = this._absoluteMatrix;\n    abs.reset(this.relativeMatrix());\n\n    this._parent && abs.concat(this._parent._absoluteMatrix);\n\n    this._ts_matrix = ++iid;\n\n    return abs;\n  }\n\n  relativeMatrix() {\n    this._update();\n    const ts = Math.max(\n      this._ts_transform,\n      this._ts_translate,\n      this._parent ? this._parent._ts_transform : 0,\n    );\n    if (this._mo_rel == ts) {\n      return this._relativeMatrix;\n    }\n    this._mo_rel = ts;\n\n    const rel = this._relativeMatrix;\n\n    rel.identity();\n    if (this._pivoted) {\n      rel.translate(-this._pivotX * this._width, -this._pivotY * this._height);\n    }\n    rel.scale(this._scaleX * this._directionX, this._scaleY * this._directionY);\n    rel.skew(this._skewX, this._skewY);\n    rel.rotate(this._rotation);\n    if (this._pivoted) {\n      rel.translate(this._pivotX * this._width, this._pivotY * this._height);\n    }\n\n    // calculate effective box\n    if (this._pivoted) {\n      // origin\n      this._boxX = 0;\n      this._boxY = 0;\n      this._boxWidth = this._width;\n      this._boxHeight = this._height;\n    } else {\n      // aabb\n      let p;\n      let q;\n      if ((rel.a > 0 && rel.c > 0) || (rel.a < 0 && rel.c < 0)) {\n        p = 0;\n        q = rel.a * this._width + rel.c * this._height;\n      } else {\n        p = rel.a * this._width;\n        q = rel.c * this._height;\n      }\n      if (p > q) {\n        this._boxX = q;\n        this._boxWidth = p - q;\n      } else {\n        this._boxX = p;\n        this._boxWidth = q - p;\n      }\n      if ((rel.b > 0 && rel.d > 0) || (rel.b < 0 && rel.d < 0)) {\n        p = 0;\n        q = rel.b * this._width + rel.d * this._height;\n      } else {\n        p = rel.b * this._width;\n        q = rel.d * this._height;\n      }\n      if (p > q) {\n        this._boxY = q;\n        this._boxHeight = p - q;\n      } else {\n        this._boxY = p;\n        this._boxHeight = q - p;\n      }\n    }\n\n    this._x = this._offsetX;\n    this._y = this._offsetY;\n\n    this._x -= this._boxX + this._handleX * this._boxWidth * this._directionX;\n    this._y -= this._boxY + this._handleY * this._boxHeight * this._directionY;\n\n    if (this._aligned && this._parent) {\n      this._parent.relativeMatrix();\n      this._x += this._alignX * this._parent._width;\n      this._y += this._alignY * this._parent._height;\n    }\n\n    rel.translate(this._x, this._y);\n\n    return this._relativeMatrix;\n  }\n\n  /** @internal */\n  get(key: string) {\n    if (typeof getters[key] === \"function\") {\n      return getters[key](this);\n    }\n  }\n\n  // TODO: Use defineProperty instead? What about multi-field pinning?\n  /** @internal */\n  set(a, b?) {\n    if (typeof a === \"string\") {\n      if (typeof setters[a] === \"function\" && typeof b !== \"undefined\") {\n        setters[a](this, b);\n      }\n    } else if (typeof a === \"object\") {\n      for (b in a) {\n        if (typeof setters[b] === \"function\" && typeof a[b] !== \"undefined\") {\n          setters[b](this, a[b], a);\n        }\n      }\n    }\n    if (this._owner) {\n      this._owner._ts_pin = ++iid;\n      this._owner.touch();\n    }\n    return this;\n  }\n\n  // todo: should this be public?\n  /** @internal */\n  fit(width: number | null, height: number | null, mode?: FitMode) {\n    this._ts_transform = ++iid;\n    if (mode === \"contain\") {\n      mode = \"in-pad\";\n    }\n    if (mode === \"cover\") {\n      mode = \"out-crop\";\n    }\n    if (typeof width === \"number\") {\n      this._scaleX = width / this._unscaled_width;\n      this._width = this._unscaled_width;\n    }\n    if (typeof height === \"number\") {\n      this._scaleY = height / this._unscaled_height;\n      this._height = this._unscaled_height;\n    }\n    if (typeof width === \"number\" && typeof height === \"number\" && typeof mode === \"string\") {\n      if (mode === \"fill\") {\n      } else if (mode === \"out\" || mode === \"out-crop\") {\n        this._scaleX = this._scaleY = Math.max(this._scaleX, this._scaleY);\n      } else if (mode === \"in\" || mode === \"in-pad\") {\n        this._scaleX = this._scaleY = Math.min(this._scaleX, this._scaleY);\n      }\n      if (mode === \"out-crop\" || mode === \"in-pad\") {\n        this._width = width / this._scaleX;\n        this._height = height / this._scaleY;\n      }\n    }\n  }\n}\n\n/** @internal */ const getters = {\n  alpha: function (pin: Pin) {\n    return pin._alpha;\n  },\n\n  textureAlpha: function (pin: Pin) {\n    return pin._textureAlpha;\n  },\n\n  width: function (pin: Pin) {\n    return pin._width;\n  },\n\n  height: function (pin: Pin) {\n    return pin._height;\n  },\n\n  boxWidth: function (pin: Pin) {\n    return pin._boxWidth;\n  },\n\n  boxHeight: function (pin: Pin) {\n    return pin._boxHeight;\n  },\n\n  // scale : function(pin: Pin) {\n  // },\n\n  scaleX: function (pin: Pin) {\n    return pin._scaleX;\n  },\n\n  scaleY: function (pin: Pin) {\n    return pin._scaleY;\n  },\n\n  // skew : function(pin: Pin) {\n  // },\n\n  skewX: function (pin: Pin) {\n    return pin._skewX;\n  },\n\n  skewY: function (pin: Pin) {\n    return pin._skewY;\n  },\n\n  rotation: function (pin: Pin) {\n    return pin._rotation;\n  },\n\n  // pivot : function(pin: Pin) {\n  // },\n\n  pivotX: function (pin: Pin) {\n    return pin._pivotX;\n  },\n\n  pivotY: function (pin: Pin) {\n    return pin._pivotY;\n  },\n\n  // offset : function(pin: Pin) {\n  // },\n\n  offsetX: function (pin: Pin) {\n    return pin._offsetX;\n  },\n\n  offsetY: function (pin: Pin) {\n    return pin._offsetY;\n  },\n\n  // align : function(pin: Pin) {\n  // },\n\n  alignX: function (pin: Pin) {\n    return pin._alignX;\n  },\n\n  alignY: function (pin: Pin) {\n    return pin._alignY;\n  },\n\n  // handle : function(pin: Pin) {\n  // },\n\n  handleX: function (pin: Pin) {\n    return pin._handleX;\n  },\n\n  handleY: function (pin: Pin) {\n    return pin._handleY;\n  },\n};\n\ntype ResizeParams = {\n  resizeMode: FitMode;\n  resizeWidth: number;\n  resizeHeight: number;\n};\n\ntype ScaleParams = {\n  scaleMode: FitMode;\n  scaleWidth: number;\n  scaleHeight: number;\n};\n\n/** @internal */ const setters = {\n  alpha: function (pin: Pin, value: number) {\n    pin._alpha = value;\n  },\n\n  textureAlpha: function (pin: Pin, value: number) {\n    pin._textureAlpha = value;\n  },\n\n  width: function (pin: Pin, value: number) {\n    pin._unscaled_width = value;\n    pin._width = value;\n    pin._ts_transform = ++iid;\n  },\n\n  height: function (pin: Pin, value: number) {\n    pin._unscaled_height = value;\n    pin._height = value;\n    pin._ts_transform = ++iid;\n  },\n\n  scale: function (pin: Pin, value: number) {\n    pin._scaleX = value;\n    pin._scaleY = value;\n    pin._ts_transform = ++iid;\n  },\n\n  scaleX: function (pin: Pin, value: number) {\n    pin._scaleX = value;\n    pin._ts_transform = ++iid;\n  },\n\n  scaleY: function (pin: Pin, value: number) {\n    pin._scaleY = value;\n    pin._ts_transform = ++iid;\n  },\n\n  skew: function (pin: Pin, value: number) {\n    pin._skewX = value;\n    pin._skewY = value;\n    pin._ts_transform = ++iid;\n  },\n\n  skewX: function (pin: Pin, value: number) {\n    pin._skewX = value;\n    pin._ts_transform = ++iid;\n  },\n\n  skewY: function (pin: Pin, value: number) {\n    pin._skewY = value;\n    pin._ts_transform = ++iid;\n  },\n\n  rotation: function (pin: Pin, value: number) {\n    pin._rotation = value;\n    pin._ts_transform = ++iid;\n  },\n\n  pivot: function (pin: Pin, value: number) {\n    pin._pivotX = value;\n    pin._pivotY = value;\n    pin._pivoted = true;\n    pin._ts_transform = ++iid;\n  },\n\n  pivotX: function (pin: Pin, value: number) {\n    pin._pivotX = value;\n    pin._pivoted = true;\n    pin._ts_transform = ++iid;\n  },\n\n  pivotY: function (pin: Pin, value: number) {\n    pin._pivotY = value;\n    pin._pivoted = true;\n    pin._ts_transform = ++iid;\n  },\n\n  offset: function (pin: Pin, value: number) {\n    pin._offsetX = value;\n    pin._offsetY = value;\n    pin._ts_translate = ++iid;\n  },\n\n  offsetX: function (pin: Pin, value: number) {\n    pin._offsetX = value;\n    pin._ts_translate = ++iid;\n  },\n\n  offsetY: function (pin: Pin, value: number) {\n    pin._offsetY = value;\n    pin._ts_translate = ++iid;\n  },\n\n  align: function (pin: Pin, value: number) {\n    this.alignX(pin, value);\n    this.alignY(pin, value);\n  },\n\n  alignX: function (pin: Pin, value: number) {\n    pin._alignX = value;\n    pin._aligned = true;\n    pin._ts_translate = ++iid;\n\n    this.handleX(pin, value);\n  },\n\n  alignY: function (pin: Pin, value: number) {\n    pin._alignY = value;\n    pin._aligned = true;\n    pin._ts_translate = ++iid;\n\n    this.handleY(pin, value);\n  },\n\n  handle: function (pin: Pin, value: number) {\n    this.handleX(pin, value);\n    this.handleY(pin, value);\n  },\n\n  handleX: function (pin: Pin, value: number) {\n    pin._handleX = value;\n    pin._handled = true;\n    pin._ts_translate = ++iid;\n  },\n\n  handleY: function (pin: Pin, value: number) {\n    pin._handleY = value;\n    pin._handled = true;\n    pin._ts_translate = ++iid;\n  },\n\n  resizeMode: function (pin: Pin, value: FitMode, all: ResizeParams) {\n    if (all) {\n      if (value == \"in\") {\n        value = \"in-pad\";\n      } else if (value == \"out\") {\n        value = \"out-crop\";\n      }\n      pin.fit(all.resizeWidth, all.resizeHeight, value);\n    }\n  },\n\n  resizeWidth: function (pin: Pin, value: number, all: ResizeParams) {\n    if (!all || !all.resizeMode) {\n      pin.fit(value, null);\n    }\n  },\n\n  resizeHeight: function (pin: Pin, value: number, all: ResizeParams) {\n    if (!all || !all.resizeMode) {\n      pin.fit(null, value);\n    }\n  },\n\n  scaleMode: function (pin: Pin, value: FitMode, all: ScaleParams) {\n    if (all) {\n      pin.fit(all.scaleWidth, all.scaleHeight, value);\n    }\n  },\n\n  scaleWidth: function (pin: Pin, value: number, all: ScaleParams) {\n    if (!all || !all.scaleMode) {\n      pin.fit(value, null);\n    }\n  },\n\n  scaleHeight: function (pin: Pin, value: number, all: ScaleParams) {\n    if (!all || !all.scaleMode) {\n      pin.fit(null, value);\n    }\n  },\n\n  matrix: function (pin: Pin, value: Matrix) {\n    this.scaleX(pin, value.a);\n    this.skewX(pin, value.c / value.d);\n    this.skewY(pin, value.b / value.a);\n    this.scaleY(pin, value.d);\n    this.offsetX(pin, value.e);\n    this.offsetY(pin, value.f);\n    this.rotation(pin, 0);\n  },\n};\n", "/** @internal */\nfunction IDENTITY(x: any) {\n  return x;\n}\n\n/**\n * Easing function formats are:\n * - [name]\n * - [name\\]([params])\n * - [name]-[mode]\n * - [name]-[mode\\]([params])\n *\n * Easing function names are 'linear', 'quad', 'cubic', 'quart', 'quint', 'sin' (or 'sine'), 'exp' (or 'expo'), 'circle' (or 'circ'), 'bounce', 'poly', 'elastic', 'back'.\n *\n * Easing modes are 'in', 'out', 'in-out', 'out-in'.\n *\n * For example, 'linear', 'cubic-in', and 'poly(2)'.\n */\nexport type EasingFunctionName = string;\n\nexport type EasingFunction = (p: number) => number;\n/** @internal */\ntype EasingFunctionFactory = (...paras: any[]) => EasingFunction;\n\n/** @internal */\ntype EasingMode = (f: EasingFunction) => EasingFunction;\n\n/** @internal */\ntype EasingType =\n  | {\n      name: string;\n      fn: EasingFunction;\n    }\n  | {\n      name: string;\n      fc: EasingFunctionFactory;\n    };\n\n/** @internal */ const LOOKUP_CACHE: Record<string, EasingFunction> = {};\n/** @internal */ const MODE_BY_NAME: Record<string, EasingMode> = {};\n\n// split this to functions and factories\n/** @internal */ const EASE_BY_NAME: Record<string, EasingType> = {};\n\n// todo: make easing names and list statics?\n// todo: pass additional params as ...rest, instead of factories/curring? (`fc`)\n// todo: simplify add functions as (name, fn)?\n\nexport class Easing {\n  static get(\n    token: EasingFunctionName | EasingFunction,\n    fallback?: EasingFunction,\n  ): EasingFunction {\n    fallback = fallback || IDENTITY;\n    if (typeof token === \"function\") {\n      return token;\n    }\n    if (typeof token !== \"string\") {\n      return fallback;\n    }\n    let easeFn = LOOKUP_CACHE[token];\n    if (easeFn) {\n      return easeFn;\n    }\n    const tokens = /^(\\w+)(-(in|out|in-out|out-in))?(\\((.*)\\))?$/i.exec(token);\n    if (!tokens || !tokens.length) {\n      return fallback;\n    }\n\n    const easeName = tokens[1];\n    const easing = EASE_BY_NAME[easeName];\n\n    const modeName = tokens[3];\n    const modeFn = MODE_BY_NAME[modeName];\n\n    const params = tokens[5];\n\n    if (!easing) {\n      easeFn = fallback;\n    } else if (\"fn\" in easing && typeof easing.fn === \"function\") {\n      easeFn = easing.fn;\n    } else if (\"fc\" in easing && typeof easing.fc === \"function\") {\n      const args = params ? params.replace(/\\s+/, \"\").split(\",\") : undefined;\n      easeFn = easing.fc.apply(easing.fc, args);\n    } else {\n      easeFn = fallback;\n    }\n\n    if (modeFn) {\n      easeFn = modeFn(easeFn);\n    }\n    // TODO: It can be a memory leak with different `params`.\n    LOOKUP_CACHE[token] = easeFn;\n\n    return easeFn;\n  }\n}\n\n/** @internal */\nfunction addMode(name: string, fn: EasingMode) {\n  MODE_BY_NAME[name] = fn;\n}\n\n/** @internal */\nfunction addEaseFn(data: EasingType) {\n  const names = data.name.split(/\\s+/);\n  for (let i = 0; i < names.length; i++) {\n    const key = names[i];\n    if (key) {\n      EASE_BY_NAME[key] = data;\n    }\n  }\n}\n\naddMode(\"in\", function (f: EasingFunction) {\n  return f;\n});\n\naddMode(\"out\", function (f: EasingFunction) {\n  return function (t: number) {\n    return 1 - f(1 - t);\n  };\n});\n\naddMode(\"in-out\", function (f: EasingFunction) {\n  return function (t: number) {\n    return t < 0.5 ? f(2 * t) / 2 : 1 - f(2 * (1 - t)) / 2;\n  };\n});\n\naddMode(\"out-in\", function (f: EasingFunction) {\n  return function (t: number) {\n    return t < 0.5 ? 1 - f(2 * (1 - t)) / 2 : f(2 * t) / 2;\n  };\n});\n\naddEaseFn({\n  name: \"linear\",\n  fn: function (t: number) {\n    return t;\n  },\n});\n\naddEaseFn({\n  name: \"quad\",\n  fn: function (t: number) {\n    return t * t;\n  },\n});\n\naddEaseFn({\n  name: \"cubic\",\n  fn: function (t: number) {\n    return t * t * t;\n  },\n});\n\naddEaseFn({\n  name: \"quart\",\n  fn: function (t: number) {\n    return t * t * t * t;\n  },\n});\n\naddEaseFn({\n  name: \"quint\",\n  fn: function (t: number) {\n    return t * t * t * t * t;\n  },\n});\n\naddEaseFn({\n  name: \"sin sine\",\n  fn: function (t: number) {\n    return 1 - Math.cos((t * Math.PI) / 2);\n  },\n});\n\naddEaseFn({\n  name: \"exp expo\",\n  fn: function (t: number) {\n    return t == 0 ? 0 : Math.pow(2, 10 * (t - 1));\n  },\n});\n\naddEaseFn({\n  name: \"circle circ\",\n  fn: function (t: number) {\n    return 1 - Math.sqrt(1 - t * t);\n  },\n});\n\naddEaseFn({\n  name: \"bounce\",\n  fn: function (t: number) {\n    return t < 1 / 2.75\n      ? 7.5625 * t * t\n      : t < 2 / 2.75\n        ? 7.5625 * (t -= 1.5 / 2.75) * t + 0.75\n        : t < 2.5 / 2.75\n          ? 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375\n          : 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;\n  },\n});\n\naddEaseFn({\n  name: \"poly\",\n  fc: function (e) {\n    return function (t: number) {\n      return Math.pow(t, e);\n    };\n  },\n});\n\naddEaseFn({\n  name: \"elastic\",\n  fc: function (a, p) {\n    p = p || 0.45;\n    a = a || 1;\n    const s = (p / (2 * Math.PI)) * Math.asin(1 / a);\n    return function (t: number) {\n      return 1 + a * Math.pow(2, -10 * t) * Math.sin(((t - s) * (2 * Math.PI)) / p);\n    };\n  },\n});\n\naddEaseFn({\n  name: \"back\",\n  fc: function (s) {\n    s = typeof s !== \"undefined\" ? s : 1.70158;\n    return function (t: number) {\n      return t * t * ((s + 1) * t - s);\n    };\n  },\n});\n", "import { Vec2Value } from \"../common/matrix\";\nimport { uid } from \"../common/uid\";\n\nimport { Easing, EasingFunction, EasingFunctionName } from \"./easing\";\nimport { Component } from \"./component\";\nimport { Pinned } from \"./pin\";\n\nexport type TransitionOptions = {\n  duration?: number;\n  delay?: number;\n  append?: boolean;\n};\n\nexport type TransitionEndListener = (this: Component) => void;\n\nexport class Transition implements Pinned {\n  /** @internal */ uid = \"transition:\" + uid();\n\n  /** @internal */ _end: object;\n  /** @internal */ _start: object;\n\n  /** @internal */ _ending: TransitionEndListener[] = [];\n\n  /** @internal */ _duration: number;\n  /** @internal */ _delay: number;\n\n  /** @internal */ _owner: Component;\n\n  /** @internal */ _time: number;\n\n  /** @internal */ _easing: any;\n  /** @internal */ _next: any;\n\n  /** @internal */ _hide: boolean;\n  /** @internal */ _remove: boolean;\n\n  constructor(owner: Component, options: TransitionOptions = {}) {\n    this._end = {};\n    this._duration = options.duration || 400;\n    this._delay = options.delay || 0;\n\n    this._owner = owner;\n    this._time = 0;\n  }\n\n  /** @internal */\n  tick(component: Component, elapsed: number, now: number, last: number) {\n    this._time += elapsed;\n\n    if (this._time < this._delay) {\n      return;\n    }\n\n    const time = this._time - this._delay;\n\n    if (!this._start) {\n      this._start = {};\n      for (const key in this._end) {\n        this._start[key] = this._owner.pin(key);\n      }\n    }\n\n    let p = Math.min(time / this._duration, 1);\n    const ended = p >= 1;\n\n    if (typeof this._easing == \"function\") {\n      p = this._easing(p);\n    }\n\n    const q = 1 - p;\n\n    for (const key in this._end) {\n      this._owner.pin(key, this._start[key] * q + this._end[key] * p);\n    }\n\n    return ended;\n  }\n\n  /** @internal */\n  finish() {\n    this._ending.forEach((callback: TransitionEndListener) => {\n      try {\n        callback.call(this._owner);\n      } catch (e) {\n        console.error(e);\n      }\n    });\n    return this._next;\n  }\n\n  tween(opts?: TransitionOptions): Transition;\n  tween(duration?: number, delay?: number): Transition;\n  tween(a?: object | number, b?: number) {\n    let options: TransitionOptions;\n    if (typeof a === \"object\" && a !== null) {\n      options = a;\n    } else {\n      options = {};\n      if (typeof a === \"number\") {\n        options.duration = a;\n        if (typeof b === \"number\") {\n          options.delay = b;\n        }\n      }\n    }\n\n    return (this._next = new Transition(this._owner, options));\n  }\n\n  duration(duration: number) {\n    this._duration = duration;\n    return this;\n  }\n\n  delay(delay: number) {\n    this._delay = delay;\n    return this;\n  }\n\n  ease(easing: EasingFunctionName | EasingFunction) {\n    this._easing = Easing.get(easing);\n    return this;\n  }\n\n  done(fn: TransitionEndListener) {\n    this._ending.push(fn);\n    return this;\n  }\n\n  hide() {\n    this._ending.push(function () {\n      this.hide();\n    });\n    this._hide = true;\n    return this;\n  }\n\n  remove() {\n    this._ending.push(function () {\n      this.remove();\n    });\n    this._remove = true;\n    return this;\n  }\n\n  pin(key: string, value: any): this;\n  pin(obj: object): this;\n  pin(key: string): any;\n  pin(a?, b?) {\n    if (typeof a === \"object\") {\n      for (const attr in a) {\n        pinning(this._owner, this._end, attr, a[attr]);\n      }\n    } else if (typeof b !== \"undefined\") {\n      pinning(this._owner, this._end, a, b);\n    }\n    return this;\n  }\n\n  /**\n   *  @hidden @deprecated Use .done(fn) instead.\n   */\n  then(fn: TransitionEndListener) {\n    this.done(fn);\n    return this;\n  }\n\n  /**\n   *  @hidden @deprecated this doesn't do anything anymore, call transition on the component instead.\n   */\n  clear(forward: boolean) {\n    return this;\n  }\n\n  size(w: number, h: number) {\n    // Pin shortcut, used by Transition and Component\n    this.pin(\"width\", w);\n    this.pin(\"height\", h);\n    return this;\n  }\n\n  width(w: number): this;\n  width(): number;\n  width(w?: number) {\n    // Pin shortcut, used by Transition and Component\n    if (typeof w === \"undefined\") {\n      return this.pin(\"width\");\n    }\n    this.pin(\"width\", w);\n    return this;\n  }\n\n  height(h: number): this;\n  height(): number;\n  height(h?: number) {\n    // Pin shortcut, used by Transition and Component\n    if (typeof h === \"undefined\") {\n      return this.pin(\"height\");\n    }\n    this.pin(\"height\", h);\n    return this;\n  }\n\n  offset(value: Vec2Value): this;\n  offset(x: number, y: number): this;\n  offset(a: number | Vec2Value, b?: number) {\n    // Pin shortcut, used by Transition and Component\n    if (typeof a === \"object\") {\n      b = a.y;\n      a = a.x;\n    }\n    this.pin(\"offsetX\", a);\n    this.pin(\"offsetY\", b);\n    return this;\n  }\n\n  rotate(a: number) {\n    // Pin shortcut, used by Transition and Component\n    this.pin(\"rotation\", a);\n    return this;\n  }\n\n  skew(value: Vec2Value): this;\n  skew(x: number, y: number): this;\n  skew(a: number | Vec2Value, b?: number) {\n    // Pin shortcut, used by Transition and Component\n    if (typeof a === \"object\") {\n      b = a.y;\n      a = a.x;\n    } else if (typeof b === \"undefined\") {\n      b = a;\n    }\n    this.pin(\"skewX\", a);\n    this.pin(\"skewY\", b);\n    return this;\n  }\n\n  scale(value: Vec2Value): this;\n  scale(x: number, y: number): this;\n  scale(s: number): this;\n  scale(a: number | Vec2Value, b?: number) {\n    // Pin shortcut, used by Transition and Component\n    if (typeof a === \"object\") {\n      b = a.y;\n      a = a.x;\n    } else if (typeof b === \"undefined\") {\n      b = a;\n    }\n    this.pin(\"scaleX\", a);\n    this.pin(\"scaleY\", b);\n    return this;\n  }\n\n  alpha(a: number, ta?: number) {\n    // Pin shortcut, used by Transition and Component\n    this.pin(\"alpha\", a);\n    if (typeof ta !== \"undefined\") {\n      this.pin(\"textureAlpha\", ta);\n    }\n    return this;\n  }\n}\n\n/** @internal */\nfunction pinning(component: Component, map: object, key: string, value: number) {\n  if (typeof component.pin(key) === \"number\") {\n    map[key] = value;\n  } else if (typeof component.pin(key + \"X\") === \"number\" && typeof component.pin(key + \"Y\") === \"number\") {\n    map[key + \"X\"] = value;\n    map[key + \"Y\"] = value;\n  }\n}\n", "import stats from \"../common/stats\";\nimport { Vec2Value } from \"../common/matrix\";\nimport { uid } from \"../common/uid\";\nimport { getDevicePixelRatio } from \"../common/browser\";\n\nimport { Pin, Pinned, FitMode } from \"./pin\";\nimport { Transition, TransitionOptions } from \"./transition\";\n\n// todo: why there are two iids (other in pin)?\n/** @internal */\nlet iid = 0;\nstats.create = 0;\n\n/** @internal */\nfunction assertType<T>(obj: T): T {\n  if (obj && obj instanceof Component) {\n    return obj;\n  }\n  throw \"Invalid component: \" + obj;\n}\n\ninterface ComponentVisitor<D> {\n  reverse?: boolean;\n  visible?: boolean;\n  start?: (component: Component, data?: D) => boolean | void;\n  end?: (component: Component, data?: D) => boolean | void;\n}\n\nexport type ComponentTickListener<T> = (\n  this: T,\n  elapsed: number,\n  now: number,\n  last: number,\n) => boolean | void;\n\nexport type ComponentEventListener<T> = (this: T, ...args: any[]) => void;\n\n/** @hidden @deprecated Use component() */\nexport function create() {\n  return component();\n}\n\n/** @hidden @deprecated Use maximize() */\nexport function layer() {\n  return maximize();\n}\n\n/** @hidden @deprecated Use minimize() */\nexport function box() {\n  return minimize();\n}\n\n// todo: create a new subclass called layout, and make component abstract\n// discussion: in some cases sprites are used as parent component, like a window\n\n/** @hidden @deprecated */\nexport function layout() {\n  return component();\n}\n\nexport function component() {\n  return new Component();\n}\n\nexport function row(align: number) {\n  return new Component().row(align).label(\"Row\");\n}\n\nexport function column(align: number) {\n  return new Component().column(align).label(\"Column\");\n}\n\nexport function minimize() {\n  return new Component().minimize().label(\"Minimize\");\n}\n\nexport function maximize() {\n  return new Component().maximize().label(\"Maximize\");\n}\n\n// TODO: do not clear next/prev/parent on remove (why?)\n\n// There are three sets of core functions:\n// - tree model manipulation functions\n// - frame loop and rendering\n// - events handling\n\nexport class Component implements Pinned {\n  /** @internal */ uid = \"component:\" + uid();\n\n  /** @internal */ _label = \"\";\n\n  /** @internal */ _parent: Component | null = null;\n  /** @internal */ _next: Component | null = null;\n  /** @internal */ _prev: Component | null = null;\n\n  /** @internal */ _first: Component | null = null;\n  /** @internal */ _last: Component | null = null;\n\n  /** @internal */ _visible = true;\n\n  // this is computed on every render, and used by children\n  /** @internal */ _alpha: number = 1;\n\n  /** @internal */ _padding: number = 0;\n  /** @internal */ _spacing: number = 0;\n\n  /** @internal */ _pin = new Pin(this);\n\n  /** @internal */ _ts_pin: number;\n  /** @internal */ _ts_parent: number;\n  /** @internal */ _ts_children: number;\n  /** @internal */ _ts_touch: number;\n\n  // todo: don't need to check if these fields are initialized anymore\n  /** @internal */ _listeners: Record<string, ComponentEventListener<Component>[]> = {};\n  /** @internal */ _attrs: Record<string, any> = {};\n  /** @internal */ _flags: Record<string, any> = {};\n  /** @internal */ _transitions: Transition[] = [];\n\n  /** @internal */ _tickBefore: ComponentTickListener<any>[] = [];\n  /** @internal */ _tickAfter: ComponentTickListener<any>[] = [];\n\n  /** @internal */ _layoutTicker?: () => void;\n\n  // todo: remove this\n  MAX_ELAPSE = Infinity;\n\n  /** @internal */ _mo_seq: number;\n  /** @internal */ _mo_seqAlign: number;\n  /** @internal */ _mo_box: number;\n\n  constructor() {\n    stats.create++;\n    if (this instanceof Component) {\n      this.label(this.constructor.name);\n    }\n  }\n\n  matrix(relative = false) {\n    if (relative === true) {\n      return this._pin.relativeMatrix();\n    }\n    return this._pin.absoluteMatrix();\n  }\n\n  /** @hidden @deprecated */\n  getPixelRatio() {\n    // todo: remove this function\n    const m = this._parent?.matrix();\n    const pixelRatio = !m ? 1 : Math.max(Math.abs(m.a), Math.abs(m.b)) / getDevicePixelRatio();\n    return pixelRatio;\n  }\n\n  /** @hidden This is not accurate before first tick */\n  getDevicePixelRatio() {\n    // todo: parent matrix is not available in the first call\n    const parentMatrix = this._parent?.matrix();\n    const pixelRatio = !parentMatrix\n      ? 1\n      : Math.max(Math.abs(parentMatrix.a), Math.abs(parentMatrix.b));\n    return pixelRatio;\n  }\n\n  /** @hidden This is not accurate before first tick */\n  getLogicalPixelRatio() {\n    return this.getDevicePixelRatio() / getDevicePixelRatio();\n  }\n\n  pin(key: string): any;\n  pin(key: string, value: any): this;\n  pin(obj: object): this;\n  pin(): Pin;\n  pin(a?: object | string, b?: any) {\n    if (typeof a === \"object\") {\n      this._pin.set(a);\n      return this;\n    } else if (typeof a === \"string\") {\n      if (typeof b === \"undefined\") {\n        return this._pin.get(a);\n      } else {\n        this._pin.set(a, b);\n        return this;\n      }\n    } else if (typeof a === \"undefined\") {\n      return this._pin;\n    }\n  }\n\n  fit(width: number, height: number, mode?: FitMode): this;\n  fit(fit: object): this;\n  fit(a, b?, c?) {\n    if (typeof a === \"object\") {\n      c = b;\n      b = a.y;\n      a = a.x;\n    }\n    this._pin.fit(a, b, c);\n    return this;\n  }\n\n  /** @hidden @deprecated Use fit */\n  scaleTo(a, b?, c?): this {\n    return this.fit(a, b, c);\n  }\n\n  toString() {\n    return \"[\" + this._label + \"]\";\n  }\n\n  /** @hidden @deprecated Use label() */\n  id(): string;\n  /** @hidden @deprecated Use label() */\n  id(label: string): this;\n  /** @hidden @deprecated Use label() */\n  id(label?: string) {\n    if (typeof label === \"undefined\") {\n      return this._label;\n    }\n    this._label = label;\n    return this;\n  }\n\n  label(): string;\n  label(label: string): this;\n  label(label?: string) {\n    if (typeof label === \"undefined\") {\n      return this._label;\n    }\n    this._label = label;\n    return this;\n  }\n\n  attr(name: string, value: any): this;\n  attr(name: string): any;\n  attr(name: string, value?: any) {\n    if (typeof value === \"undefined\") {\n      return this._attrs !== null ? this._attrs[name] : undefined;\n    }\n    (this._attrs !== null ? this._attrs : (this._attrs = {}))[name] = value;\n    return this;\n  }\n\n  visible(visible: boolean): this;\n  visible(): boolean;\n  visible(visible?: boolean) {\n    if (typeof visible === \"undefined\") {\n      return this._visible;\n    }\n    this._visible = visible;\n    this._parent && (this._parent._ts_children = ++iid);\n    this._ts_pin = ++iid;\n    this.touch();\n    return this;\n  }\n\n  hide() {\n    this.visible(false);\n    return this;\n  }\n\n  show() {\n    this.visible(true);\n    return this;\n  }\n\n  parent() {\n    return this._parent;\n  }\n\n  next(visible?: boolean) {\n    let next = this._next;\n    while (next && visible && !next._visible) {\n      next = next._next;\n    }\n    return next;\n  }\n\n  prev(visible?: boolean) {\n    let prev = this._prev;\n    while (prev && visible && !prev._visible) {\n      prev = prev._prev;\n    }\n    return prev;\n  }\n\n  first(visible?: boolean) {\n    let next = this._first;\n    while (next && visible && !next._visible) {\n      next = next._next;\n    }\n    return next;\n  }\n\n  last(visible?: boolean) {\n    let prev = this._last;\n    while (prev && visible && !prev._visible) {\n      prev = prev._prev;\n    }\n    return prev;\n  }\n\n  visit<P>(visitor: ComponentVisitor<P>, payload?: P) {\n    const reverse = visitor.reverse;\n    const visible = visitor.visible;\n    if (visitor.start && visitor.start(this, payload)) {\n      return;\n    }\n    let child: Component;\n    let next = reverse ? this.last(visible) : this.first(visible);\n    while ((child = next)) {\n      next = reverse ? child.prev(visible) : child.next(visible);\n      if (child.visit(visitor, payload)) {\n        return true;\n      }\n    }\n    return visitor.end && visitor.end(this, payload);\n  }\n\n  append(...child: Component[]): this;\n  append(child: Component[]): this;\n  append(child: Component | Component[], more?: Component) {\n    if (Array.isArray(child)) {\n      for (let i = 0; i < child.length; i++) {\n        Component.append(this, child[i]);\n      }\n    } else if (typeof more !== \"undefined\") {\n      // deprecated\n      for (let i = 0; i < arguments.length; i++) {\n        Component.append(this, arguments[i]);\n      }\n    } else if (typeof child !== \"undefined\") Component.append(this, child);\n\n    return this;\n  }\n\n  prepend(...child: Component[]): this;\n  prepend(child: Component[]): this;\n  prepend(child: Component | Component[], more?: Component) {\n    if (Array.isArray(child)) {\n      for (let i = child.length - 1; i >= 0; i--) {\n        Component.prepend(this, child[i]);\n      }\n    } else if (typeof more !== \"undefined\") {\n      // deprecated\n      for (let i = arguments.length - 1; i >= 0; i--) {\n        Component.prepend(this, arguments[i]);\n      }\n    } else if (typeof child !== \"undefined\") Component.prepend(this, child);\n\n    return this;\n  }\n\n  appendTo(parent: Component) {\n    Component.append(parent, this);\n    return this;\n  }\n\n  prependTo(parent: Component) {\n    Component.prepend(parent, this);\n    return this;\n  }\n\n  insertNext(sibling: Component, more?: Component) {\n    if (Array.isArray(sibling)) {\n      for (let i = 0; i < sibling.length; i++) {\n        Component.insertAfter(sibling[i], this);\n      }\n    } else if (typeof more !== \"undefined\") {\n      // deprecated\n      for (let i = 0; i < arguments.length; i++) {\n        Component.insertAfter(arguments[i], this);\n      }\n    } else if (typeof sibling !== \"undefined\") {\n      Component.insertAfter(sibling, this);\n    }\n\n    return this;\n  }\n\n  insertPrev(sibling: Component, more?: Component) {\n    if (Array.isArray(sibling)) {\n      for (let i = sibling.length - 1; i >= 0; i--) {\n        Component.insertBefore(sibling[i], this);\n      }\n    } else if (typeof more !== \"undefined\") {\n      // deprecated\n      for (let i = arguments.length - 1; i >= 0; i--) {\n        Component.insertBefore(arguments[i], this);\n      }\n    } else if (typeof sibling !== \"undefined\") {\n      Component.insertBefore(sibling, this);\n    }\n\n    return this;\n  }\n\n  insertAfter(prev: Component) {\n    Component.insertAfter(this, prev);\n    return this;\n  }\n\n  insertBefore(next: Component) {\n    Component.insertBefore(this, next);\n    return this;\n  }\n\n  /** @internal */\n  static append(parent: Component, child: Component) {\n    assertType(child);\n    assertType(parent);\n\n    child.remove();\n\n    if (parent._last) {\n      parent._last._next = child;\n      child._prev = parent._last;\n    }\n\n    child._parent = parent;\n    parent._last = child;\n\n    if (!parent._first) {\n      parent._first = child;\n    }\n\n    child._parent._flag(child, true);\n\n    child._ts_parent = ++iid;\n    parent._ts_children = ++iid;\n    parent.touch();\n  }\n\n  /** @internal */\n  static prepend(parent: Component, child: Component) {\n    assertType(child);\n    assertType(parent);\n\n    child.remove();\n\n    if (parent._first) {\n      parent._first._prev = child;\n      child._next = parent._first;\n    }\n\n    child._parent = parent;\n    parent._first = child;\n\n    if (!parent._last) {\n      parent._last = child;\n    }\n\n    child._parent._flag(child, true);\n\n    child._ts_parent = ++iid;\n    parent._ts_children = ++iid;\n    parent.touch();\n  }\n\n  /** @internal */\n  static insertBefore(self: Component, next: Component) {\n    assertType(self);\n    assertType(next);\n\n    self.remove();\n\n    const parent = next._parent;\n    const prev = next._prev;\n\n    if (!parent) {\n      return;\n    }\n\n    next._prev = self;\n    // todo:\n    (prev && (prev._next = self)) || (parent && (parent._first = self));\n\n    self._parent = parent;\n    self._prev = prev;\n    self._next = next;\n\n    self._parent._flag(self, true);\n\n    self._ts_parent = ++iid;\n    self.touch();\n  }\n\n  /** @internal */\n  static insertAfter(self: Component, prev: Component) {\n    assertType(self);\n    assertType(prev);\n\n    self.remove();\n\n    const parent = prev._parent;\n    const next = prev._next;\n\n    if (!parent) {\n      return;\n    }\n\n    prev._next = self;\n    // todo:\n    (next && (next._prev = self)) || (parent && (parent._last = self));\n\n    self._parent = parent;\n    self._prev = prev;\n    self._next = next;\n\n    self._parent._flag(self, true);\n\n    self._ts_parent = ++iid;\n    self.touch();\n  }\n\n  remove(child?: Component, more?: any) {\n    if (typeof child !== \"undefined\") {\n      if (Array.isArray(child)) {\n        for (let i = 0; i < child.length; i++) {\n          assertType(child[i]).remove();\n        }\n      } else if (typeof more !== \"undefined\") {\n        for (let i = 0; i < arguments.length; i++) {\n          assertType(arguments[i]).remove();\n        }\n      } else {\n        assertType(child).remove();\n      }\n      return this;\n    }\n\n    if (this._prev) {\n      this._prev._next = this._next;\n    }\n    if (this._next) {\n      this._next._prev = this._prev;\n    }\n\n    if (this._parent) {\n      if (this._parent._first === this) {\n        this._parent._first = this._next;\n      }\n      if (this._parent._last === this) {\n        this._parent._last = this._prev;\n      }\n\n      this._parent._flag(this, false);\n\n      this._parent._ts_children = ++iid;\n      this._parent.touch();\n    }\n\n    this._prev = this._next = this._parent = null;\n    this._ts_parent = ++iid;\n    // this._parent.touch();\n    return this;\n  }\n\n  empty() {\n    let child: Component | null = null;\n    let next = this._first;\n    while ((child = next)) {\n      next = child._next;\n      child._prev = child._next = child._parent = null;\n\n      this._flag(child, false);\n    }\n\n    this._first = this._last = null;\n\n    this._ts_children = ++iid;\n    this.touch();\n    return this;\n  }\n\n  touch() {\n    this._ts_touch = ++iid;\n    this._parent && this._parent.touch();\n    return this;\n  }\n\n  /** @internal */\n  _flag(child: Component, value: boolean): Component;\n  /** @internal */\n  _flag(key: string): boolean;\n  /** @internal */\n  _flag(key: string, value: boolean): Component;\n  /** @internal Deep flag, used for optimizing event distribution. */\n  _flag(key: string | Component, value?: boolean) {\n    if (typeof value === \"undefined\") {\n      return (this._flags !== null && this._flags[key as string]) || 0;\n    }\n    if (typeof key === \"string\") {\n      if (value) {\n        this._flags = this._flags || {};\n        if (!this._flags[key] && this._parent) {\n          this._parent._flag(key, true);\n        }\n        this._flags[key] = (this._flags[key] || 0) + 1;\n      } else if (this._flags && this._flags[key] > 0) {\n        if (this._flags[key] == 1 && this._parent) {\n          this._parent._flag(key, false);\n        }\n        this._flags[key] = this._flags[key] - 1;\n      }\n    }\n    if (typeof key === \"object\") {\n      if (key._flags) {\n        for (const type in key._flags) {\n          if (key._flags[type] > 0) {\n            this._flag(type, value);\n          }\n        }\n      }\n    }\n    return this;\n  }\n\n  /** @internal */\n  hitTest(hit: Vec2Value) {\n    const width = this._pin._width;\n    const height = this._pin._height;\n    return hit.x >= 0 && hit.x <= width && hit.y >= 0 && hit.y <= height;\n  }\n\n  /** @hidden */\n  prerender() {\n    if (!this._visible) {\n      return;\n    }\n\n    this.prerenderTexture();\n\n    let child: Component;\n    let next = this._first;\n    while ((child = next)) {\n      next = child._next;\n      child.prerender();\n    }\n  }\n\n  /** @hidden */\n  prerenderTexture() {\n    // to be implemented by subclasses if needed\n  }\n\n  /** @hidden */\n  private renderedBefore = false;\n\n  /** @hidden */\n  render(context: CanvasRenderingContext2D) {\n    if (!this._visible) {\n      return;\n    }\n    stats.component++;\n\n    const m = this.matrix();\n    context.setTransform(m.a, m.b, m.c, m.d, m.e, m.f);\n\n    // move this elsewhere!\n    this._alpha = this._pin._alpha * (this._parent ? this._parent._alpha : 1);\n    const alpha = this._pin._textureAlpha * this._alpha;\n\n    if (context.globalAlpha != alpha) {\n      context.globalAlpha = alpha;\n    }\n\n    if (!this.renderedBefore) {\n      // todo: because getDevicePixelRatio is not accurate before first tick\n      this.prerenderTexture();\n    }\n    this.renderedBefore = true;\n\n    this.renderTexture(context);\n\n    if (context.globalAlpha != this._alpha) {\n      context.globalAlpha = this._alpha;\n    }\n\n    let child: Component;\n    let next = this._first;\n    while ((child = next)) {\n      next = child._next;\n      child.render(context);\n    }\n  }\n\n  /** @hidden */\n  renderTexture(context: CanvasRenderingContext2D) {\n    // to be implemented by subclasses if needed\n  }\n\n  /** @internal */\n  _tick(elapsed: number, now: number, last: number) {\n    if (!this._visible) {\n      return;\n    }\n\n    if (elapsed > this.MAX_ELAPSE) {\n      elapsed = this.MAX_ELAPSE;\n    }\n\n    let ticked = false;\n\n    if (this._tickBefore !== null) {\n      for (let i = 0; i < this._tickBefore.length; i++) {\n        stats.tick++;\n        const tickFn = this._tickBefore[i];\n        ticked = tickFn.call(this, elapsed, now, last) === true || ticked;\n      }\n    }\n\n    let child: Component | null;\n    let next = this._first;\n    while ((child = next)) {\n      next = child._next;\n      if (child._flag(\"_tick\")) {\n        ticked = child._tick(elapsed, now, last) === true ? true : ticked;\n      }\n    }\n\n    if (this._tickAfter !== null) {\n      for (let i = 0; i < this._tickAfter.length; i++) {\n        stats.tick++;\n        const tickFn = this._tickAfter[i];\n        ticked = tickFn.call(this, elapsed, now, last) === true || ticked;\n      }\n    }\n\n    return ticked;\n  }\n\n  tick(callback: ComponentTickListener<this>, before = false) {\n    if (typeof callback !== \"function\") {\n      return;\n    }\n    if (before) {\n      if (this._tickBefore === null) {\n        this._tickBefore = [];\n      }\n      this._tickBefore.push(callback);\n    } else {\n      if (this._tickAfter === null) {\n        this._tickAfter = [];\n      }\n      this._tickAfter.push(callback);\n    }\n    const hasTickListener = this._tickAfter?.length > 0 || this._tickBefore?.length > 0;\n    this._flag(\"_tick\", hasTickListener);\n  }\n\n  untick(callback: ComponentTickListener<this>) {\n    if (typeof callback !== \"function\") {\n      return;\n    }\n    let i;\n    if (this._tickBefore !== null && (i = this._tickBefore.indexOf(callback)) >= 0) {\n      this._tickBefore.splice(i, 1);\n    }\n    if (this._tickAfter !== null && (i = this._tickAfter.indexOf(callback)) >= 0) {\n      this._tickAfter.splice(i, 1);\n    }\n  }\n\n  timeout(callback: () => any, time: number) {\n    this.setTimeout(callback, time);\n  }\n\n  setTimeout(callback: () => any, time: number) {\n    function timer(t: number) {\n      if ((time -= t) < 0) {\n        this.untick(timer);\n        callback.call(this);\n      } else {\n        return true;\n      }\n    }\n    this.tick(timer);\n    return timer;\n  }\n\n  clearTimeout(timer: ComponentTickListener<this>) {\n    this.untick(timer);\n  }\n\n  on(types: string, listener: ComponentEventListener<this>): this;\n  /** @hidden @deprecated @internal */\n  on(types: string[], listener: ComponentEventListener<this>): this;\n  on(type: string | string[], listener: ComponentEventListener<this>) {\n    if (!type || !type.length || typeof listener !== \"function\") {\n      return this;\n    }\n    if (typeof type !== \"string\" && typeof type.join === \"function\") {\n      // deprecated arguments, type is array\n      for (let i = 0; i < type.length; i++) {\n        this.on(type[i], listener);\n      }\n    } else if (typeof type === \"string\" && type.indexOf(\" \") > -1) {\n      // deprecated arguments, type is spaced string\n      type = type.match(/\\S+/g);\n      for (let i = 0; i < type.length; i++) {\n        this._on(type[i], listener);\n      }\n    } else if (typeof type === \"string\") {\n      this._on(type, listener);\n    } else {\n      // invalid\n    }\n    return this;\n  }\n\n  /** @internal */\n  _on(type: string, listener: ComponentEventListener<this>) {\n    if (typeof type !== \"string\" && typeof listener !== \"function\") {\n      return;\n    }\n    this._listeners[type] = this._listeners[type] || [];\n    this._listeners[type].push(listener);\n    // todo: maybe recompute/set exact value?\n    this._flag(type, true);\n  }\n\n  off(types: string, listener: ComponentEventListener<this>): this;\n  /** @hidden @deprecated @internal */\n  off(types: string[], listener: ComponentEventListener<this>): this;\n  off(type: string | string[], listener: ComponentEventListener<this>) {\n    if (!type || !type.length || typeof listener !== \"function\") {\n      return this;\n    }\n    if (typeof type !== \"string\" && typeof type.join === \"function\") {\n      // deprecated arguments, type is array\n      for (let i = 0; i < type.length; i++) {\n        this.off(type[i], listener);\n      }\n    } else if (typeof type === \"string\" && type.indexOf(\" \") > -1) {\n      // deprecated arguments, type is spaced string\n      type = type.match(/\\S+/g);\n      for (let i = 0; i < type.length; i++) {\n        this._off(type[i], listener);\n      }\n    } else if (typeof type === \"string\") {\n      this._off(type, listener);\n    } else {\n      // invalid\n    }\n    return this;\n  }\n\n  /** @internal */\n  _off(type: string, listener: ComponentEventListener<this>) {\n    if (typeof type !== \"string\" && typeof listener !== \"function\") {\n      return;\n    }\n    const listeners = this._listeners[type];\n    if (!listeners || !listeners.length) {\n      return;\n    }\n    const index = listeners.indexOf(listener);\n    if (index >= 0) {\n      listeners.splice(index, 1);\n      // if (!listeners.length) {\n      //   delete this._listeners[type];\n      // }\n      // todo: maybe recompute/set exact value?\n      this._flag(type, false);\n    }\n  }\n\n  listeners(type: string) {\n    return this._listeners[type];\n  }\n\n  publish(name: string, args?: any) {\n    const listeners = this.listeners(name);\n    if (!listeners || !listeners.length) {\n      return 0;\n    }\n    for (let l = 0; l < listeners.length; l++) {\n      listeners[l].apply(this, args);\n    }\n    return listeners.length;\n  }\n\n  /** @hidden @deprecated @internal */\n  trigger(name: string, args?: any) {\n    this.publish(name, args);\n    return this;\n  }\n\n  size(w: number, h: number) {\n    // Pin shortcut, used by Transition and Component\n    this.pin(\"width\", w);\n    this.pin(\"height\", h);\n    return this;\n  }\n\n  width(w: number): this;\n  width(): number;\n  width(w?: number) {\n    // Pin shortcut, used by Transition and Component\n    if (typeof w === \"undefined\") {\n      return this.pin(\"width\");\n    }\n    this.pin(\"width\", w);\n    return this;\n  }\n\n  height(h: number): this;\n  height(): number;\n  height(h?: number) {\n    // Pin shortcut, used by Transition and Component\n    if (typeof h === \"undefined\") {\n      return this.pin(\"height\");\n    }\n    this.pin(\"height\", h);\n    return this;\n  }\n\n  offset(value: Vec2Value): this;\n  offset(x: number, y: number): this;\n  offset(a?: Vec2Value | number, b?: number) {\n    // Pin shortcut, used by Transition and Component\n    if (typeof a === \"object\") {\n      b = a.y;\n      a = a.x;\n    }\n    this.pin(\"offsetX\", a);\n    this.pin(\"offsetY\", b);\n    return this;\n  }\n\n  rotate(a: number) {\n    // Pin shortcut, used by Transition and Component\n    this.pin(\"rotation\", a);\n    return this;\n  }\n\n  skew(value: Vec2Value): this;\n  skew(x: number, y: number): this;\n  skew(a?: Vec2Value | number, b?: number) {\n    // Pin shortcut, used by Transition and Component\n    if (typeof a === \"object\") {\n      b = a.y;\n      a = a.x;\n    } else if (typeof b === \"undefined\") b = a;\n    this.pin(\"skewX\", a);\n    this.pin(\"skewY\", b);\n    return this;\n  }\n\n  scale(value: Vec2Value): this;\n  scale(x: number, y: number): this;\n  scale(s: number): this;\n  scale(a?: Vec2Value | number, b?: number) {\n    // Pin shortcut, used by Transition and Component\n    if (typeof a === \"object\") {\n      b = a.y;\n      a = a.x;\n    } else if (typeof b === \"undefined\") b = a;\n    this.pin(\"scaleX\", a);\n    this.pin(\"scaleY\", b);\n    return this;\n  }\n\n  alpha(a: number, ta?: number) {\n    // Pin shortcut, used by Transition and Component\n    this.pin(\"alpha\", a);\n    if (typeof ta !== \"undefined\") {\n      this.pin(\"textureAlpha\", ta);\n    }\n    return this;\n  }\n\n  tween(opts?: TransitionOptions): Transition;\n  tween(duration?: number, delay?: number, append?: boolean): Transition;\n  tween(a?: object | number, b?: number, c?: boolean) {\n    let options: TransitionOptions;\n    if (typeof a === \"object\" && a !== null) {\n      options = a;\n    } else {\n      options = {};\n      if (typeof a === \"number\") {\n        options.duration = a;\n        if (typeof b === \"number\") {\n          options.delay = b;\n          if (typeof c === \"boolean\") {\n            options.append = c;\n          }\n        } else if (typeof b === \"boolean\") {\n          options.append = b;\n        }\n      } else if (typeof a === \"boolean\") {\n        options.append = a;\n      }\n    }\n\n    if (!this._transitionTickInitied) {\n      this.tick(this._transitionTick, true);\n      this._transitionTickInitied = true;\n    }\n\n    this.touch();\n\n    // todo: what is the expected default behavior?\n    if (!options.append) {\n      this._transitions.length = 0;\n    }\n\n    const transition = new Transition(this, options);\n    this._transitions.push(transition);\n    return transition;\n  }\n\n  /** @internal */ _transitionTickInitied = false;\n  /** @internal */ _transitionTickLastTime = 0;\n  /** @internal */\n  _transitionTick = (elapsed: number, now: number, last: number) => {\n    if (!this._transitions.length) {\n      return false;\n    }\n\n    // ignore untracked tick\n    const ignore = this._transitionTickLastTime !== last;\n    this._transitionTickLastTime = now;\n    if (ignore) {\n      return true;\n    }\n\n    const head = this._transitions[0];\n\n    const ended = head.tick(this, elapsed, now, last);\n\n    // todo: move this logic to TransitionQueue\n    if (ended) {\n      if (head === this._transitions[0]) {\n        this._transitions.shift();\n      }\n      const next = head.finish();\n      if (next) {\n        this._transitions.unshift(next);\n      }\n    }\n\n    return true;\n  };\n\n  row(align: number) {\n    this.align(\"row\", align);\n    return this;\n  }\n\n  column(align: number) {\n    this.align(\"column\", align);\n    return this;\n  }\n\n  align(type: \"row\" | \"column\", align: number) {\n    this._padding = this._padding;\n    this._spacing = this._spacing;\n\n    this._layoutTicker && this.untick(this._layoutTicker);\n    this.tick(\n      (this._layoutTicker = () => {\n        if (this._mo_seq == this._ts_touch) {\n          return;\n        }\n        this._mo_seq = this._ts_touch;\n\n        const alignChildren = this._mo_seqAlign != this._ts_children;\n        this._mo_seqAlign = this._ts_children;\n\n        let width = 0;\n        let height = 0;\n\n        let child: Component;\n        let next = this.first(true);\n        let first = true;\n        while ((child = next)) {\n          next = child.next(true);\n\n          child.matrix(true);\n          const w = child.pin(\"boxWidth\");\n          const h = child.pin(\"boxHeight\");\n\n          if (type == \"column\") {\n            !first && (height += this._spacing);\n            child.pin(\"offsetY\") != height && child.pin(\"offsetY\", height);\n            width = Math.max(width, w);\n            height = height + h;\n            alignChildren && child.pin(\"alignX\", align);\n          } else if (type == \"row\") {\n            !first && (width += this._spacing);\n            child.pin(\"offsetX\") != width && child.pin(\"offsetX\", width);\n            width = width + w;\n            height = Math.max(height, h);\n            alignChildren && child.pin(\"alignY\", align);\n          }\n          first = false;\n        }\n        width += 2 * this._padding;\n        height += 2 * this._padding;\n        this.pin(\"width\") != width && this.pin(\"width\", width);\n        this.pin(\"height\") != height && this.pin(\"height\", height);\n      }),\n    );\n    return this;\n  }\n\n  /** @hidden @deprecated Use minimize() */\n  box() {\n    return this.minimize();\n  }\n\n  /** @hidden @deprecated Use minimize() */\n  layer() {\n    return this.maximize();\n  }\n\n  /**\n   * Set size to match largest child size.\n   */\n  minimize() {\n    this._padding = this._padding;\n\n    this._layoutTicker && this.untick(this._layoutTicker);\n    this.tick(\n      (this._layoutTicker = () => {\n        if (this._mo_box == this._ts_touch) {\n          return;\n        }\n        this._mo_box = this._ts_touch;\n\n        let width = 0;\n        let height = 0;\n        let child: Component;\n        let next = this.first(true);\n        while ((child = next)) {\n          next = child.next(true);\n          child.matrix(true);\n          const w = child.pin(\"boxWidth\");\n          const h = child.pin(\"boxHeight\");\n          width = Math.max(width, w);\n          height = Math.max(height, h);\n        }\n        width += 2 * this._padding;\n        height += 2 * this._padding;\n        this.pin(\"width\") != width && this.pin(\"width\", width);\n        this.pin(\"height\") != height && this.pin(\"height\", height);\n      }),\n    );\n    return this;\n  }\n\n  /**\n   * Set size to match parent size.\n   */\n  maximize() {\n    this._layoutTicker && this.untick(this._layoutTicker);\n    this.tick(\n      (this._layoutTicker = () => {\n        const parent = this.parent();\n        if (parent) {\n          const width = parent.pin(\"width\");\n          if (this.pin(\"width\") != width) {\n            this.pin(\"width\", width);\n          }\n          const height = parent.pin(\"height\");\n          if (this.pin(\"height\") != height) {\n            this.pin(\"height\", height);\n          }\n        }\n      }),\n      true,\n    );\n    return this;\n  }\n\n  // TODO: move padding to pin\n  /**\n   * Set cell spacing for layout.\n   */\n  padding(pad: number) {\n    this._padding = pad;\n    return this;\n  }\n\n  /**\n   * Set cell spacing for row and column layout.\n   */\n  spacing(space: number) {\n    this._spacing = space;\n    return this;\n  }\n}\n\n/** @hidden @deprecated Node is renamed to Component */\nexport { Component as Node };\n", "import {\n  PipeTexture,\n  texture,\n  Texture,\n  TexturePrerenderContext,\n  TextureSelectionInput,\n} from \"../texture\";\nimport { ResizableTexture } from \"../texture/resizable\";\n\nimport { Component } from \"./component\";\n\nexport function sprite(frame?: TextureSelectionInput) {\n  const sprite = new Sprite();\n  frame && sprite.texture(frame);\n  return sprite;\n}\n\nexport class Sprite extends Component {\n  /** @internal */ _texture: Texture | null = null;\n\n  /** @internal */ _image: Texture | null = null;\n  /** @internal */ _tiled: boolean = false;\n  /** @internal */ _stretched: boolean = false;\n\n  constructor() {\n    super();\n    this.label(\"Sprite\");\n  }\n\n  texture(frame: TextureSelectionInput) {\n    this._image = texture(frame).one();\n    if (this._image) {\n      this.pin(\"width\", this._image.getWidth());\n      this.pin(\"height\", this._image.getHeight());\n\n      // todo: could we chain textures in a way that doesn't require rebuilding the chain?\n      if (this._tiled) {\n        this._texture = new ResizableTexture(this._image, \"tile\");\n      } else if (this._stretched) {\n        this._texture = new ResizableTexture(this._image, \"stretch\");\n      } else {\n        this._texture = new PipeTexture(this._image);\n      }\n    } else {\n      this.pin(\"width\", 0);\n      this.pin(\"height\", 0);\n      this._texture = null;\n    }\n    return this;\n  }\n\n  /** @deprecated */\n  image(frame: TextureSelectionInput) {\n    return this.texture(frame);\n  }\n\n  tile(inner = false) {\n    this._tiled = true;\n    const texture = new ResizableTexture(this._image, \"tile\");\n    this._texture = texture;\n    return this;\n  }\n\n  stretch(inner = false) {\n    this._stretched = true;\n    const texture = new ResizableTexture(this._image, \"stretch\");\n    this._texture = texture;\n    return this;\n  }\n\n  /** @internal */\n  private prerenderContext = {} as TexturePrerenderContext;\n\n  /** @hidden */\n  prerenderTexture(): void {\n    if (!this._image) return;\n    const pixelRatio = this.getDevicePixelRatio();\n    this.prerenderContext.pixelRatio = pixelRatio;\n    const updated = this._image.prerender(this.prerenderContext);\n    if (updated === true) {\n      // should we let draw function decide to make this update?\n      const w = this._image.getWidth();\n      const h = this._image.getHeight();\n      this.size(w, h);\n    }\n  }\n\n  /** @hidden */\n  renderTexture(context: CanvasRenderingContext2D) {\n    if (!this._texture) return;\n\n    if (this._texture[\"_resizeMode\"]) {\n      this._texture.dw = this.pin(\"width\");\n      this._texture.dh = this.pin(\"height\");\n    }\n\n    this._texture.draw(context);\n  }\n}\n\n/** @hidden @deprecated */\nexport { sprite as image };\n/** @hidden @deprecated */\nexport { Sprite as Image };\n", "import { Sprite } from \"../core/sprite\";\n\nimport { ImageTexture } from \"./image\";\nimport { TexturePrerenderContext } from \"./texture\";\n\ntype CanvasTextureDrawer = (this: CanvasTexture) => void;\ntype CanvasTextureMemoizer = (this: CanvasTexture) => any;\n\n/** @hidden @deprecated */\ntype LegacyCanvasTextureDrawer = (this: CanvasTexture, context: CanvasRenderingContext2D) => void;\n/** @hidden @deprecated */\ntype LegacyCanvasSpriteMemoizer = () => any;\n\n/** @hidden @deprecated */\ntype LegacyCanvasSpriteDrawer = (ratio: number, texture: CanvasTexture, sprite: Sprite) => void;\n\n/**\n * A texture with off-screen canvas.\n */\nexport class CanvasTexture extends ImageTexture {\n  /** @internal */ _source: HTMLCanvasElement;\n  /** @internal */ _drawer?: CanvasTextureDrawer;\n  /** @internal */ _memoizer: CanvasTextureMemoizer;\n\n  /** @internal */ _lastPixelRatio = 0;\n  /** @internal */ _lastMemoKey: any;\n\n  constructor() {\n    super(document.createElement(\"canvas\"));\n  }\n\n  /**\n   * Set texture size to given width and height, and set canvas size to texture size multiply by pixelRatio.\n   */\n  setSize(destWidth: number, destHeight: number, pixelRatio = 1) {\n    this._source.width = destWidth * pixelRatio;\n    this._source.height = destHeight * pixelRatio;\n    this._pixelRatio = pixelRatio;\n  }\n\n  getContext(type = \"2d\", attributes?: any): CanvasRenderingContext2D {\n    return this._source.getContext(type, attributes) as CanvasRenderingContext2D;\n  }\n\n  /**\n   * @hidden @experimental\n   *\n   * This is the ratio of screen pixel to this canvas pixel.\n   */\n  getDevicePixelRatio() {\n    return this._lastPixelRatio;\n  }\n\n  // todo: remove in stable v1.0\n  /** @hidden @deprecated */\n  getOptimalPixelRatio() {\n    return this.getDevicePixelRatio();\n  }\n\n  setMemoizer(memoizer: CanvasTextureMemoizer) {\n    this._memoizer = memoizer;\n  }\n\n  setDrawer(drawer: CanvasTextureDrawer) {\n    this._drawer = drawer;\n  }\n\n  /** @internal */\n  prerender(context: TexturePrerenderContext): boolean {\n    const newPixelRatio = context.pixelRatio;\n    const lastPixelRatio = this._lastPixelRatio;\n\n    const pixelRationChange = lastPixelRatio / newPixelRatio;\n    const pixelRatioChanged =\n      lastPixelRatio === 0 || pixelRationChange > 1.25 || pixelRationChange < 0.8;\n\n    if (pixelRatioChanged) {\n      this._lastPixelRatio = newPixelRatio;\n    }\n\n    const newMemoKey = this._memoizer ? this._memoizer.call(this) : null;\n    const memoKeyChanged = this._lastMemoKey !== newMemoKey;\n\n    if (pixelRatioChanged || memoKeyChanged) {\n      this._lastMemoKey = newMemoKey;\n      this._lastPixelRatio = newPixelRatio;\n\n      if (typeof this._drawer === \"function\") {\n        this._drawer.call(this);\n      }\n      return true;\n    }\n  }\n\n  /** @hidden @deprecated */\n  size(width: number, height: number, pixelRatio: number) {\n    this.setSize(width, height, pixelRatio);\n    return this;\n  }\n\n  /** @hidden @deprecated */\n  context(type = \"2d\", attributes?: any) {\n    return this.getContext(type, attributes);\n  }\n\n  /** @hidden @deprecated */\n  canvas(legacyTextureDrawer: LegacyCanvasTextureDrawer) {\n    if (typeof legacyTextureDrawer === \"function\") {\n      legacyTextureDrawer.call(this, this.getContext());\n    } else if (typeof legacyTextureDrawer === \"undefined\") {\n      if (typeof this._drawer === \"function\") {\n        this._drawer.call(this);\n      }\n    }\n\n    return this;\n  }\n}\n\n/**\n * Create CanvasTexture (a texture with off-screen canvas).\n */\nexport function canvas(): CanvasTexture;\n\n/** @hidden @deprecated @internal */\nexport function canvas(drawer: LegacyCanvasTextureDrawer): CanvasTexture;\n\n/** @hidden @deprecated @internal */\nexport function canvas(type: string, drawer: LegacyCanvasTextureDrawer): CanvasTexture;\n\n/** @hidden @deprecated @internal */\nexport function canvas(\n  type: string,\n  attributes: Record<string, string>,\n  drawer: LegacyCanvasTextureDrawer,\n): CanvasTexture;\n\nexport function canvas(type?, attributes?, legacyTextureDrawer?): CanvasTexture {\n  if (typeof type === \"function\") {\n    const texture = new CanvasTexture();\n    legacyTextureDrawer = type;\n    texture.setDrawer(function () {\n      legacyTextureDrawer.call(texture, texture.getContext());\n    });\n    return texture;\n  } else if (typeof attributes === \"function\") {\n    const texture = new CanvasTexture();\n    legacyTextureDrawer = attributes;\n    texture.setDrawer(function () {\n      legacyTextureDrawer.call(texture, texture.getContext(type));\n    });\n    return texture;\n  } else if (typeof legacyTextureDrawer === \"function\") {\n    const texture = new CanvasTexture();\n    texture.setDrawer(function () {\n      legacyTextureDrawer.call(texture, texture.getContext(type, attributes));\n    });\n    return texture;\n  } else {\n    const texture = new CanvasTexture();\n    return texture;\n  }\n}\n\n/** @hidden @deprecated */\nexport function memoizeDraw(\n  legacySpriteDrawer: LegacyCanvasSpriteDrawer,\n  legacySpriteMemoizer: LegacyCanvasSpriteMemoizer = () => null,\n) {\n  const sprite = new Sprite();\n  const texture = new CanvasTexture();\n\n  sprite.texture(texture);\n\n  texture.setDrawer(function () {\n    legacySpriteDrawer(2.5 * texture._lastPixelRatio, texture, sprite);\n  });\n\n  texture.setMemoizer(legacySpriteMemoizer);\n\n  return sprite;\n}\n", "import { Vec2Value } from \"../common/matrix\";\n\nimport { Root, Viewport } from \"./root\";\nimport { Component } from \"./component\";\n\n// todo: capture mouse\n// todo: implement unmount\n\n// todo: replace this with single synthetic event names\nexport const POINTER_CLICK = \"click\";\nexport const POINTER_DOWN = \"touchstart mousedown\";\nexport const POINTER_MOVE = \"touchmove mousemove\";\nexport const POINTER_UP = \"touchend mouseup\";\nexport const POINTER_CANCEL = \"touchcancel mousecancel\";\n\n/** @hidden @deprecated */\nexport const POINTER_START = \"touchstart mousedown\";\n/** @hidden @deprecated */\nexport const POINTER_END = \"touchend mouseup\";\n\nclass EventPoint {\n  x: number;\n  y: number;\n\n  clone(obj?: Vec2Value) {\n    if (obj) {\n      obj.x = this.x;\n      obj.y = this.y;\n    } else {\n      obj = {\n        x: this.x,\n        y: this.y,\n      };\n    }\n    return obj;\n  }\n\n  toString() {\n    return (this.x | 0) + \"x\" + (this.y | 0);\n  }\n}\n\n// todo: make object readonly for users, to make it safe for reuse\nclass PointerSyntheticEvent {\n  x: number;\n  y: number;\n  readonly abs = new EventPoint();\n\n  raw: UIEvent;\n  type: string;\n  timeStamp: number;\n\n  clone(obj?: Vec2Value) {\n    if (obj) {\n      obj.x = this.x;\n      obj.y = this.y;\n    } else {\n      obj = {\n        x: this.x,\n        y: this.y,\n      };\n    }\n    return obj;\n  }\n\n  toString() {\n    return this.type + \": \" + (this.x | 0) + \"x\" + (this.y | 0);\n  }\n}\n\n/** @internal */\nclass VisitPayload {\n  type: string = \"\";\n  x: number = 0;\n  y: number = 0;\n  timeStamp: number = -1;\n  event: UIEvent = null;\n  root: Root = null;\n  collected: Component[] | null = null;\n  toString() {\n    return this.type + \": \" + (this.x | 0) + \"x\" + (this.y | 0);\n  }\n}\n\n// todo: define per pointer object? so that don't need to update root\n/** @internal */ const syntheticEvent = new PointerSyntheticEvent();\n\n/** @internal */ const PAYLOAD = new VisitPayload();\n\n/** @internal */\nexport class Pointer {\n  static DEBUG = false;\n  ratio = 1;\n\n  stage: Root;\n  elem: HTMLElement;\n\n  mount(stage: Root, elem: HTMLElement) {\n    this.stage = stage;\n    this.elem = elem;\n\n    this.ratio = stage.viewport().ratio || 1;\n    stage.on(\"viewport\", (viewport: Viewport) => {\n      this.ratio = viewport.ratio ?? this.ratio;\n    });\n\n    // `click` events are synthesized from start/end events on same components\n    // `mousecancel` events are synthesized on blur or mouseup outside element\n\n    elem.addEventListener(\"touchstart\", this.handleStart);\n    elem.addEventListener(\"touchend\", this.handleEnd);\n    elem.addEventListener(\"touchmove\", this.handleMove);\n    elem.addEventListener(\"touchcancel\", this.handleCancel);\n\n    elem.addEventListener(\"mousedown\", this.handleStart);\n    elem.addEventListener(\"mouseup\", this.handleEnd);\n    elem.addEventListener(\"mousemove\", this.handleMove);\n\n    document.addEventListener(\"mouseup\", this.handleCancel);\n    window.addEventListener(\"blur\", this.handleCancel);\n\n    return this;\n  }\n\n  unmount() {\n    const elem = this.elem;\n\n    elem.removeEventListener(\"touchstart\", this.handleStart);\n    elem.removeEventListener(\"touchend\", this.handleEnd);\n    elem.removeEventListener(\"touchmove\", this.handleMove);\n    elem.removeEventListener(\"touchcancel\", this.handleCancel);\n\n    elem.removeEventListener(\"mousedown\", this.handleStart);\n    elem.removeEventListener(\"mouseup\", this.handleEnd);\n    elem.removeEventListener(\"mousemove\", this.handleMove);\n\n    document.removeEventListener(\"mouseup\", this.handleCancel);\n    window.removeEventListener(\"blur\", this.handleCancel);\n\n    return this;\n  }\n\n  clickList: Component[] = [];\n  cancelList: Component[] = [];\n\n  handleStart = (event: TouchEvent | MouseEvent) => {\n    Pointer.DEBUG && console.debug && console.debug(\"pointer-start\", event.type);\n    event.preventDefault();\n    this.localPoint(event);\n    this.dispatchEvent(event.type, event);\n\n    this.findTargets(\"click\", this.clickList);\n    this.findTargets(\"mousecancel\", this.cancelList);\n  };\n\n  handleMove = (event: TouchEvent | MouseEvent) => {\n    event.preventDefault();\n    this.localPoint(event);\n    this.dispatchEvent(event.type, event);\n  };\n\n  handleEnd = (event: TouchEvent | MouseEvent) => {\n    event.preventDefault();\n    // up/end location is not available, last one is used instead\n    Pointer.DEBUG && console.debug && console.debug(\"pointer-end\", event.type);\n    this.dispatchEvent(event.type, event);\n\n    if (this.clickList.length) {\n      Pointer.DEBUG && console.debug && console.debug(\"pointer-click: \", event.type, this.clickList?.length);\n      this.dispatchEvent(\"click\", event, this.clickList);\n    }\n    this.cancelList.length = 0;\n  };\n\n  handleCancel = (event: TouchEvent | MouseEvent | FocusEvent) => {\n    if (this.cancelList.length) {\n      Pointer.DEBUG && console.debug && console.debug(\"pointer-cancel\", event.type, this.clickList?.length);\n      this.dispatchEvent(\"mousecancel\", event, this.cancelList);\n    }\n    this.clickList.length = 0;\n  };\n\n  /**\n   * Computer the location of the pointer event in the canvas coordination\n   */\n  localPoint(event: TouchEvent | MouseEvent) {\n    const elem = this.elem;\n    let x: number;\n    let y: number;\n    // pageX/Y if available?\n\n    if ((event as TouchEvent).touches?.length) {\n      x = (event as TouchEvent).touches[0].clientX;\n      y = (event as TouchEvent).touches[0].clientY;\n    } else {\n      x = (event as MouseEvent).clientX;\n      y = (event as MouseEvent).clientY;\n    }\n\n    const rect = elem.getBoundingClientRect();\n    x -= rect.left;\n    y -= rect.top;\n    x -= elem.clientLeft | 0;\n    y -= elem.clientTop | 0;\n\n    PAYLOAD.x = x * this.ratio;\n    PAYLOAD.y = y * this.ratio;\n  }\n\n  /**\n   * Find eligible target for and event type, used to keep trace components to dispatch click event\n   */\n  findTargets(type: string, result: Component[]) {\n    const payload = PAYLOAD;\n\n    payload.type = type;\n    payload.root = this.stage;\n    payload.event = null;\n    payload.collected = result;\n    payload.collected.length = 0;\n\n    this.stage.visit(\n      {\n        reverse: true,\n        visible: true,\n        start: this.visitStart,\n        end: this.visitEnd,\n      },\n      payload,\n    );\n  }\n\n  dispatchEvent(type: string, event: UIEvent, targets?: Component[]) {\n    const payload = PAYLOAD;\n\n    payload.type = type;\n    payload.root = this.stage;\n    payload.event = event;\n    payload.timeStamp = Date.now();\n    payload.collected = null;\n\n    if (type !== \"mousemove\" && type !== \"touchmove\") {\n      Pointer.DEBUG && console.debug && console.debug(\"pointer:dispatchEvent\", payload, targets?.length);\n    }\n\n    if (targets) {\n      while (targets.length) {\n        const component = targets.shift();\n        if (this.visitEnd(component, payload)) {\n          break;\n        }\n      }\n      targets.length = 0;\n    } else {\n      this.stage.visit(\n        {\n          reverse: true,\n          visible: true,\n          start: this.visitStart,\n          end: this.visitEnd,\n        },\n        payload,\n      );\n    }\n  }\n\n  visitStart = (component: Component, payload: VisitPayload) => {\n    return !component._flag(payload.type);\n  };\n\n  visitEnd = (component: Component, payload: VisitPayload) => {\n    // mouse: event/collect, type, root\n    syntheticEvent.raw = payload.event;\n    syntheticEvent.type = payload.type;\n    syntheticEvent.timeStamp = payload.timeStamp;\n    syntheticEvent.abs.x = payload.x;\n    syntheticEvent.abs.y = payload.y;\n\n    const listeners = component.listeners(payload.type);\n    if (!listeners) {\n      return;\n    }\n\n    component.matrix().inverse().map(payload, syntheticEvent);\n\n    // deep flags are used to decide to pass down event, and spy is not used for that\n    // we use spy to decide if an event should be delivered to elements that do not have hitTest\n    // todo: collect and pass hitTest result upward instead, probably use visit payload\n    const isEventTarget = component === payload.root || component.attr(\"spy\") || component.hitTest(syntheticEvent);\n    if (!isEventTarget) {\n      return;\n    }\n\n    if (payload.collected) {\n      payload.collected.push(component);\n    }\n\n    // todo: when this condition is false?\n    if (payload.event) {\n      // todo: use a function call to cancel processing events, like dom\n      let stop = false;\n      for (let l = 0; l < listeners.length; l++) {\n        stop = listeners[l].call(component, syntheticEvent) ? true : stop;\n      }\n      return stop;\n    }\n  };\n}\n\n/** @hidden @deprecated @internal */\nexport const Mouse = {\n  CLICK: \"click\",\n  START: \"touchstart mousedown\",\n  MOVE: \"touchmove mousemove\",\n  END: \"touchend mouseup\",\n  CANCEL: \"touchcancel mousecancel\",\n};\n", "import stats from \"../common/stats\";\nimport { Matrix } from \"../common/matrix\";\n\nimport { Component } from \"./component\";\nimport { Pointer } from \"./pointer\";\nimport { FitMode, isValidFitMode } from \"./pin\";\n\n/** @internal */ const ROOTS: Root[] = [];\n\nexport function pause() {\n  for (let i = ROOTS.length - 1; i >= 0; i--) {\n    ROOTS[i].pause();\n  }\n}\n\nexport function resume() {\n  for (let i = ROOTS.length - 1; i >= 0; i--) {\n    ROOTS[i].resume();\n  }\n}\n\nexport function mount(configs: RootConfig = {}) {\n  const root = new Root();\n  // todo: root.use(new Pointer());\n  root.mount(configs);\n  // todo: maybe just pass root? or do root.use(pointer)\n  root.pointer = new Pointer().mount(root, root.dom as HTMLElement);\n  return root;\n}\n\ntype RootConfig = {\n  canvas?: string | HTMLCanvasElement;\n};\n\n/**\n * Geometry of the rectangular that the application takes on the screen.\n */\nexport type Viewport = {\n  width: number;\n  height: number;\n  ratio: number;\n};\n\n/**\n * Geometry of a rectangular portion of the game that is projected on the screen.\n */\nexport type Viewbox = {\n  x?: number;\n  y?: number;\n  width: number;\n  height: number;\n  mode?: FitMode;\n};\n\nexport class Root extends Component {\n  canvas: HTMLCanvasElement | null = null;\n  dom: HTMLCanvasElement | null = null;\n  context: CanvasRenderingContext2D | null = null;\n\n  /** @internal */ pixelWidth = -1;\n  /** @internal */ pixelHeight = -1;\n  /** @internal */ pixelRatio = 1;\n  /** @internal */ drawingWidth = 0;\n  /** @internal */ drawingHeight = 0;\n\n  mounted = false;\n  paused = false;\n  sleep = false;\n\n  /** @internal */ devicePixelRatio: number;\n  /** @internal */ backingStoreRatio: number;\n\n  /** @internal */ pointer: Pointer;\n\n  /** @internal */ _viewport: Viewport;\n  /** @internal */ _viewbox: Viewbox;\n  /** @internal */ _camera: Matrix;\n\n  constructor() {\n    super();\n    this.label(\"Root\");\n  }\n\n  mount = (configs: RootConfig = {}) => {\n    if (typeof configs.canvas === \"string\") {\n      this.canvas = document.getElementById(configs.canvas) as HTMLCanvasElement;\n      if (!this.canvas) {\n        console.error(\"Canvas element not found: \", configs.canvas);\n      }\n    } else if (configs.canvas instanceof HTMLCanvasElement) {\n      this.canvas = configs.canvas;\n    } else if (configs.canvas) {\n      console.error(\"Unknown value for canvas:\", configs.canvas);\n    }\n\n    if (!this.canvas) {\n      this.canvas = (document.getElementById(\"cutjs\") ||\n        document.getElementById(\"stage\")) as HTMLCanvasElement;\n    }\n\n    if (!this.canvas) {\n      console.debug && console.debug(\"Creating canvas element...\");\n      this.canvas = document.createElement(\"canvas\");\n      Object.assign(this.canvas.style, {\n        position: \"absolute\",\n        display: \"block\",\n        top: \"0\",\n        left: \"0\",\n        bottom: \"0\",\n        right: \"0\",\n        width: \"100%\",\n        height: \"100%\",\n      });\n\n      const body = document.body;\n      body.insertBefore(this.canvas, body.firstChild);\n    }\n\n    this.dom = this.canvas;\n\n    this.context = this.canvas.getContext(\"2d\");\n\n    this.devicePixelRatio = window.devicePixelRatio || 1;\n    this.backingStoreRatio =\n      this.context[\"webkitBackingStorePixelRatio\"] ||\n      this.context[\"mozBackingStorePixelRatio\"] ||\n      this.context[\"msBackingStorePixelRatio\"] ||\n      this.context[\"oBackingStorePixelRatio\"] ||\n      this.context[\"backingStorePixelRatio\"] ||\n      1;\n\n    this.pixelRatio = this.devicePixelRatio / this.backingStoreRatio;\n\n    // resize();\n    // window.addEventListener('resize', resize, false);\n    // window.addEventListener('orientationchange', resize, false);\n\n    this.mounted = true;\n    ROOTS.push(this);\n    this.requestFrame();\n  };\n\n  /** @internal */ frameRequested = false;\n\n  /** @internal */\n  requestFrame = () => {\n    // one request at a time\n    if (!this.frameRequested) {\n      this.frameRequested = true;\n      requestAnimationFrame(this.onFrame);\n    }\n  };\n\n  /** @internal */ _lastFrameTime = 0;\n  /** @internal */ _mo_touch: number | null = null; // monitor touch\n\n  /** @internal */\n  onFrame = (now: number) => {\n    this.frameRequested = false;\n\n    if (!this.mounted || !this.canvas || !this.context) {\n      return;\n    }\n\n    this.requestFrame();\n\n    const newPixelWidth = this.canvas.clientWidth;\n    const newPixelHeight = this.canvas.clientHeight;\n\n    if (this.pixelWidth !== newPixelWidth || this.pixelHeight !== newPixelHeight) {\n      // viewport pixel size is not the same as last time\n      this.pixelWidth = newPixelWidth;\n      this.pixelHeight = newPixelHeight;\n\n      this.drawingWidth = newPixelWidth * this.pixelRatio;\n      this.drawingHeight = newPixelHeight * this.pixelRatio;\n\n      if (this.canvas.width !== this.drawingWidth || this.canvas.height !== this.drawingHeight) {\n        // canvas size doesn't math\n        this.canvas.width = this.drawingWidth;\n        this.canvas.height = this.drawingHeight;\n\n        console.debug && console.debug(\n            \"Resize: [\" +\n              this.drawingWidth +\n              \", \" +\n              this.drawingHeight +\n              \"] = \" +\n              this.pixelRatio +\n              \" x [\" +\n              this.pixelWidth +\n              \", \" +\n              this.pixelHeight +\n              \"]\",\n          );\n\n        this.viewport({\n          width: this.drawingWidth,\n          height: this.drawingHeight,\n          ratio: this.pixelRatio,\n        });\n      }\n    }\n\n    const last = this._lastFrameTime || now;\n    const elapsed = now - last;\n\n    if (!this.mounted || this.paused || this.sleep) {\n      return;\n    }\n\n    this._lastFrameTime = now;\n\n    this.prerender();\n\n    const tickRequest = this._tick(elapsed, now, last);\n    if (this._mo_touch != this._ts_touch) {\n      // something changed since last call\n      this._mo_touch = this._ts_touch;\n      this.sleep = false;\n\n      if (this.drawingWidth > 0 && this.drawingHeight > 0) {\n        this.context.setTransform(1, 0, 0, 1, 0, 0);\n        this.context.clearRect(0, 0, this.drawingWidth, this.drawingHeight);  \n        if (this.debugDrawAxis > 0) {\n          this.renderDebug(this.context);\n        }\n        this.render(this.context);\n      }\n    } else if (tickRequest) {\n      // nothing changed, but a component requested next tick\n      this.sleep = false;\n    } else {\n      // nothing changed, and no component requested next tick\n      this.sleep = true;\n    }\n\n    stats.fps = elapsed ? 1000 / elapsed : 0;\n  };\n\n  /** @hidden */\n  debugDrawAxis = 0;\n\n  private renderDebug(context: CanvasRenderingContext2D): void {\n    const size = typeof this.debugDrawAxis === \"number\" ? this.debugDrawAxis : 10;\n    const m = this.matrix();\n    context.setTransform(m.a, m.b, m.c, m.d, m.e, m.f);\n    const lineWidth = 3 / m.a;\n\n    context.beginPath();\n    context.moveTo(0, 0);\n    context.lineTo(0, 0.8 * size);\n    context.lineTo(-0.2 * size, 0.8 * size);\n    context.lineTo(0, size);\n    context.lineTo(+0.2 * size, 0.8 * size);\n    context.lineTo(0, 0.8 * size);\n    context.strokeStyle = 'rgba(93, 173, 226)';\n    context.lineJoin = \"round\";\n    context.lineCap = \"round\";\n    context.lineWidth = lineWidth;\n    context.stroke();\n\n    context.beginPath();\n    context.moveTo(0, 0);\n    context.lineTo(0.8 * size, 0);\n    context.lineTo(0.8 * size, -0.2 * size);\n    context.lineTo(size, 0);\n    context.lineTo(0.8 * size, +0.2 * size);\n    context.lineTo(0.8 * size, 0);\n    context.strokeStyle = 'rgba(236, 112, 99)';\n    context.lineJoin = \"round\";\n    context.lineCap = \"round\";\n    context.lineWidth = lineWidth;\n    context.stroke();\n  }\n\n  resume() {\n    if (this.sleep || this.paused) {\n      this.requestFrame();\n    }\n    this.paused = false;\n    this.sleep = false;\n    this.publish(\"resume\");\n    return this;\n  }\n\n  pause() {\n    if (!this.paused) {\n      this.publish(\"pause\");\n    }\n    this.paused = true;\n    return this;\n  }\n\n  /** @internal */\n  touch() {\n    if (this.sleep || this.paused) {\n      this.requestFrame();\n    }\n    this.sleep = false;\n    return super.touch();\n  }\n\n  unmount() {\n    this.mounted = false;\n    const index = ROOTS.indexOf(this);\n    if (index >= 0) {\n      ROOTS.splice(index, 1);\n    }\n\n    this.pointer?.unmount();\n    return this;\n  }\n\n  background(color: string) {\n    if (this.dom) {\n      this.dom.style.backgroundColor = color;\n    }\n    return this;\n  }\n\n  /**\n   * Set/Get viewport.\n   * This is used along with viewbox to determine the scale and position of the viewbox within the viewport.\n   * Viewport is the size of the container, for example size of the canvas element.\n   * Viewbox is provided by the user, and is the ideal size of the content.\n   */\n  viewport(): Viewport;\n  viewport(width: number, height: number, ratio?: number): this;\n  viewport(viewbox: Viewport): this;\n  viewport(width?: number | Viewport, height?: number, ratio?: number) {\n    if (typeof width === \"undefined\") {\n      // todo: return readonly object instead\n      return Object.assign({}, this._viewport);\n    }\n\n    if (typeof width === \"object\") {\n      const options = width;\n      width = options.width;\n      height = options.height;\n      ratio = options.ratio;\n    }\n\n    if (typeof width === \"number\" && typeof height === \"number\") {\n      this._viewport = {\n        width: width,\n        height: height,\n        ratio: typeof ratio === \"number\" ? ratio : 1,\n      };\n      this.viewbox();\n      const data = Object.assign({}, this._viewport);\n      this.visit({\n        start: function (component) {\n          if (!component._flag(\"viewport\")) {\n            return true;\n          }\n          component.publish(\"viewport\", [data]);\n        },\n      });\n    }\n\n    return this;\n  }\n\n  /**\n   * Set viewbox.\n   */\n  viewbox(viewbox: Viewbox): this;\n  viewbox(width?: number, height?: number, mode?: FitMode): this;\n  viewbox(width?: number | Viewbox, height?: number, mode?: FitMode): this {\n    // TODO: static/fixed viewbox\n    // TODO: use css object-fit values\n    if (typeof width === \"number\" && typeof height === \"number\") {\n      this._viewbox = {\n        width,\n        height,\n        mode,\n      };\n    } else if (typeof width === \"object\" && width !== null) {\n      this._viewbox = {\n        ...width,\n      };\n    }\n\n    this.rescale();\n\n    return this;\n  }\n\n  camera(matrix: Matrix) {\n    this._camera = matrix;\n    this.rescale();\n    return this;\n  }\n\n  /** @internal */\n  rescale() {\n    const viewbox = this._viewbox;\n    const viewport = this._viewport;\n    const camera = this._camera;\n    if (viewport && viewbox) {\n      const viewportWidth = viewport.width;\n      const viewportHeight = viewport.height;\n      const viewboxMode = isValidFitMode(viewbox.mode) ? viewbox.mode : \"in-pad\";\n      const viewboxWidth = viewbox.width;\n      const viewboxHeight = viewbox.height;\n\n      this.pin({\n        width: viewboxWidth,\n        height: viewboxHeight,\n      });\n      this.fit(viewportWidth, viewportHeight, viewboxMode);\n\n      const viewboxX = viewbox.x || 0;\n      const viewboxY = viewbox.y || 0;\n\n      const cameraZoomX = camera?.a || 1;\n      const cameraZoomY = camera?.d || 1;\n      const cameraX = camera?.e || 0;\n      const cameraY = camera?.f || 0;\n\n      const scaleX = this.pin(\"scaleX\");\n      const scaleY = this.pin(\"scaleY\");\n\n      this.pin(\"scaleX\", scaleX * cameraZoomX);\n      this.pin(\"scaleY\", scaleY * cameraZoomY);\n\n      this.pin(\"offsetX\", cameraX - viewboxX * scaleX * cameraZoomX);\n      this.pin(\"offsetY\", cameraY - viewboxY * scaleY * cameraZoomY);\n    } else if (viewport) {\n      this.pin({\n        width: viewport.width,\n        height: viewport.height,\n      });\n    }\n\n    return this;\n  }\n\n  /** @hidden */\n  flipX(x: boolean) {\n    this._pin._directionX = x ? -1 : 1;\n    return this;\n  }\n\n  /** @hidden */\n  flipY(y: boolean) {\n    this._pin._directionY = y ? -1 : 1;\n    return this;\n  }\n}\n", "import { math } from \"../common/math\";\nimport { Texture, TextureSelectionInputArray, texture } from \"../texture\";\n\nimport { Component } from \"./component\";\n\nexport function anim(frames: string | TextureSelectionInputArray, fps?: number) {\n  const anim = new Anim();\n  anim.frames(frames).gotoFrame(0);\n  fps && anim.fps(fps);\n  return anim;\n}\n\n// TODO: replace with atlas fps or texture time\n/** @internal */ const FPS = 15;\n\nexport class Anim extends Component {\n  /** @internal */ _texture: Texture | null = null;\n\n  /** @internal */ _frames: Texture[] = [];\n\n  /** @internal */ _fps: number;\n  /** @internal */ _ft: number;\n\n  /** @internal */ _time: number = -1;\n  /** @internal */ _repeat: number = 0;\n  /** @internal */ _index: number = 0;\n\n  /** @internal */ _callback: () => void;\n\n  constructor() {\n    super();\n    this.label(\"Anim\");\n\n    this._fps = FPS;\n    this._ft = 1000 / this._fps;\n\n    this.tick(this._animTick, false);\n  }\n\n  /** @hidden */\n  renderTexture(context: CanvasRenderingContext2D) {\n    if (!this._texture) return;\n\n    this._texture.draw(context);\n  }\n\n  /** @internal */\n  private _animTickLastTime = 0;\n  /** @internal */\n  private _animTick = (t: number, now: number, last: number) => {\n    if (this._time < 0 || this._frames.length <= 1) {\n      return;\n    }\n\n    // ignore old elapsed\n    const ignore = this._animTickLastTime != last;\n    this._animTickLastTime = now;\n    if (ignore) {\n      return true;\n    }\n\n    this._time += t;\n    if (this._time < this._ft) {\n      return true;\n    }\n    const n = (this._time / this._ft) | 0;\n    this._time -= n * this._ft;\n    this.moveFrame(n);\n    if (this._repeat > 0 && (this._repeat -= n) <= 0) {\n      this.stop();\n      this._callback && this._callback();\n      return false;\n    }\n    return true;\n  };\n\n  fps(fps?: number) {\n    if (typeof fps === \"undefined\") {\n      return this._fps;\n    }\n    this._fps = fps > 0 ? fps : FPS;\n    this._ft = 1000 / this._fps;\n    return this;\n  }\n\n  /** @deprecated Use frames */\n  setFrames(frames: string | TextureSelectionInputArray) {\n    return this.frames(frames);\n  }\n\n  frames(frames: string | TextureSelectionInputArray) {\n    this._index = 0;\n    this._frames = texture(frames).array();\n    this.touch();\n    return this;\n  }\n\n  length() {\n    return this._frames ? this._frames.length : 0;\n  }\n\n  gotoFrame(frame: number, resize = false) {\n    this._index = math.wrap(frame, this._frames.length) | 0;\n    resize = resize || !this._texture;\n    this._texture = this._frames[this._index];\n    if (resize) {\n      this.pin(\"width\", this._texture.getWidth());\n      this.pin(\"height\", this._texture.getHeight());\n    }\n    this.touch();\n    return this;\n  }\n\n  moveFrame(move: number) {\n    return this.gotoFrame(this._index + move);\n  }\n\n  repeat(repeat: number, callback?: () => void) {\n    this._repeat = repeat * this._frames.length - 1;\n    this._callback = callback;\n    this.play();\n    return this;\n  }\n\n  play(frame?: number) {\n    if (typeof frame !== \"undefined\") {\n      this.gotoFrame(frame);\n      this._time = 0;\n    } else if (this._time < 0) {\n      this._time = 0;\n    }\n\n    this.touch();\n    return this;\n  }\n\n  stop(frame?: number) {\n    this._time = -1;\n    if (typeof frame !== \"undefined\") {\n      this.gotoFrame(frame);\n    }\n    return this;\n  }\n}\n", "import { Texture, texture } from \"../texture\";\n\nimport { Component } from \"./component\";\n\nexport function monotype(chars: string | Record<string, Texture> | ((char: string) => Texture)) {\n  return new Monotype().frames(chars);\n}\n\nexport class Monotype extends Component {\n  /** @internal */ _textures: Texture[] = [];\n\n  /** @internal */ _font: (value: string) => Texture;\n  /** @internal */ _value: string | number | string[] | number[];\n\n  constructor() {\n    super();\n    this.label(\"Monotype\");\n  }\n\n  /** @hidden */\n  renderTexture(context: CanvasRenderingContext2D) {\n    if (!this._textures || !this._textures.length) return;\n\n    for (let i = 0, n = this._textures.length; i < n; i++) {\n      this._textures[i].draw(context);\n    }\n  }\n\n  /** @deprecated Use frames */\n  setFont(frames: string | Record<string, Texture> | ((char: string) => Texture)) {\n    return this.frames(frames);\n  }\n\n  frames(frames: string | Record<string, Texture> | ((char: string) => Texture)) {\n    this._textures = [];\n    if (typeof frames == \"string\") {\n      const selection = texture(frames);\n      this._font = function (value: string) {\n        return selection.one(value);\n      };\n    } else if (typeof frames === \"object\") {\n      this._font = function (value: string) {\n        return frames[value];\n      };\n    } else if (typeof frames === \"function\") {\n      this._font = frames;\n    }\n    return this;\n  }\n\n  /** @deprecated Use value */\n  setValue(value: string | number | string[] | number[]) {\n    return this.value(value);\n  }\n\n  value(value: string | number | string[] | number[]): this;\n  value(value: string | number | string[] | number[]) {\n    if (typeof value === \"undefined\") {\n      return this._value;\n    }\n    if (this._value === value) {\n      return this;\n    }\n    this._value = value;\n\n    if (value === null) {\n      value = \"\";\n    } else if (typeof value !== \"string\" && !Array.isArray(value)) {\n      value = value.toString();\n    }\n\n    this._spacing = this._spacing || 0;\n\n    let width = 0;\n    let height = 0;\n    for (let i = 0; i < value.length; i++) {\n      const v = value[i];\n      const texture = (this._textures[i] = this._font(typeof v === \"string\" ? v : v + \"\"));\n      width += i > 0 ? this._spacing : 0;\n      texture.setDestinationCoordinate(width, 0);\n      width = width + texture.getWidth();\n      height = Math.max(height, texture.getHeight());\n    }\n    this.pin(\"width\", width);\n    this.pin(\"height\", height);\n    this._textures.length = value.length;\n    return this;\n  }\n}\n\n/** @hidden @deprecated */\nexport { monotype as string };\n/** @hidden @deprecated */\nexport { Monotype as Str };\n"], "names": ["Matrix", "d", "b", "__assign", "Texture", "ImageTexture", "PipeTexture", "texture", "Atlas", "def", "TextureSelection", "atlas", "ResizableTexture", "iid", "<PERSON>n", "Easing", "Transition", "component", "Component", "sprite", "Sprite", "CanvasTexture", "EventPoint", "PointerSyntheticEvent", "VisitPayload", "Pointer", "Root", "anim", "<PERSON><PERSON>", "Monotype"], "mappings": ";;;;;AAAiB,IAAM,cAAc,KAAK;AACzB,IAAM,YAAY,KAAK;AAGxB,SAAA,OAAO,KAAc,KAAY;AAC3C,MAAA,OAAO,QAAQ,aAAa;AACxB,UAAA;AACA,UAAA;AAAA,EAAA,WACG,OAAO,QAAQ,aAAa;AAC/B,UAAA;AACA,UAAA;AAAA,EAAA;AAER,SAAO,OAAO,MAAM,MAAM,YAAa,KAAI,MAAM,OAAO;AAC1D;AAGgB,SAAA,KAAK,KAAa,KAAc,KAAY;AACtD,MAAA,OAAO,QAAQ,aAAa;AACxB,UAAA;AACA,UAAA;AAAA,EAAA,WACG,OAAO,QAAQ,aAAa;AAC/B,UAAA;AACA,UAAA;AAAA,EAAA;AAER,MAAI,MAAM,KAAK;AACN,WAAA,MAAM,QAAQ,MAAM;AACpB,WAAA,OAAO,MAAM,IAAI,MAAM;AAAA,EAAA,OACzB;AACE,WAAA,MAAM,QAAQ,MAAM;AACpB,WAAA,OAAO,OAAO,IAAI,MAAM;AAAA,EAAA;AAEnC;AAGgB,SAAA,MAAM,KAAa,KAAa,KAAW;AACzD,MAAI,MAAM,KAAK;AACN,WAAA;AAAA,EAAA,WACE,MAAM,KAAK;AACb,WAAA;AAAA,EAAA,OACF;AACE,WAAA;AAAA,EAAA;AAEX;AAGgB,SAAA,OAAO,GAAW,GAAS;AACzC,SAAO,UAAU,IAAI,IAAI,IAAI,CAAC;AAChC;AAEa,IAAA,OAAO,OAAO,OAAO,IAAI;AAEtC,KAAK,SAAS;AACd,KAAK,OAAO;AACZ,KAAK,QAAQ;AACb,KAAK,SAAS;AAGd,KAAK,SAAS;AAEd,KAAK,QAAQ;AC7Cb,IAAA;AAAA;AAAA,EAAA,WAAA;AAkBE,aACEA,QAAA,GACA,GACA,GACA,GACA,GACA,GAAU;AAtBZ,WAAC,IAAG;AACJ,WAAC,IAAG;AACJ,WAAC,IAAG;AAEJ,WAAC,IAAG;AAEJ,WAAC,IAAG;AAEJ,WAAC,IAAG;AAgBE,UAAA,OAAO,MAAM,UAAU;AACzB,aAAK,MAAM,CAAC;AAAA,MAAA,OACP;AACL,aAAK,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,MAAA;AAAA,IAC7B;AAGFA,YAAA,UAAA,WAAA,WAAA;AACE,aACE,MACA,KAAK,IACL,OACA,KAAK,IACL,OACA,KAAK,IACL,OACA,KAAK,IACL,OACA,KAAK,IACL,OACA,KAAK,IACL;AAAA,IAEJ;AAEAA,YAAA,UAAA,QAAA,WAAA;AACE,aAAO,IAAIA,QAAO,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AAAA,IAClE;AAIAA,YAAA,UAAA,QAAA,SACE,GACA,GACA,GACA,GACA,GACA,GAAU;AAEV,WAAK,SAAS;AACV,UAAA,OAAO,MAAM,UAAU;AACzB,aAAK,IAAI,EAAE;AACX,aAAK,IAAI,EAAE;AACX,aAAK,IAAI,EAAE;AACX,aAAK,IAAI,EAAE;AACX,aAAK,IAAI,EAAE;AACX,aAAK,IAAI,EAAE;AAAA,MAAA,OACN;AACL,aAAK,IAAI,OAAO,MAAM,WAAW,IAAI;AACrC,aAAK,IAAI,OAAO,MAAM,WAAW,IAAI;AACrC,aAAK,IAAI,OAAO,MAAM,WAAW,IAAI;AACrC,aAAK,IAAI,OAAO,MAAM,WAAW,IAAI;AACrC,aAAK,IAAI,OAAO,MAAM,WAAW,IAAI;AACrC,aAAK,IAAI,OAAO,MAAM,WAAW,IAAI;AAAA,MAAA;AAEhC,aAAA;AAAA,IACT;AAEAA,YAAA,UAAA,WAAA,WAAA;AACE,WAAK,SAAS;AACd,WAAK,IAAI;AACT,WAAK,IAAI;AACT,WAAK,IAAI;AACT,WAAK,IAAI;AACT,WAAK,IAAI;AACT,WAAK,IAAI;AACF,aAAA;AAAA,IACT;AAEAA,YAAM,UAAA,SAAN,SAAO,OAAa;AAClB,UAAI,CAAC,OAAO;AACH,eAAA;AAAA,MAAA;AAGT,WAAK,SAAS;AAEd,UAAM,IAAI,QAAQ,KAAK,IAAI,KAAK,IAAI;AAEpC,UAAM,IAAI,QAAQ,KAAK,IAAI,KAAK,IAAI;AAEpC,UAAM,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK;AAChC,UAAM,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK;AAChC,UAAM,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK;AAChC,UAAM,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK;AAChC,UAAM,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK;AAChC,UAAM,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK;AAEhC,WAAK,IAAI;AACT,WAAK,IAAI;AACT,WAAK,IAAI;AACT,WAAK,IAAI;AACT,WAAK,IAAI;AACT,WAAK,IAAI;AAEF,aAAA;AAAA,IACT;AAEAA,YAAA,UAAA,YAAA,SAAU,GAAW,GAAS;AACxB,UAAA,CAAC,KAAK,CAAC,GAAG;AACL,eAAA;AAAA,MAAA;AAET,WAAK,SAAS;AACd,WAAK,KAAK;AACV,WAAK,KAAK;AACH,aAAA;AAAA,IACT;AAEAA,YAAA,UAAA,QAAA,SAAM,GAAW,GAAS;AACxB,UAAI,EAAE,IAAI,MAAM,EAAE,IAAI,IAAI;AACjB,eAAA;AAAA,MAAA;AAET,WAAK,SAAS;AACd,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AACH,aAAA;AAAA,IACT;AAEAA,YAAA,UAAA,OAAA,SAAK,GAAW,GAAS;AACnB,UAAA,CAAC,KAAK,CAAC,GAAG;AACL,eAAA;AAAA,MAAA;AAET,WAAK,SAAS;AAEd,UAAM,IAAI,KAAK,IAAI,KAAK,IAAI;AAC5B,UAAM,IAAI,KAAK,IAAI,KAAK,IAAI;AAC5B,UAAM,IAAI,KAAK,IAAI,KAAK,IAAI;AAC5B,UAAM,IAAI,KAAK,IAAI,KAAK,IAAI;AAC5B,UAAM,IAAI,KAAK,IAAI,KAAK,IAAI;AAC5B,UAAM,IAAI,KAAK,IAAI,KAAK,IAAI;AAE5B,WAAK,IAAI;AACT,WAAK,IAAI;AACT,WAAK,IAAI;AACT,WAAK,IAAI;AACT,WAAK,IAAI;AACT,WAAK,IAAI;AACF,aAAA;AAAA,IACT;AAEAA,YAAM,UAAA,SAAN,SAAO,GAAc;AACnB,WAAK,SAAS;AAEd,UAAM,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;AACpC,UAAM,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;AACpC,UAAM,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;AACpC,UAAM,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;AAC9B,UAAA,IAAI,KAAK,IAAI,EAAE,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;AACpC,UAAA,IAAI,KAAK,IAAI,EAAE,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;AAE1C,WAAK,IAAI;AACT,WAAK,IAAI;AACT,WAAK,IAAI;AACT,WAAK,IAAI;AACT,WAAK,IAAI;AACT,WAAK,IAAI;AAEF,aAAA;AAAA,IACT;AAEAA,YAAA,UAAA,UAAA,WAAA;AACE,UAAI,KAAK,QAAQ;AACf,aAAK,SAAS;AACV,YAAA,CAAC,KAAK,UAAU;AACb,eAAA,WAAW,IAAIA;;AAItB,YAAM,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK;AACrC,aAAA,SAAS,IAAI,KAAK,IAAI;AAC3B,aAAK,SAAS,IAAI,CAAC,KAAK,IAAI;AAC5B,aAAK,SAAS,IAAI,CAAC,KAAK,IAAI;AACvB,aAAA,SAAS,IAAI,KAAK,IAAI;AACtB,aAAA,SAAS,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK;AACnD,aAAA,SAAS,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK;AAAA,MAAA;AAE1D,aAAO,KAAK;AAAA,IACd;AAEAA,YAAA,UAAA,MAAA,SAAI,GAAc,GAAa;AAC7B,UAAI,KAAK,EAAE,GAAG,GAAG,GAAG,EAAC;AACnB,QAAA,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK;AACvC,QAAA,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK;AAClC,aAAA;AAAA,IACT;AAEAA,YAAA,UAAA,OAAA,SAAK,GAAuB,GAAU;AAChC,UAAA,OAAO,MAAM,UAAU;AACzB,YAAI,EAAE;AACN,YAAI,EAAE;AAAA,MAAA;AAER,aAAO,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK;AAAA,IACxC;AAEAA,YAAA,UAAA,OAAA,SAAK,GAAuB,GAAU;AAChC,UAAA,OAAO,MAAM,UAAU;AACzB,YAAI,EAAE;AACN,YAAI,EAAE;AAAA,MAAA;AAER,aAAO,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK;AAAA,IACxC;AACDA,WAAAA;AAAAA,EAAA,EAAA;AAAA;ACpPD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA,IAAI,gBAAgB,SAAS,GAAG,GAAG;AAC/B,kBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAA,eAAgB,SAAS,SAAUC,IAAGC,IAAG;AAAE,IAAAD,GAAE,YAAYC;AAAA,EAAE,KACzE,SAAUD,IAAGC,IAAG;AAAE,aAAS,KAAKA,GAAG,KAAIA,GAAE,eAAe,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA;AAC1E,SAAO,cAAc,GAAG,CAAC;AAC7B;AAEO,SAAS,UAAU,GAAG,GAAG;AAC5B,gBAAc,GAAG,CAAC;AAClB,WAAS,KAAK;AAAE,SAAK,cAAc;AAAA,EAAI;AACvC,IAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAI;AACvF;AAEO,IAAI,WAAW,WAAW;AAC7B,aAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC9E;AACD,WAAO;AAAA,EACV;AACD,SAAO,SAAS,MAAM,MAAM,SAAS;AACzC;AA6BO,SAAS,UAAU,SAAS,YAAY,GAAG,WAAW;AACzD,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAE,CAAE;AAAA,EAAI;AAC5G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAE,SAAU,GAAG;AAAE,eAAO,CAAC;AAAA;IAAM;AAC3F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAI,SAAQ,GAAG;AAAE,eAAO,CAAC;AAAA;IAAM;AAC9F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAI;AAC9G,UAAM,YAAY,UAAU,MAAM,SAAuB,CAAE,CAAA,GAAG,KAAI,CAAE;AAAA,EAC5E,CAAK;AACL;AAEO,SAAS,YAAY,SAAS,MAAM;AACvC,MAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,QAAI,EAAE,CAAC,IAAI,EAAG,OAAM,EAAE,CAAC;AAAG,WAAO,EAAE,CAAC;AAAA,EAAI,GAAE,MAAM,CAAE,GAAE,KAAK,CAAA,EAAI,GAAE,GAAG,GAAG,GAAG;AAC/G,SAAO,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAG,GAAE,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,WAAO;AAAA,EAAO,IAAG;AACvJ,WAAS,KAAK,GAAG;AAAE,WAAO,SAAU,GAAG;AAAE,aAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,IAAI;AAAA,EAAG;AAClE,WAAS,KAAK,IAAI;AACd,QAAI,EAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,WAAO,EAAG,KAAI;AACV,UAAI,IAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,KAAM,QAAO;AAC3J,UAAI,IAAI,GAAG,EAAG,MAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,cAAQ,GAAG,CAAC,GAAC;AAAA,QACT,KAAK;AAAA,QAAG,KAAK;AAAG,cAAI;AAAI;AAAA,QACxB,KAAK;AAAG,YAAE;AAAS,iBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAK;AAAA,QACrD,KAAK;AAAG,YAAE;AAAS,cAAI,GAAG,CAAC;AAAG,eAAK,CAAC,CAAC;AAAG;AAAA,QACxC,KAAK;AAAG,eAAK,EAAE,IAAI;AAAO,YAAE,KAAK,IAAG;AAAI;AAAA,QACxC;AACI,cAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,gBAAI;AAAG;AAAA,UAAW;AAC5G,cAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,cAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,UAAQ;AACtF,cAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,cAAE,QAAQ,EAAE,CAAC;AAAG,gBAAI;AAAI;AAAA,UAAQ;AACrE,cAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,cAAE,QAAQ,EAAE,CAAC;AAAG,cAAE,IAAI,KAAK,EAAE;AAAG;AAAA,UAAQ;AACnE,cAAI,EAAE,CAAC,EAAG,GAAE,IAAI,IAAG;AACnB,YAAE,KAAK,IAAK;AAAE;AAAA,MACrB;AACD,WAAK,KAAK,KAAK,SAAS,CAAC;AAAA,IAC5B,SAAQ,GAAG;AAAE,WAAK,CAAC,GAAG,CAAC;AAAG,UAAI;AAAA,IAAE,UAAW;AAAE,UAAI,IAAI;AAAA,IAAI;AAC1D,QAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AAAG,WAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM;EAC7E;AACL;ACtGA,IAAM,iBAAiB,OAAO,UAAU;AAGlC,SAAU,KAAK,OAAU;AACvB,MAAA,MAAM,eAAe,KAAK,KAAK;AACrC,SACE,QAAQ,uBACR,QAAQ,gCACR,QAAQ;AAEZ;AAGM,SAAU,OAAO,OAAU;AAC/B,SAAO,eAAe,KAAK,KAAK,MAAM,qBAAqB,MAAM,gBAAgB;AAEnF;AChBA,MAAe,QAAA;AAAA,EACb,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,WAAW;AAAA,EACX,MAAM;AAAA,EACN,KAAK;;ACLA,IAAM,MAAM,WAAA;AACjB,SAAO,KAAK,IAAA,EAAM,SAAS,EAAE,IAAI,KAAK,OAAA,EAAS,SAAS,EAAE,EAAE,MAAM,CAAC;AACrE;ACYA,IAAA;AAAA;AAAA,EAAA,WAAA;AAAA,aAAAC,WAAA;AACsB,WAAA,MAAG,aAAa;AAErB,WAAA,KAAK;AACL,WAAA,KAAK;AAGL,WAAA,KAAK;AACL,WAAA,KAAK;AAAA,IAAA;AAYpBA,aAAA,UAAA,sBAAA,SAAoB,GAAW,GAAS;AACtC,WAAK,KAAK;AACV,WAAK,KAAK;AAAA,IACZ;AAGAA,aAAA,UAAA,qBAAA,SAAmB,GAAW,GAAS;AACrC,WAAK,KAAK;AACV,WAAK,KAAK;AAAA,IACZ;AAGAA,aAAA,UAAA,2BAAA,SAAyB,GAAW,GAAS;AAC3C,WAAK,KAAK;AACV,WAAK,KAAK;AAAA,IACZ;AAGAA,aAAA,UAAA,0BAAA,SAAwB,GAAW,GAAS;AAC1C,WAAK,KAAK;AACV,WAAK,KAAK;AAAA,IACZ;AAoCAA,aAAA,UAAA,OAAA,SACE,SACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IAAW;AAEP,UAAA,IAAY,IAAY,IAAY;AACpC,UAAA,IAAY,IAAY,IAAY;AAEpC,UAAA,UAAU,SAAS,GAAG;AAExB,aAAK,KAAK,KAAK;AACf,aAAK,KAAK,KAAK;AACf,aAAK,OAAE,QAAF,OAAE,SAAF,KAAM,KAAK;AAChB,aAAK,OAAE,QAAF,OAAE,SAAF,KAAM,KAAK;AAEhB,aAAK,KAAK,KAAK;AACf,aAAK,KAAK,KAAK;AACf,aAAK,OAAE,QAAF,OAAE,SAAF,KAAM,KAAK;AAChB,aAAK,OAAE,QAAF,OAAE,SAAF,KAAM,KAAK;AAAA,MAAA,WACR,UAAU,SAAS,GAAG;AAE9B,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AAEV,aAAK,KAAK,KAAK;AACf,aAAK,KAAK,KAAK;AACf,aAAK,OAAE,QAAF,OAAE,SAAF,KAAM,KAAK;AAChB,aAAK,OAAE,QAAF,OAAE,SAAF,KAAM,KAAK;AAAA,MAAA,OACX;AAEL,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AAEV,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AAAA,MAAA;AAGP,WAAA,uBAAuB,SAAS,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IACrE;AAcDA,WAAAA;AAAAA,EAAA,EAAA;AAAA;AChJD,IAAA;AAAA;AAAA,EAAA,SAAA,QAAA;AAAkC,cAAOC,eAAA,MAAA;AAU3BA,aAAAA,cAAA,QAA6B,YAAmB;AAC1D,UAAA,QAAA,qBAAQ;AARO,YAAA,cAAc;AAKvB,YAAO,UAAG;AAIZ,UAAA,OAAO,WAAW,UAAU;AACzB,cAAA,eAAe,QAAQ,UAAU;AAAA,MAAA;;;AAI1CA,kBAAA,UAAA,iBAAA,SAAe,OAA2B,YAAc;AAAd,UAAA,eAAA,QAAA;AAAc,qBAAA;AAAA,MAAA;AACtD,WAAK,UAAU;AACf,WAAK,cAAc;AAAA,IACrB;AAKAA,kBAAU,UAAA,aAAV,SAAW,SAAe;AACxB,WAAK,UAAU;AAAA,IACjB;AAEAA,kBAAA,UAAA,WAAA,WAAA;AACE,aAAO,KAAK,QAAQ,QAAQ,KAAK,eAAe,KAAK,UAAU,KAAK;AAAA,IACtE;AAEAA,kBAAA,UAAA,YAAA,WAAA;AACE,aAAO,KAAK,QAAQ,SAAS,KAAK,eAAe,KAAK,UAAU,KAAK;AAAA,IACvE;AAGAA,kBAAS,UAAA,YAAT,SAAU,SAAgC;AACjC,aAAA;AAAA,IACT;AAGAA,kBAAA,UAAA,yBAAA,SACE,SACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IAAU;AAEV,UAAM,QAAQ,KAAK;AACnB,UAAI,UAAU,QAAQ,OAAO,UAAU,UAAU;AAC/C;AAAA,MAAA;AAGG,WAAA,OAAE,QAAF,OAAE,SAAF,KAAM,KAAK,QAAQ,QAAQ,KAAK;AAChC,WAAA,OAAE,QAAF,OAAE,SAAF,KAAM,KAAK,QAAQ,SAAS,KAAK;AAEtC,WAAK,OAAA,QAAA,gBAAA,KAAM;AACX,WAAK,OAAA,QAAA,gBAAA,KAAM;AAEX,YAAM,KAAK;AACX,YAAM,KAAK;AAEL,UAAA,KAAK,KAAK,KAAK;AACf,UAAA,KAAK,KAAK,KAAK;AACf,UAAA,KAAK,KAAK,KAAK;AACf,UAAA,KAAK,KAAK,KAAK;AAEjB,UAAA;AACI,cAAA;AAOE,gBAAA,UAAU,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,eAChD,IAAI;AACP,YAAA,CAAC,KAAK,cAAc;AACd,kBAAA,IAAI,oBAAoB,KAAK;AACrC,kBAAQ,IAAI,EAAE;AACd,eAAK,eAAe;AAAA,QAAA;AAAA,MACtB;AAAA,IAEJ;AACDA,WAAAA;AAAAA,EAAA,EA1FiC,OAAO;AAAA;ACVzC,IAAA;AAAA;AAAA,EAAA,SAAA,QAAA;AAAiC,cAAOC,cAAA,MAAA;AAGtC,aAAAA,aAAY,QAAe;AACzB,UAAA,QAAA,qBAAQ;AACR,YAAK,UAAU;;;AAGjBA,iBAAgB,UAAA,mBAAhB,SAAiBC,UAAgB;AAC/B,WAAK,UAAUA;AAAA,IACjB;AAEAD,iBAAA,UAAA,WAAA,WAAA;;AACE,oBAAO,KAAA,KAAK,qCAAM,KAAK,QAAE,QAAA,OAAA,SAAA,KAAI,KAAK,QAAQ;IAC5C;AAEAA,iBAAA,UAAA,YAAA,WAAA;;AACE,oBAAO,KAAA,KAAK,qCAAM,KAAK,QAAE,QAAA,OAAA,SAAA,KAAI,KAAK,QAAQ;IAC5C;AAGAA,iBAAS,UAAA,YAAT,SAAU,SAAgC;AACjC,aAAA,KAAK,QAAQ,UAAU,OAAO;AAAA,IACvC;AAGAA,iBAAA,UAAA,yBAAA,SACE,SACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IAAU;AAEV,UAAMC,WAAU,KAAK;AACrB,UAAIA,aAAY,QAAQ,OAAOA,aAAY,UAAU;AACnD;AAAA,MAAA;AAGM,MAAAA,SAAA,KAAK,SAAS,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IACtD;AACDD,WAAAA;AAAAA,EAAA,EA5CgC,OAAO;AAAA;ACuDxC,IAAA;AAAA;AAAA,EAAA,SAAA,QAAA;AAA2B,cAAYE,QAAA,MAAA;AASrC,aAAAA,OAAY,KAAyB;AAAzB,UAAA,QAAA,QAAA;AAAA,cAAyB,CAAA;AAAA,MAAA;AACnC,UAAA,QAAA,qBAAQ;AA2CO,YAAA,oBAAG,SAACC,MAA2B;AAC9C,YAAM,MAAM,MAAK;AACjB,YAAM,MAAM,MAAK;AACjB,YAAM,OAAO,MAAK;AAElB,YAAI,CAACA,MAAK;AACD,iBAAA;AAAA,QAAA;AAGTA,eAAM,OAAO,OAAO,CAAA,GAAIA,IAAG;AAEvB,YAAA,KAAK,GAAG,GAAG;AACbA,iBAAM,IAAIA,IAAG;AAAA,QAAA;AAGf,YAAI,OAAO,GAAG;AACZA,eAAI,KAAK;AACTA,eAAI,KAAK;AACTA,eAAI,SAAS;AACbA,eAAI,UAAU;AACdA,eAAI,OAAO;AACXA,eAAI,UAAU;AACdA,eAAI,QAAQ;AACZA,eAAI,SAAS;AAAA,QAAA;AAGf,YAAI,QAAQ,GAAG;AACbA,eAAI,KAAK;AACTA,eAAI,KAAK;AACTA,eAAI,SAAS,IAAI;AACjBA,eAAI,UAAU,IAAI;AAClBA,eAAI,OAAO;AACXA,eAAI,UAAU;AACdA,eAAI,QAAQ;AACZA,eAAI,SAAS;AAAA,QAAA;AAGT,YAAAF,WAAU,IAAI,YAAY,KAAI;AACpC,QAAAA,SAAQ,MAAME,KAAI;AAClB,QAAAF,SAAQ,SAASE,KAAI;AACrB,QAAAF,SAAQ,OAAOE,KAAI;AACnB,QAAAF,SAAQ,QAAQE,KAAI;AACpB,QAAAF,SAAQ,oBAAoBE,KAAI,GAAGA,KAAI,CAAC;AACxC,QAAAF,SAAQ,mBAAmBE,KAAI,OAAOA,KAAI,MAAM;AACzC,eAAAF;AAAA,MACT;AAMoB,YAAA,uBAAG,SAAC,OAAa;AACnC,YAAM,WAAW,MAAK;AAEtB,YAAI,UAAU;AACR,cAAA,KAAK,QAAQ,GAAG;AAClB,mBAAO,SAAS,KAAK;AAAA,UAAA,WACZ,OAAO,QAAQ,GAAG;AAC3B,mBAAO,SAAS,KAAK;AAAA,UAAA;AAAA,QACvB;AAAA,MAEJ;AAGM,YAAA,SAAG,SAAC,OAAc;AACtB,YAAI,CAAC,OAAO;AAEV,iBAAO,IAAI,iBAAiB,IAAI,YAAY,KAAI,CAAC;AAAA,QAAA;AAE7C,YAAA,oBAAoB,MAAK,qBAAqB,KAAK;AACzD,YAAI,mBAAmB;AACd,iBAAA,IAAI,iBAAiB,mBAAmB,KAAI;AAAA,QAAA;AAAA,MAEvD;AAlHE,YAAK,OAAO,IAAI;AAChB,YAAK,OAAO,IAAI,OAAO,IAAI,SAAS;AAC/B,YAAA,QAAQ,IAAI,QAAQ;AAEpB,YAAA,OAAO,IAAI,OAAO,IAAI;AAC3B,YAAK,YAAY,IAAI;AAErB,UAAI,OAAO,IAAI,UAAU,YAAY,OAAO,IAAI,KAAK,GAAG;AAClD,YAAA,SAAS,IAAI,OAAO;AACjB,gBAAA,YAAY,IAAI,MAAM;AAAA,QAAA,WAClB,SAAS,IAAI,OAAO;AACxB,gBAAA,YAAY,IAAI,MAAM;AAAA,QAAA;AAE7B,YAAI,OAAO,IAAI,MAAM,UAAU,UAAU;AAClC,gBAAA,cAAc,IAAI,MAAM;AAAA,QAAA;AAAA,MAC/B,OACK;AACD,YAAA,OAAO,IAAI,cAAc,UAAU;AACrC,gBAAK,YAAY,IAAI;AAAA,QACZ,WAAA,OAAO,IAAI,UAAU,UAAU;AACxC,gBAAK,YAAY,IAAI;AAAA,QAAA;AAEnB,YAAA,OAAO,IAAI,eAAe,UAAU;AACtC,gBAAK,cAAc,IAAI;AAAA,QAAA;AAAA,MACzB;AAGF,wBAAkB,GAAG;;;AAGjBC,WAAA,UAAA,OAAN,WAAA;;;;;;mBACM,KAAK,UAAS,QAAA,CAAA,GAAA,CAAA;AACF,qBAAA,CAAA,GAAM,eAAe,KAAK,SAAS,CAAC;AAAA;AAA5C,sBAAQ,GAAoC,KAAA;AAC7C,mBAAA,eAAe,OAAO,KAAK,WAAW;;;;;;;;;;IAE9C;AAgFFA,WAAAA;AAAAA,EAAA,EA/H0B,YAAY;AAAA;AAkIvC,SAAS,eAAe,KAAW;AACjC,UAAQ,SAAS,QAAQ,MAAM,oBAAoB,GAAG;AACtD,SAAO,IAAI,QAA0B,SAAU,SAAS,QAAM;AACtD,QAAA,MAAM,IAAI;AAChB,QAAI,SAAS,WAAA;AACX,cAAQ,SAAS,QAAQ,MAAM,mBAAmB,GAAG;AACrD,cAAQ,GAAG;AAAA,IACb;AACI,QAAA,UAAU,SAAU,OAAK;AACnB,cAAA,MAAM,qBAAqB,GAAG;AACtC,aAAO,KAAK;AAAA,IACd;AACA,QAAI,MAAM;AAAA,EAAA,CACX;AACH;AAGA,SAAS,kBAAkB,KAAoB;AAC7C,MAAI,YAAY;AAAK,YAAQ,KAAK,kDAAkD;AAGpF,MAAI,aAAa;AAAK,YAAQ,KAAK,mDAAmD;AAGtF,MAAI,aAAa;AAAK,YAAQ,KAAK,mDAAmD;AAGtF,MAAI,aAAa;AAAK,YAAQ,KAAK,mDAAmD;AAEtF,MAAI,WAAW;AAAK,YAAQ,KAAK,iDAAiD;AAElF,MAAI,eAAe;AAAK,YAAQ,KAAK,qDAAqD;AAE1F,MAAI,gBAAgB;AAAK,YAAQ,KAAK,sDAAsD;AAE5F,MAAI,OAAO,IAAI,UAAU,YAAY,SAAS,IAAI;AAChD,YAAQ,KAAK,qDAAqD;AACtE;ACtMA,SAAS,wBAAwB,WAAc;AAC7C,SACE,OAAO,cAAc,YACrB,OAAO,SAAS,KAChB,aAAa,OAAO,UAAU,SAC9B,aAAa,OAAO,UAAU;AAElC;AAOA,IAAA;AAAA;AAAA,EAAA,WAAA;AAGcE,aAAAA,kBAAA,WAAkCC,QAAa;AACzD,WAAK,YAAY;AACjB,WAAK,QAAQA;AAAAA,IAAA;AAOfD,sBAAA,UAAA,UAAA,SAAQ,WAAkC,UAAiB;AACzD,UAAI,CAAC,WAAW;AACP,eAAA;AAAA,MACE,WAAA,MAAM,QAAQ,SAAS,GAAG;AACnC,eAAO,KAAK,QAAQ,UAAU,CAAC,CAAC;AAAA,MAAA,WACvB,qBAAqB,SAAS;AAChC,eAAA;AAAA,MAAA,WACE,wBAAwB,SAAS,GAAG;AACzC,YAAA,CAAC,KAAK,OAAO;AACR,iBAAA;AAAA,QAAA;AAEF,eAAA,KAAK,MAAM,kBAAkB,SAAmC;AAAA,MAAA,WAEvE,OAAO,cAAc,YACrB,OAAO,SAAS,KAChB,OAAO,aAAa,aACpB;AACA,eAAO,KAAK,QAAQ,UAAU,QAAQ,CAAC;AAAA,iBAC9B,OAAO,cAAc,cAAc,KAAK,SAAS,GAAG;AAC7D,eAAO,KAAK,QAAQ,UAAU,QAAQ,CAAC;AAAA,MAAA,WAC9B,OAAO,cAAc,UAAU;AACpC,YAAA,CAAC,KAAK,OAAO;AACR,iBAAA;AAAA,QAAA;AAET,eAAO,KAAK,QAAQ,KAAK,MAAM,qBAAqB,SAAS,CAAC;AAAA,MAAA;AAAA,IAElE;AAEAA,sBAAG,UAAA,MAAH,SAAI,UAAiB;AACnB,aAAO,KAAK,QAAQ,KAAK,WAAW,QAAQ;AAAA,IAC9C;AAEAA,sBAAK,UAAA,QAAL,SAAM,KAAe;AACnB,UAAM,QAAQ,MAAM,QAAQ,GAAG,IAAI,MAAM;AACzC,UAAI,MAAM,QAAQ,KAAK,SAAS,GAAG;AACjC,iBAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,gBAAM,CAAC,IAAI,KAAK,QAAQ,KAAK,UAAU,CAAC,CAAC;AAAA,QAAA;AAAA,MAC3C,OACK;AACL,cAAM,CAAC,IAAI,KAAK,QAAQ,KAAK,SAAS;AAAA,MAAA;AAEjC,aAAA;AAAA,IACT;AACDA,WAAAA;AAAAA,EAAA,EAAA;AAAA;AAGD,IAAM,aAAa;AAAA,CAAI,SAAA,QAAA;AAAe,YAAO,SAAA,MAAA;AAqB3C,WAAA,UAAA;AACE,QAAA,QAAA,qBAAQ;AACH,UAAA,mBAAmB,GAAG,CAAC;;;AAtB9B,UAAA,UAAA,WAAA,WAAA;AACS,WAAA;AAAA,EACT;AACA,UAAA,UAAA,YAAA,WAAA;AACS,WAAA;AAAA,EACT;AACS,UAAA,UAAA,YAAT,SAAU,SAAgC;AACjC,WAAA;AAAA,EACT;AACsB,UAAA,UAAA,yBAAtB,SACE,SACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA;EACO;AAKT,UAAA,UAAA,sBAAA,SAAoB,GAAQ;EAAe;AAC3C,UAAA,UAAA,qBAAA,SAAmB,GAAQ;EAAe;AAC1C,UAAA,UAAA,2BAAA,SAAyB,GAAW;EAAkB;AACtD,UAAA,UAAA,0BAAA,SAAwB,GAAW;EAAkB;AACjD,UAAA,UAAA,OAAJ;EAAc;AACf,SAAA;AAAD,EA9BsC,OAAO;AAiC7C,IAAM,eAAe,IAAI,iBAAiB,UAAU;AAGpD,IAAM,qBAA4C,CAAA;AAGlD,IAAM,cAAuB,CAAA;AAKvB,SAAgB,MAAM,KAA4B;;;;;;AAGtD,cAAI,eAAe,OAAO;AACxBC,qBAAQ;AAAA,UAAA,OACH;AACLA,qBAAQ,IAAI,MAAM,GAAG;AAAA,UAAA;AAGvB,cAAIA,OAAM,MAAM;AACKA,+BAAAA,OAAM,IAAI,IAAIA;AAAAA,UAAA;AAEnC,sBAAY,KAAKA,MAAK;AAEtB,iBAAA,CAAA,GAAMA,OAAM,MAAM;AAAA;AAAlB,aAAA,KAAA;AAEA,iBAAA,CAAA,GAAOA,MAAK;AAAA,MAAA;AAAA;;AACb;AAOK,SAAU,QAAQ,OAAqC;AACvD,MAAA,aAAa,OAAO,OAAO;AACtB,WAAA,IAAI,iBAAiB,KAAK;AAAA,EAAA;AAGnC,MAAI,SAA8C;AAG5C,MAAA,aAAa,MAAM,QAAQ,GAAG;AACpC,MAAI,aAAa,KAAK,MAAM,SAAS,aAAa,GAAG;AACnD,QAAM,UAAQ,mBAAmB,MAAM,MAAM,GAAG,UAAU,CAAC;AAC3D,aAAS,WAAS,QAAM,OAAO,MAAM,MAAM,aAAa,CAAC,CAAC;AAAA,EAAA;AAG5D,MAAI,CAAC,QAAQ;AAEL,QAAA,UAAQ,mBAAmB,KAAK;AAC7B,aAAA,WAAS,QAAM;;AAG1B,MAAI,CAAC,QAAQ;AAEX,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,eAAS,YAAY,CAAC,EAAE,OAAO,KAAK;AACpC,UAAI,QAAQ;AACV;AAAA,MAAA;AAAA,IACF;AAAA,EACF;AAGF,MAAI,CAAC,QAAQ;AACH,YAAA,MAAM,wBAAwB,KAAK;AAClC,aAAA;AAAA,EAAA;AAGJ,SAAA;AACT;ACtMA,IAAA;AAAA;AAAA,EAAA,SAAA,QAAA;AAAsC,cAAOC,mBAAA,MAAA;AAM/BA,aAAAA,kBAAA,QAAiB,MAA0B;AACrD,UAAA,QAAA,qBAAQ;AACR,YAAK,UAAU;AACf,YAAK,cAAc;;;AAGrBA,sBAAA,UAAA,WAAA,WAAA;;AAES,cAAA,KAAA,KAAK,QAAE,QAAA,OAAA,SAAA,KAAI,KAAK,QAAQ,SAAQ;AAAA,IACzC;AAEAA,sBAAA,UAAA,YAAA,WAAA;;AAES,cAAA,KAAA,KAAK,QAAE,QAAA,OAAA,SAAA,KAAI,KAAK,QAAQ,UAAS;AAAA,IAC1C;AAGAA,sBAAS,UAAA,YAAT,SAAU,SAAgC;AACjC,aAAA;AAAA,IACT;AAEAA,sBAAA,UAAA,yBAAA,SACE,SACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IAAU;AAEV,UAAML,WAAU,KAAK;AACrB,UAAIA,aAAY,QAAQ,OAAOA,aAAY,UAAU;AACnD;AAAA,MAAA;AAGF,UAAI,WAAW;AACf,UAAI,YAAY;AAEhB,UAAM,OAAO,OAAO,SAASA,SAAQ,IAAI,IAAIA,SAAQ,OAAO;AAC5D,UAAM,QAAQ,OAAO,SAASA,SAAQ,KAAK,IAAIA,SAAQ,QAAQ;AAC/D,UAAM,MAAM,OAAO,SAASA,SAAQ,GAAG,IAAIA,SAAQ,MAAM;AACzD,UAAM,SAAS,OAAO,SAASA,SAAQ,MAAM,IAAIA,SAAQ,SAAS;AAElE,UAAM,QAAQA,SAAQ,aAAa,OAAO;AAC1C,UAAM,SAASA,SAAQ,cAAc,MAAM;AAEvC,UAAA,CAAC,KAAK,YAAY;AACpB,mBAAW,KAAK,IAAI,WAAW,OAAO,OAAO,CAAC;AAC9C,oBAAY,KAAK,IAAI,YAAY,MAAM,QAAQ,CAAC;AAAA,MAAA;AAI9C,UAAA,MAAM,KAAK,OAAO,GAAG;AACf,QAAAA,SAAA,KAAK,SAAS,GAAG,GAAG,MAAM,KAAK,GAAG,GAAG,MAAM,GAAG;AAAA,MAAA;AAEpD,UAAA,SAAS,KAAK,OAAO,GAAG;AAClB,QAAAA,SAAA,KAAK,SAAS,GAAG,SAAS,KAAK,MAAM,QAAQ,GAAG,YAAY,KAAK,MAAM,MAAM;AAAA,MAAA;AAEnF,UAAA,MAAM,KAAK,QAAQ,GAAG;AAChB,QAAAA,SAAA,KAAK,SAAS,QAAQ,MAAM,GAAG,OAAO,KAAK,WAAW,MAAM,GAAG,OAAO,GAAG;AAAA,MAAA;AAE/E,UAAA,SAAS,KAAK,QAAQ,GAAG;AAC3B,QAAAA,SAAQ,KACN,SACA,QAAQ,MACR,SAAS,KACT,OACA,QACA,WAAW,MACX,YAAY,KACZ,OACA,MAAM;AAAA,MAAA;AAIN,UAAA,KAAK,gBAAgB,WAAW;AAElC,YAAI,MAAM,GAAG;AACH,UAAAA,SAAA,KAAK,SAAS,MAAM,GAAG,OAAO,KAAK,MAAM,GAAG,UAAU,GAAG;AAAA,QAAA;AAEnE,YAAI,SAAS,GAAG;AACN,UAAAA,SAAA,KACN,SACA,MACA,SAAS,KACT,OACA,QACA,MACA,YAAY,KACZ,UACA,MAAM;AAAA,QAAA;AAGV,YAAI,OAAO,GAAG;AACJ,UAAAA,SAAA,KAAK,SAAS,GAAG,KAAK,MAAM,QAAQ,GAAG,KAAK,MAAM,SAAS;AAAA,QAAA;AAErE,YAAI,QAAQ,GAAG;AACL,UAAAA,SAAA,KACN,SACA,QAAQ,MACR,KACA,OACA,QACA,WAAW,MACX,KACA,OACA,SAAS;AAAA,QAAA;AAIL,QAAAA,SAAA,KAAK,SAAS,MAAM,KAAK,OAAO,QAAQ,MAAM,KAAK,UAAU,SAAS;AAAA,MAAA,WACrE,KAAK,gBAAgB,QAAQ;AAEtC,YAAI,IAAI;AACR,YAAI,IAAI;AACR,YAAI;AACJ,eAAO,IAAI,GAAG;AACR,cAAA,KAAK,IAAI,OAAO,CAAC;AAChB,eAAA;AACL,cAAI,IAAI;AACR,cAAI,IAAI;AACR,cAAI;AACJ,iBAAO,IAAI,GAAG;AACR,gBAAA,KAAK,IAAI,QAAQ,CAAC;AACjB,iBAAA;AACG,YAAAA,SAAA,KAAK,SAAS,MAAM,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACjD,gBAAI,KAAK,GAAG;AACV,kBAAI,MAAM;AACA,gBAAAA,SAAA,KAAK,SAAS,GAAG,KAAK,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC;AAAA,cAAA;AAEtD,kBAAI,OAAO;AACD,gBAAAA,SAAA,KAAK,SAAS,QAAQ,MAAM,KAAK,OAAO,GAAG,IAAI,GAAG,GAAG,OAAO,CAAC;AAAA,cAAA;AAAA,YACvE;AAEG,iBAAA;AAAA,UAAA;AAEP,cAAI,KAAK;AACC,YAAAA,SAAA,KAAK,SAAS,MAAM,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG;AAAA,UAAA;AAErD,cAAI,QAAQ;AACF,YAAAA,SAAA,KAAK,SAAS,MAAM,SAAS,KAAK,GAAG,QAAQ,GAAG,GAAG,GAAG,MAAM;AAAA,UAAA;AAEjE,eAAA;AAAA,QAAA;AAAA,MACP;AAAA,IAEJ;AACDK,WAAAA;AAAAA,EAAA,EA1JqC,OAAO;AAAA;SCH7B,sBAAmB;AAEjC,SAAO,OAAO,WAAW,cAAc,OAAO,oBAAoB,IAAI;AACxE;ACkBM,SAAU,eAAe,OAAa;AAC1C,SACE,UACC,UAAU,WACT,UAAU,aACV,UAAU,UACV,UAAU,QACV,UAAU,YACV,UAAU,SACV,UAAU;AAEhB;AAEiB,IAAIC,QAAM;AA8B3B,IAAA;AAAA;AAAA,EAAA,WAAA;AA+DE,aAAAC,KAAY,OAAgB;AA9DR,WAAA,MAAG,SAAS;AA0Df,WAAA,cAAc;AACd,WAAA,cAAc;AAI7B,WAAK,SAAS;AACd,WAAK,UAAU;AAGV,WAAA,kBAAkB,IAAI;AAGtB,WAAA,kBAAkB,IAAI;AAE3B,WAAK,MAAK;AAAA,IAAA;AAGZA,SAAA,UAAA,QAAA,WAAA;AACE,WAAK,gBAAgB;AACrB,WAAK,SAAS;AAEd,WAAK,SAAS;AACd,WAAK,UAAU;AAEf,WAAK,UAAU;AACf,WAAK,UAAU;AACf,WAAK,SAAS;AACd,WAAK,SAAS;AACd,WAAK,YAAY;AAGjB,WAAK,WAAW;AAEhB,WAAK,UAAU;AACf,WAAK,UAAU;AAGf,WAAK,WAAW;AAChB,WAAK,WAAW;AAChB,WAAK,WAAW;AAGhB,WAAK,WAAW;AAChB,WAAK,UAAU;AACf,WAAK,UAAU;AAGf,WAAK,WAAW;AAChB,WAAK,WAAW;AAEhB,WAAK,QAAQ;AACb,WAAK,QAAQ;AACb,WAAK,YAAY,KAAK;AACtB,WAAK,aAAa,KAAK;AAGvB,WAAK,gBAAgB,EAAED;AACvB,WAAK,gBAAgB,EAAEA;AACvB,WAAK,aAAa,EAAEA;AAAAA,IACtB;AAGAC,SAAA,UAAA,UAAA,WAAA;AACE,WAAK,UAAU,KAAK,OAAO,WAAW,KAAK,OAAO,QAAQ;AAG1D,UAAI,KAAK,YAAY,KAAK,cAAc,KAAK,eAAe;AAC1D,aAAK,aAAa,KAAK;AACvB,aAAK,gBAAgB,EAAED;AAAAA,MAAA;AAGrB,UAAA,KAAK,YAAY,KAAK,WAAW,KAAK,aAAa,KAAK,QAAQ,eAAe;AAC5E,aAAA,YAAY,KAAK,QAAQ;AAC9B,aAAK,gBAAgB,EAAEA;AAAAA,MAAA;AAGlB,aAAA;AAAA,IACT;AAEAC,SAAA,UAAA,WAAA,WAAA;AACS,aAAA,KAAK,SAAS,QAAQ,KAAK,UAAU,KAAK,QAAQ,SAAS,QAAQ;AAAA,IAC5E;AAGAA,SAAA,UAAA,iBAAA,WAAA;AACE,WAAK,QAAO;AACZ,UAAM,KAAK,KAAK,IACd,KAAK,eACL,KAAK,eACL,KAAK,UAAU,KAAK,QAAQ,aAAa,CAAC;AAExC,UAAA,KAAK,WAAW,IAAI;AACtB,eAAO,KAAK;AAAA,MAAA;AAEd,WAAK,UAAU;AAEf,UAAM,MAAM,KAAK;AACb,UAAA,MAAM,KAAK,gBAAgB;AAE/B,WAAK,WAAW,IAAI,OAAO,KAAK,QAAQ,eAAe;AAEvD,WAAK,aAAa,EAAED;AAEb,aAAA;AAAA,IACT;AAEAC,SAAA,UAAA,iBAAA,WAAA;AACE,WAAK,QAAO;AACZ,UAAM,KAAK,KAAK,IACd,KAAK,eACL,KAAK,eACL,KAAK,UAAU,KAAK,QAAQ,gBAAgB,CAAC;AAE3C,UAAA,KAAK,WAAW,IAAI;AACtB,eAAO,KAAK;AAAA,MAAA;AAEd,WAAK,UAAU;AAEf,UAAM,MAAM,KAAK;AAEjB,UAAI,SAAQ;AACZ,UAAI,KAAK,UAAU;AACb,YAAA,UAAU,CAAC,KAAK,UAAU,KAAK,QAAQ,CAAC,KAAK,UAAU,KAAK,OAAO;AAAA,MAAA;AAErE,UAAA,MAAM,KAAK,UAAU,KAAK,aAAa,KAAK,UAAU,KAAK,WAAW;AAC1E,UAAI,KAAK,KAAK,QAAQ,KAAK,MAAM;AAC7B,UAAA,OAAO,KAAK,SAAS;AACzB,UAAI,KAAK,UAAU;AACb,YAAA,UAAU,KAAK,UAAU,KAAK,QAAQ,KAAK,UAAU,KAAK,OAAO;AAAA,MAAA;AAIvE,UAAI,KAAK,UAAU;AAEjB,aAAK,QAAQ;AACb,aAAK,QAAQ;AACb,aAAK,YAAY,KAAK;AACtB,aAAK,aAAa,KAAK;AAAA,MAAA,OAClB;AAEL,YAAI;AACJ,YAAI;AACC,YAAA,IAAI,IAAI,KAAK,IAAI,IAAI,KAAO,IAAI,IAAI,KAAK,IAAI,IAAI,GAAI;AACpD,cAAA;AACJ,cAAI,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK;AAAA,QAAA,OAClC;AACD,cAAA,IAAI,IAAI,KAAK;AACb,cAAA,IAAI,IAAI,KAAK;AAAA,QAAA;AAEnB,YAAI,IAAI,GAAG;AACT,eAAK,QAAQ;AACb,eAAK,YAAY,IAAI;AAAA,QAAA,OAChB;AACL,eAAK,QAAQ;AACb,eAAK,YAAY,IAAI;AAAA,QAAA;AAElB,YAAA,IAAI,IAAI,KAAK,IAAI,IAAI,KAAO,IAAI,IAAI,KAAK,IAAI,IAAI,GAAI;AACpD,cAAA;AACJ,cAAI,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK;AAAA,QAAA,OAClC;AACD,cAAA,IAAI,IAAI,KAAK;AACb,cAAA,IAAI,IAAI,KAAK;AAAA,QAAA;AAEnB,YAAI,IAAI,GAAG;AACT,eAAK,QAAQ;AACb,eAAK,aAAa,IAAI;AAAA,QAAA,OACjB;AACL,eAAK,QAAQ;AACb,eAAK,aAAa,IAAI;AAAA,QAAA;AAAA,MACxB;AAGF,WAAK,KAAK,KAAK;AACf,WAAK,KAAK,KAAK;AAEf,WAAK,MAAM,KAAK,QAAQ,KAAK,WAAW,KAAK,YAAY,KAAK;AAC9D,WAAK,MAAM,KAAK,QAAQ,KAAK,WAAW,KAAK,aAAa,KAAK;AAE3D,UAAA,KAAK,YAAY,KAAK,SAAS;AACjC,aAAK,QAAQ;AACb,aAAK,MAAM,KAAK,UAAU,KAAK,QAAQ;AACvC,aAAK,MAAM,KAAK,UAAU,KAAK,QAAQ;AAAA,MAAA;AAGzC,UAAI,UAAU,KAAK,IAAI,KAAK,EAAE;AAE9B,aAAO,KAAK;AAAA,IACd;AAGAA,SAAG,UAAA,MAAH,SAAI,KAAW;AACb,UAAI,OAAO,QAAQ,GAAG,MAAM,YAAY;AAC/B,eAAA,QAAQ,GAAG,EAAE,IAAI;AAAA,MAAA;AAAA,IAE5B;AAIAA,SAAA,UAAA,MAAA,SAAI,GAAG,GAAE;AACH,UAAA,OAAO,MAAM,UAAU;AACzB,YAAI,OAAO,QAAQ,CAAC,MAAM,cAAc,OAAO,MAAM,aAAa;AACxD,kBAAA,CAAC,EAAE,MAAM,CAAC;AAAA,QAAA;AAAA,MACpB,WACS,OAAO,MAAM,UAAU;AAChC,aAAK,KAAK,GAAG;AACP,cAAA,OAAO,QAAQ,CAAC,MAAM,cAAc,OAAO,EAAE,CAAC,MAAM,aAAa;AACnE,oBAAQ,CAAC,EAAE,MAAM,EAAE,CAAC,GAAG,CAAC;AAAA,UAAA;AAAA,QAC1B;AAAA,MACF;AAEF,UAAI,KAAK,QAAQ;AACV,aAAA,OAAO,UAAU,EAAED;AACxB,aAAK,OAAO;;AAEP,aAAA;AAAA,IACT;AAIAC,SAAA,UAAA,MAAA,SAAI,OAAsB,QAAuB,MAAc;AAC7D,WAAK,gBAAgB,EAAED;AACvB,UAAI,SAAS,WAAW;AACf,eAAA;AAAA,MAAA;AAET,UAAI,SAAS,SAAS;AACb,eAAA;AAAA,MAAA;AAEL,UAAA,OAAO,UAAU,UAAU;AACxB,aAAA,UAAU,QAAQ,KAAK;AAC5B,aAAK,SAAS,KAAK;AAAA,MAAA;AAEjB,UAAA,OAAO,WAAW,UAAU;AACzB,aAAA,UAAU,SAAS,KAAK;AAC7B,aAAK,UAAU,KAAK;AAAA,MAAA;AAElB,UAAA,OAAO,UAAU,YAAY,OAAO,WAAW,YAAY,OAAO,SAAS,UAAU;AACvF,YAAI,SAAS,OAAQ;AAAA,iBACV,SAAS,SAAS,SAAS,YAAY;AAC3C,eAAA,UAAU,KAAK,UAAU,KAAK,IAAI,KAAK,SAAS,KAAK,OAAO;AAAA,QACxD,WAAA,SAAS,QAAQ,SAAS,UAAU;AACxC,eAAA,UAAU,KAAK,UAAU,KAAK,IAAI,KAAK,SAAS,KAAK,OAAO;AAAA,QAAA;AAE/D,YAAA,SAAS,cAAc,SAAS,UAAU;AACvC,eAAA,SAAS,QAAQ,KAAK;AACtB,eAAA,UAAU,SAAS,KAAK;AAAA,QAAA;AAAA,MAC/B;AAAA,IAEJ;AACDC,WAAAA;AAAAA,EAAA,EAAA;AAAA;AAEgB,IAAM,UAAU;AAAA,EAC/B,OAAO,SAAU,KAAQ;AACvB,WAAO,IAAI;AAAA,EACb;AAAA,EAEA,cAAc,SAAU,KAAQ;AAC9B,WAAO,IAAI;AAAA,EACb;AAAA,EAEA,OAAO,SAAU,KAAQ;AACvB,WAAO,IAAI;AAAA,EACb;AAAA,EAEA,QAAQ,SAAU,KAAQ;AACxB,WAAO,IAAI;AAAA,EACb;AAAA,EAEA,UAAU,SAAU,KAAQ;AAC1B,WAAO,IAAI;AAAA,EACb;AAAA,EAEA,WAAW,SAAU,KAAQ;AAC3B,WAAO,IAAI;AAAA,EACb;AAAA;AAAA;AAAA,EAKA,QAAQ,SAAU,KAAQ;AACxB,WAAO,IAAI;AAAA,EACb;AAAA,EAEA,QAAQ,SAAU,KAAQ;AACxB,WAAO,IAAI;AAAA,EACb;AAAA;AAAA;AAAA,EAKA,OAAO,SAAU,KAAQ;AACvB,WAAO,IAAI;AAAA,EACb;AAAA,EAEA,OAAO,SAAU,KAAQ;AACvB,WAAO,IAAI;AAAA,EACb;AAAA,EAEA,UAAU,SAAU,KAAQ;AAC1B,WAAO,IAAI;AAAA,EACb;AAAA;AAAA;AAAA,EAKA,QAAQ,SAAU,KAAQ;AACxB,WAAO,IAAI;AAAA,EACb;AAAA,EAEA,QAAQ,SAAU,KAAQ;AACxB,WAAO,IAAI;AAAA,EACb;AAAA;AAAA;AAAA,EAKA,SAAS,SAAU,KAAQ;AACzB,WAAO,IAAI;AAAA,EACb;AAAA,EAEA,SAAS,SAAU,KAAQ;AACzB,WAAO,IAAI;AAAA,EACb;AAAA;AAAA;AAAA,EAKA,QAAQ,SAAU,KAAQ;AACxB,WAAO,IAAI;AAAA,EACb;AAAA,EAEA,QAAQ,SAAU,KAAQ;AACxB,WAAO,IAAI;AAAA,EACb;AAAA;AAAA;AAAA,EAKA,SAAS,SAAU,KAAQ;AACzB,WAAO,IAAI;AAAA,EACb;AAAA,EAEA,SAAS,SAAU,KAAQ;AACzB,WAAO,IAAI;AAAA,EAAA;;AAgBE,IAAM,UAAU;AAAA,EAC/B,OAAO,SAAU,KAAU,OAAa;AACtC,QAAI,SAAS;AAAA,EACf;AAAA,EAEA,cAAc,SAAU,KAAU,OAAa;AAC7C,QAAI,gBAAgB;AAAA,EACtB;AAAA,EAEA,OAAO,SAAU,KAAU,OAAa;AACtC,QAAI,kBAAkB;AACtB,QAAI,SAAS;AACb,QAAI,gBAAgB,EAAED;AAAAA,EACxB;AAAA,EAEA,QAAQ,SAAU,KAAU,OAAa;AACvC,QAAI,mBAAmB;AACvB,QAAI,UAAU;AACd,QAAI,gBAAgB,EAAEA;AAAAA,EACxB;AAAA,EAEA,OAAO,SAAU,KAAU,OAAa;AACtC,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,gBAAgB,EAAEA;AAAAA,EACxB;AAAA,EAEA,QAAQ,SAAU,KAAU,OAAa;AACvC,QAAI,UAAU;AACd,QAAI,gBAAgB,EAAEA;AAAAA,EACxB;AAAA,EAEA,QAAQ,SAAU,KAAU,OAAa;AACvC,QAAI,UAAU;AACd,QAAI,gBAAgB,EAAEA;AAAAA,EACxB;AAAA,EAEA,MAAM,SAAU,KAAU,OAAa;AACrC,QAAI,SAAS;AACb,QAAI,SAAS;AACb,QAAI,gBAAgB,EAAEA;AAAAA,EACxB;AAAA,EAEA,OAAO,SAAU,KAAU,OAAa;AACtC,QAAI,SAAS;AACb,QAAI,gBAAgB,EAAEA;AAAAA,EACxB;AAAA,EAEA,OAAO,SAAU,KAAU,OAAa;AACtC,QAAI,SAAS;AACb,QAAI,gBAAgB,EAAEA;AAAAA,EACxB;AAAA,EAEA,UAAU,SAAU,KAAU,OAAa;AACzC,QAAI,YAAY;AAChB,QAAI,gBAAgB,EAAEA;AAAAA,EACxB;AAAA,EAEA,OAAO,SAAU,KAAU,OAAa;AACtC,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,WAAW;AACf,QAAI,gBAAgB,EAAEA;AAAAA,EACxB;AAAA,EAEA,QAAQ,SAAU,KAAU,OAAa;AACvC,QAAI,UAAU;AACd,QAAI,WAAW;AACf,QAAI,gBAAgB,EAAEA;AAAAA,EACxB;AAAA,EAEA,QAAQ,SAAU,KAAU,OAAa;AACvC,QAAI,UAAU;AACd,QAAI,WAAW;AACf,QAAI,gBAAgB,EAAEA;AAAAA,EACxB;AAAA,EAEA,QAAQ,SAAU,KAAU,OAAa;AACvC,QAAI,WAAW;AACf,QAAI,WAAW;AACf,QAAI,gBAAgB,EAAEA;AAAAA,EACxB;AAAA,EAEA,SAAS,SAAU,KAAU,OAAa;AACxC,QAAI,WAAW;AACf,QAAI,gBAAgB,EAAEA;AAAAA,EACxB;AAAA,EAEA,SAAS,SAAU,KAAU,OAAa;AACxC,QAAI,WAAW;AACf,QAAI,gBAAgB,EAAEA;AAAAA,EACxB;AAAA,EAEA,OAAO,SAAU,KAAU,OAAa;AACjC,SAAA,OAAO,KAAK,KAAK;AACjB,SAAA,OAAO,KAAK,KAAK;AAAA,EACxB;AAAA,EAEA,QAAQ,SAAU,KAAU,OAAa;AACvC,QAAI,UAAU;AACd,QAAI,WAAW;AACf,QAAI,gBAAgB,EAAEA;AAEjB,SAAA,QAAQ,KAAK,KAAK;AAAA,EACzB;AAAA,EAEA,QAAQ,SAAU,KAAU,OAAa;AACvC,QAAI,UAAU;AACd,QAAI,WAAW;AACf,QAAI,gBAAgB,EAAEA;AAEjB,SAAA,QAAQ,KAAK,KAAK;AAAA,EACzB;AAAA,EAEA,QAAQ,SAAU,KAAU,OAAa;AAClC,SAAA,QAAQ,KAAK,KAAK;AAClB,SAAA,QAAQ,KAAK,KAAK;AAAA,EACzB;AAAA,EAEA,SAAS,SAAU,KAAU,OAAa;AACxC,QAAI,WAAW;AACf,QAAI,WAAW;AACf,QAAI,gBAAgB,EAAEA;AAAAA,EACxB;AAAA,EAEA,SAAS,SAAU,KAAU,OAAa;AACxC,QAAI,WAAW;AACf,QAAI,WAAW;AACf,QAAI,gBAAgB,EAAEA;AAAAA,EACxB;AAAA,EAEA,YAAY,SAAU,KAAU,OAAgB,KAAiB;AAC/D,QAAI,KAAK;AACP,UAAI,SAAS,MAAM;AACT,gBAAA;AAAA,MAAA,WACC,SAAS,OAAO;AACjB,gBAAA;AAAA,MAAA;AAEV,UAAI,IAAI,IAAI,aAAa,IAAI,cAAc,KAAK;AAAA,IAAA;AAAA,EAEpD;AAAA,EAEA,aAAa,SAAU,KAAU,OAAe,KAAiB;AAC/D,QAAI,CAAC,OAAO,CAAC,IAAI,YAAY;AACvB,UAAA,IAAI,OAAO,IAAI;AAAA,IAAA;AAAA,EAEvB;AAAA,EAEA,cAAc,SAAU,KAAU,OAAe,KAAiB;AAChE,QAAI,CAAC,OAAO,CAAC,IAAI,YAAY;AACvB,UAAA,IAAI,MAAM,KAAK;AAAA,IAAA;AAAA,EAEvB;AAAA,EAEA,WAAW,SAAU,KAAU,OAAgB,KAAgB;AAC7D,QAAI,KAAK;AACP,UAAI,IAAI,IAAI,YAAY,IAAI,aAAa,KAAK;AAAA,IAAA;AAAA,EAElD;AAAA,EAEA,YAAY,SAAU,KAAU,OAAe,KAAgB;AAC7D,QAAI,CAAC,OAAO,CAAC,IAAI,WAAW;AACtB,UAAA,IAAI,OAAO,IAAI;AAAA,IAAA;AAAA,EAEvB;AAAA,EAEA,aAAa,SAAU,KAAU,OAAe,KAAgB;AAC9D,QAAI,CAAC,OAAO,CAAC,IAAI,WAAW;AACtB,UAAA,IAAI,MAAM,KAAK;AAAA,IAAA;AAAA,EAEvB;AAAA,EAEA,QAAQ,SAAU,KAAU,OAAa;AAClC,SAAA,OAAO,KAAK,MAAM,CAAC;AACxB,SAAK,MAAM,KAAK,MAAM,IAAI,MAAM,CAAC;AACjC,SAAK,MAAM,KAAK,MAAM,IAAI,MAAM,CAAC;AAC5B,SAAA,OAAO,KAAK,MAAM,CAAC;AACnB,SAAA,QAAQ,KAAK,MAAM,CAAC;AACpB,SAAA,QAAQ,KAAK,MAAM,CAAC;AACpB,SAAA,SAAS,KAAK,CAAC;AAAA,EAAA;;ACppBxB,SAAS,SAAS,GAAM;AACf,SAAA;AACT;AAmCiB,IAAM,eAA+C;AACrD,IAAM,eAA2C;AAGjD,IAAM,eAA2C;AAMlE,IAAA;AAAA;AAAA,EAAA,WAAA;AAAA,aAAAE,UAAA;AAAA,IAAA;AACSA,YAAA,MAAP,SACE,OACA,UAAyB;AAEzB,iBAAW,YAAY;AACnB,UAAA,OAAO,UAAU,YAAY;AACxB,eAAA;AAAA,MAAA;AAEL,UAAA,OAAO,UAAU,UAAU;AACtB,eAAA;AAAA,MAAA;AAEL,UAAA,SAAS,aAAa,KAAK;AAC/B,UAAI,QAAQ;AACH,eAAA;AAAA,MAAA;AAEH,UAAA,SAAS,gDAAgD,KAAK,KAAK;AACzE,UAAI,CAAC,UAAU,CAAC,OAAO,QAAQ;AACtB,eAAA;AAAA,MAAA;AAGH,UAAA,WAAW,OAAO,CAAC;AACnB,UAAA,SAAS,aAAa,QAAQ;AAE9B,UAAA,WAAW,OAAO,CAAC;AACnB,UAAA,SAAS,aAAa,QAAQ;AAE9B,UAAA,SAAS,OAAO,CAAC;AAEvB,UAAI,CAAC,QAAQ;AACF,iBAAA;AAAA,MAAA,WACA,QAAQ,UAAU,OAAO,OAAO,OAAO,YAAY;AAC5D,iBAAS,OAAO;AAAA,MAAA,WACP,QAAQ,UAAU,OAAO,OAAO,OAAO,YAAY;AACtD,YAAA,OAAO,SAAS,OAAO,QAAQ,OAAO,EAAE,EAAE,MAAM,GAAG,IAAI;AAC7D,iBAAS,OAAO,GAAG,MAAM,OAAO,IAAI,IAAI;AAAA,MAAA,OACnC;AACI,iBAAA;AAAA,MAAA;AAGX,UAAI,QAAQ;AACV,iBAAS,OAAO,MAAM;AAAA,MAAA;AAGxB,mBAAa,KAAK,IAAI;AAEf,aAAA;AAAA,IACT;AACDA,WAAAA;AAAAA,EAAA,EAAA;AAAA;AAGD,SAAS,QAAQ,MAAc,IAAc;AAC3C,eAAa,IAAI,IAAI;AACvB;AAGA,SAAS,UAAU,MAAgB;AACjC,MAAM,QAAQ,KAAK,KAAK,MAAM,KAAK;AACnC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAC/B,QAAA,MAAM,MAAM,CAAC;AACnB,QAAI,KAAK;AACP,mBAAa,GAAG,IAAI;AAAA,IAAA;AAAA,EACtB;AAEJ;AAEA,QAAQ,MAAM,SAAU,GAAiB;AAChC,SAAA;AACT,CAAC;AAED,QAAQ,OAAO,SAAU,GAAiB;AACxC,SAAO,SAAU,GAAS;AACjB,WAAA,IAAI,EAAE,IAAI,CAAC;AAAA,EACpB;AACF,CAAC;AAED,QAAQ,UAAU,SAAU,GAAiB;AAC3C,SAAO,SAAU,GAAS;AACxB,WAAO,IAAI,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE,KAAK,IAAI,EAAE,IAAI;AAAA,EACvD;AACF,CAAC;AAED,QAAQ,UAAU,SAAU,GAAiB;AAC3C,SAAO,SAAU,GAAS;AACxB,WAAO,IAAI,MAAM,IAAI,EAAE,KAAK,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI;AAAA,EACvD;AACF,CAAC;AAED,UAAU;AAAA,EACR,MAAM;AAAA,EACN,IAAI,SAAU,GAAS;AACd,WAAA;AAAA,EAAA;AAEV,CAAA;AAED,UAAU;AAAA,EACR,MAAM;AAAA,EACN,IAAI,SAAU,GAAS;AACrB,WAAO,IAAI;AAAA,EAAA;AAEd,CAAA;AAED,UAAU;AAAA,EACR,MAAM;AAAA,EACN,IAAI,SAAU,GAAS;AACrB,WAAO,IAAI,IAAI;AAAA,EAAA;AAElB,CAAA;AAED,UAAU;AAAA,EACR,MAAM;AAAA,EACN,IAAI,SAAU,GAAS;AACd,WAAA,IAAI,IAAI,IAAI;AAAA,EAAA;AAEtB,CAAA;AAED,UAAU;AAAA,EACR,MAAM;AAAA,EACN,IAAI,SAAU,GAAS;AACd,WAAA,IAAI,IAAI,IAAI,IAAI;AAAA,EAAA;AAE1B,CAAA;AAED,UAAU;AAAA,EACR,MAAM;AAAA,EACN,IAAI,SAAU,GAAS;AACrB,WAAO,IAAI,KAAK,IAAK,IAAI,KAAK,KAAM,CAAC;AAAA,EAAA;AAExC,CAAA;AAED,UAAU;AAAA,EACR,MAAM;AAAA,EACN,IAAI,SAAU,GAAS;AACd,WAAA,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,EAAE;AAAA,EAAA;AAE/C,CAAA;AAED,UAAU;AAAA,EACR,MAAM;AAAA,EACN,IAAI,SAAU,GAAS;AACrB,WAAO,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC;AAAA,EAAA;AAEjC,CAAA;AAED,UAAU;AAAA,EACR,MAAM;AAAA,EACN,IAAI,SAAU,GAAS;AACrB,WAAO,IAAI,IAAI,OACX,SAAS,IAAI,IACb,IAAI,IAAI,OACN,UAAU,KAAK,MAAM,QAAQ,IAAI,OACjC,IAAI,MAAM,OACR,UAAU,KAAK,OAAO,QAAQ,IAAI,SAClC,UAAU,KAAK,QAAQ,QAAQ,IAAI;AAAA,EAAA;AAE9C,CAAA;AAED,UAAU;AAAA,EACR,MAAM;AAAA,EACN,IAAI,SAAU,GAAC;AACb,WAAO,SAAU,GAAS;AACjB,aAAA,KAAK,IAAI,GAAG,CAAC;AAAA,IACtB;AAAA,EAAA;AAEH,CAAA;AAED,UAAU;AAAA,EACR,MAAM;AAAA,EACN,IAAI,SAAU,GAAG,GAAC;AAChB,QAAI,KAAK;AACT,QAAI,KAAK;AACH,QAAA,IAAK,KAAK,IAAI,KAAK,MAAO,KAAK,KAAK,IAAI,CAAC;AAC/C,WAAO,SAAU,GAAS;AACxB,aAAO,IAAI,IAAI,KAAK,IAAI,GAAG,MAAM,CAAC,IAAI,KAAK,KAAM,IAAI,MAAM,IAAI,KAAK,MAAO,CAAC;AAAA,IAC9E;AAAA,EAAA;AAEH,CAAA;AAED,UAAU;AAAA,EACR,MAAM;AAAA,EACN,IAAI,SAAU,GAAC;AACT,QAAA,OAAO,MAAM,cAAc,IAAI;AACnC,WAAO,SAAU,GAAS;AACxB,aAAO,IAAI,MAAM,IAAI,KAAK,IAAI;AAAA,IAChC;AAAA,EAAA;AAEH,CAAA;AC3ND,IAAA;AAAA;AAAA,EAAA,WAAA;AAqBcC,aAAAA,YAAA,OAAkB,SAA+B;AAA/B,UAAA,YAAA,QAAA;AAAA,kBAA+B,CAAA;AAAA,MAAA;AApBzC,WAAA,MAAG,gBAAgB;AAKtB,WAAA,UAAmC,CAAA;AAgBlD,WAAK,OAAO,CAAA;AACP,WAAA,YAAY,QAAQ,YAAY;AAChC,WAAA,SAAS,QAAQ,SAAS;AAE/B,WAAK,SAAS;AACd,WAAK,QAAQ;AAAA,IAAA;AAIfA,gBAAI,UAAA,OAAJ,SAAKC,YAAsB,SAAiB,KAAa,MAAY;AACnE,WAAK,SAAS;AAEV,UAAA,KAAK,QAAQ,KAAK,QAAQ;AAC5B;AAAA,MAAA;AAGI,UAAA,OAAO,KAAK,QAAQ,KAAK;AAE3B,UAAA,CAAC,KAAK,QAAQ;AAChB,aAAK,SAAS,CAAA;AACH,iBAAA,OAAO,KAAK,MAAM;AAC3B,eAAK,OAAO,GAAG,IAAI,KAAK,OAAO,IAAI,GAAG;AAAA,QAAA;AAAA,MACxC;AAGF,UAAI,IAAI,KAAK,IAAI,OAAO,KAAK,WAAW,CAAC;AACzC,UAAM,QAAQ,KAAK;AAEf,UAAA,OAAO,KAAK,WAAW,YAAY;AACjC,YAAA,KAAK,QAAQ,CAAC;AAAA,MAAA;AAGpB,UAAM,IAAI,IAAI;AAEH,eAAA,OAAO,KAAK,MAAM;AAC3B,aAAK,OAAO,IAAI,KAAK,KAAK,OAAO,GAAG,IAAI,IAAI,KAAK,KAAK,GAAG,IAAI,CAAC;AAAA,MAAA;AAGzD,aAAA;AAAA,IACT;AAGAD,gBAAA,UAAA,SAAA,WAAA;AAAA,UASC,QAAA;AARM,WAAA,QAAQ,QAAQ,SAAC,UAA+B;AAC/C,YAAA;AACO,mBAAA,KAAK,MAAK,MAAM;AAAA,iBAClB,GAAG;AACV,kBAAQ,MAAM,CAAC;AAAA,QAAA;AAAA,MACjB,CACD;AACD,aAAO,KAAK;AAAA,IACd;AAIAA,gBAAA,UAAA,QAAA,SAAM,GAAqB,GAAU;AAC/B,UAAA;AACJ,UAAI,OAAO,MAAM,YAAY,MAAM,MAAM;AAC7B,kBAAA;AAAA,MAAA,OACL;AACL,kBAAU;AACN,YAAA,OAAO,MAAM,UAAU;AACzB,kBAAQ,WAAW;AACf,cAAA,OAAO,MAAM,UAAU;AACzB,oBAAQ,QAAQ;AAAA,UAAA;AAAA,QAClB;AAAA,MACF;AAGF,aAAQ,KAAK,QAAQ,IAAIA,YAAW,KAAK,QAAQ,OAAO;AAAA,IAC1D;AAEAA,gBAAQ,UAAA,WAAR,SAAS,UAAgB;AACvB,WAAK,YAAY;AACV,aAAA;AAAA,IACT;AAEAA,gBAAK,UAAA,QAAL,SAAM,OAAa;AACjB,WAAK,SAAS;AACP,aAAA;AAAA,IACT;AAEAA,gBAAI,UAAA,OAAJ,SAAK,QAA2C;AACzC,WAAA,UAAU,OAAO,IAAI,MAAM;AACzB,aAAA;AAAA,IACT;AAEAA,gBAAI,UAAA,OAAJ,SAAK,IAAyB;AACvB,WAAA,QAAQ,KAAK,EAAE;AACb,aAAA;AAAA,IACT;AAEAA,gBAAA,UAAA,OAAA,WAAA;AACO,WAAA,QAAQ,KAAK,WAAA;AAChB,aAAK,KAAI;AAAA,MAAA,CACV;AACD,WAAK,QAAQ;AACN,aAAA;AAAA,IACT;AAEAA,gBAAA,UAAA,SAAA,WAAA;AACO,WAAA,QAAQ,KAAK,WAAA;AAChB,aAAK,OAAM;AAAA,MAAA,CACZ;AACD,WAAK,UAAU;AACR,aAAA;AAAA,IACT;AAKAA,gBAAA,UAAA,MAAA,SAAI,GAAI,GAAE;AACJ,UAAA,OAAO,MAAM,UAAU;AACzB,iBAAW,QAAQ,GAAG;AACpB,kBAAQ,KAAK,QAAQ,KAAK,MAAM,MAAM,EAAE,IAAI,CAAC;AAAA,QAAA;AAAA,MAC/C,WACS,OAAO,MAAM,aAAa;AACnC,gBAAQ,KAAK,QAAQ,KAAK,MAAM,GAAG,CAAC;AAAA,MAAA;AAE/B,aAAA;AAAA,IACT;AAKAA,gBAAI,UAAA,OAAJ,SAAK,IAAyB;AAC5B,WAAK,KAAK,EAAE;AACL,aAAA;AAAA,IACT;AAKAA,gBAAK,UAAA,QAAL,SAAM,SAAgB;AACb,aAAA;AAAA,IACT;AAEAA,gBAAA,UAAA,OAAA,SAAK,GAAW,GAAS;AAElB,WAAA,IAAI,SAAS,CAAC;AACd,WAAA,IAAI,UAAU,CAAC;AACb,aAAA;AAAA,IACT;AAIAA,gBAAK,UAAA,QAAL,SAAM,GAAU;AAEV,UAAA,OAAO,MAAM,aAAa;AACrB,eAAA,KAAK,IAAI,OAAO;AAAA,MAAA;AAEpB,WAAA,IAAI,SAAS,CAAC;AACZ,aAAA;AAAA,IACT;AAIAA,gBAAM,UAAA,SAAN,SAAO,GAAU;AAEX,UAAA,OAAO,MAAM,aAAa;AACrB,eAAA,KAAK,IAAI,QAAQ;AAAA,MAAA;AAErB,WAAA,IAAI,UAAU,CAAC;AACb,aAAA;AAAA,IACT;AAIAA,gBAAA,UAAA,SAAA,SAAO,GAAuB,GAAU;AAElC,UAAA,OAAO,MAAM,UAAU;AACzB,YAAI,EAAE;AACN,YAAI,EAAE;AAAA,MAAA;AAEH,WAAA,IAAI,WAAW,CAAC;AAChB,WAAA,IAAI,WAAW,CAAC;AACd,aAAA;AAAA,IACT;AAEAA,gBAAM,UAAA,SAAN,SAAO,GAAS;AAET,WAAA,IAAI,YAAY,CAAC;AACf,aAAA;AAAA,IACT;AAIAA,gBAAA,UAAA,OAAA,SAAK,GAAuB,GAAU;AAEhC,UAAA,OAAO,MAAM,UAAU;AACzB,YAAI,EAAE;AACN,YAAI,EAAE;AAAA,MAAA,WACG,OAAO,MAAM,aAAa;AAC/B,YAAA;AAAA,MAAA;AAED,WAAA,IAAI,SAAS,CAAC;AACd,WAAA,IAAI,SAAS,CAAC;AACZ,aAAA;AAAA,IACT;AAKAA,gBAAA,UAAA,QAAA,SAAM,GAAuB,GAAU;AAEjC,UAAA,OAAO,MAAM,UAAU;AACzB,YAAI,EAAE;AACN,YAAI,EAAE;AAAA,MAAA,WACG,OAAO,MAAM,aAAa;AAC/B,YAAA;AAAA,MAAA;AAED,WAAA,IAAI,UAAU,CAAC;AACf,WAAA,IAAI,UAAU,CAAC;AACb,aAAA;AAAA,IACT;AAEAA,gBAAA,UAAA,QAAA,SAAM,GAAW,IAAW;AAErB,WAAA,IAAI,SAAS,CAAC;AACf,UAAA,OAAO,OAAO,aAAa;AACxB,aAAA,IAAI,gBAAgB,EAAE;AAAA,MAAA;AAEtB,aAAA;AAAA,IACT;AACDA,WAAAA;AAAAA,EAAA,EAAA;AAAA;AAGD,SAAS,QAAQC,YAAsB,KAAa,KAAa,OAAa;AAC5E,MAAI,OAAOA,WAAU,IAAI,GAAG,MAAM,UAAU;AAC1C,QAAI,GAAG,IAAI;AAAA,EACF,WAAA,OAAOA,WAAU,IAAI,MAAM,GAAG,MAAM,YAAY,OAAOA,WAAU,IAAI,MAAM,GAAG,MAAM,UAAU;AACnG,QAAA,MAAM,GAAG,IAAI;AACb,QAAA,MAAM,GAAG,IAAI;AAAA,EAAA;AAErB;ACrQA,IAAI,MAAM;AACV,MAAM,SAAS;AAGf,SAAS,WAAc,KAAM;AACvB,MAAA,OAAO,eAAe,WAAW;AAC5B,WAAA;AAAA,EAAA;AAET,QAAM,wBAAwB;AAChC;SAmBgB,SAAM;AACpB,SAAO;AACT;SAGgB,QAAK;AACnB,SAAO;AACT;SAGgB,MAAG;AACjB,SAAO;AACT;SAMgB,SAAM;AACpB,SAAO;AACT;SAEgB,YAAS;AACvB,SAAO,IAAI,UAAS;AACtB;AAEM,SAAU,IAAI,OAAa;AAC/B,SAAO,IAAI,UAAW,EAAC,IAAI,KAAK,EAAE,MAAM,KAAK;AAC/C;AAEM,SAAU,OAAO,OAAa;AAClC,SAAO,IAAI,UAAW,EAAC,OAAO,KAAK,EAAE,MAAM,QAAQ;AACrD;SAEgB,WAAQ;AACtB,SAAO,IAAI,UAAS,EAAG,SAAU,EAAC,MAAM,UAAU;AACpD;SAEgB,WAAQ;AACtB,SAAO,IAAI,UAAS,EAAG,SAAU,EAAC,MAAM,UAAU;AACpD;AASA,IAAA;AAAA;AAAA,EAAA,WAAA;AA6CE,aAAAC,aAAA;AAAA,UAKC,QAAA;AAjDmB,WAAA,MAAG,eAAe;AAErB,WAAA,SAAS;AAET,WAAA,UAA4B;AAC5B,WAAA,QAA0B;AAC1B,WAAA,QAA0B;AAE1B,WAAA,SAA2B;AAC3B,WAAA,QAA0B;AAE1B,WAAA,WAAW;AAGX,WAAA,SAAiB;AAEjB,WAAA,WAAmB;AACnB,WAAA,WAAmB;kBAEZ,IAAI,IAAI,IAAI;AAQnB,WAAA,aAAkE,CAAA;AAClE,WAAA,SAA8B,CAAA;AAC9B,WAAA,SAA8B,CAAA;AAC9B,WAAA,eAA6B,CAAA;AAE7B,WAAA,cAA4C,CAAA;AAC5C,WAAA,aAA2C,CAAA;AAK5D,WAAU,aAAG;AAygBL,WAAc,iBAAG;AA8WR,WAAA,yBAAyB;AACzB,WAAA,0BAA0B;AAE3C,WAAA,kBAAkB,SAAC,SAAiB,KAAa,MAAY;AACvD,YAAA,CAAC,MAAK,aAAa,QAAQ;AACtB,iBAAA;AAAA,QAAA;AAIH,YAAA,SAAS,MAAK,4BAA4B;AAChD,cAAK,0BAA0B;AAC/B,YAAI,QAAQ;AACH,iBAAA;AAAA,QAAA;AAGH,YAAA,OAAO,MAAK,aAAa,CAAC;AAEhC,YAAM,QAAQ,KAAK,KAAK,OAAM,SAAS,KAAK,IAAI;AAGhD,YAAI,OAAO;AACT,cAAI,SAAS,MAAK,aAAa,CAAC,GAAG;AACjC,kBAAK,aAAa;;AAEd,cAAA,OAAO,KAAK;AAClB,cAAI,MAAM;AACH,kBAAA,aAAa,QAAQ,IAAI;AAAA,UAAA;AAAA,QAChC;AAGK,eAAA;AAAA,MACT;AA/4BQ,YAAA;AACN,UAAI,gBAAgBA,YAAW;AACxB,aAAA,MAAM,KAAK,YAAY,IAAI;AAAA,MAAA;AAAA,IAClC;AAGFA,eAAM,UAAA,SAAN,SAAO,UAAgB;AAAhB,UAAA,aAAA,QAAA;AAAgB,mBAAA;AAAA,MAAA;AACrB,UAAI,aAAa,MAAM;AACd,eAAA,KAAK,KAAK;;AAEZ,aAAA,KAAK,KAAK;IACnB;AAGAA,eAAA,UAAA,gBAAA,WAAA;;AAEQ,UAAA,KAAI,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,OAAM;AAC9B,UAAM,aAAa,CAAC,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,KAAK,IAAI,EAAE,CAAC,CAAC,IAAI;AAC9D,aAAA;AAAA,IACT;AAGAA,eAAA,UAAA,sBAAA,WAAA;;AAEQ,UAAA,gBAAe,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,OAAM;AACzC,UAAM,aAAa,CAAC,eAChB,IACA,KAAK,IAAI,KAAK,IAAI,aAAa,CAAC,GAAG,KAAK,IAAI,aAAa,CAAC,CAAC;AACxD,aAAA;AAAA,IACT;AAGAA,eAAA,UAAA,uBAAA,WAAA;AACS,aAAA,KAAK,wBAAwB;IACtC;AAMAA,eAAA,UAAA,MAAA,SAAI,GAAqB,GAAO;AAC1B,UAAA,OAAO,MAAM,UAAU;AACpB,aAAA,KAAK,IAAI,CAAC;AACR,eAAA;AAAA,MAAA,WACE,OAAO,MAAM,UAAU;AAC5B,YAAA,OAAO,MAAM,aAAa;AACrB,iBAAA,KAAK,KAAK,IAAI,CAAC;AAAA,QAAA,OACjB;AACA,eAAA,KAAK,IAAI,GAAG,CAAC;AACX,iBAAA;AAAA,QAAA;AAAA,MACT,WACS,OAAO,MAAM,aAAa;AACnC,eAAO,KAAK;AAAA,MAAA;AAAA,IAEhB;AAIAA,eAAA,UAAA,MAAA,SAAI,GAAG,GAAI,GAAE;AACP,UAAA,OAAO,MAAM,UAAU;AACrB,YAAA;AACJ,YAAI,EAAE;AACN,YAAI,EAAE;AAAA,MAAA;AAER,WAAK,KAAK,IAAI,GAAG,GAAG,CAAC;AACd,aAAA;AAAA,IACT;AAGAA,eAAA,UAAA,UAAA,SAAQ,GAAG,GAAI,GAAE;AACf,aAAO,KAAK,IAAI,GAAG,GAAG,CAAC;AAAA,IACzB;AAEAA,eAAA,UAAA,WAAA,WAAA;AACS,aAAA,MAAM,KAAK,SAAS;AAAA,IAC7B;AAOAA,eAAE,UAAA,KAAF,SAAG,OAAc;AACX,UAAA,OAAO,UAAU,aAAa;AAChC,eAAO,KAAK;AAAA,MAAA;AAEd,WAAK,SAAS;AACP,aAAA;AAAA,IACT;AAIAA,eAAK,UAAA,QAAL,SAAM,OAAc;AACd,UAAA,OAAO,UAAU,aAAa;AAChC,eAAO,KAAK;AAAA,MAAA;AAEd,WAAK,SAAS;AACP,aAAA;AAAA,IACT;AAIAA,eAAA,UAAA,OAAA,SAAK,MAAc,OAAW;AACxB,UAAA,OAAO,UAAU,aAAa;AAChC,eAAO,KAAK,WAAW,OAAO,KAAK,OAAO,IAAI,IAAI;AAAA,MAAA;AAEnD,OAAA,KAAK,WAAW,OAAO,KAAK,SAAU,KAAK,SAAS,CAAA,GAAK,IAAI,IAAI;AAC3D,aAAA;AAAA,IACT;AAIAA,eAAO,UAAA,UAAP,SAAQ,SAAiB;AACnB,UAAA,OAAO,YAAY,aAAa;AAClC,eAAO,KAAK;AAAA,MAAA;AAEd,WAAK,WAAW;AAChB,WAAK,YAAY,KAAK,QAAQ,eAAe,EAAE;AAC/C,WAAK,UAAU,EAAE;AACjB,WAAK,MAAK;AACH,aAAA;AAAA,IACT;AAEAA,eAAA,UAAA,OAAA,WAAA;AACE,WAAK,QAAQ,KAAK;AACX,aAAA;AAAA,IACT;AAEAA,eAAA,UAAA,OAAA,WAAA;AACE,WAAK,QAAQ,IAAI;AACV,aAAA;AAAA,IACT;AAEAA,eAAA,UAAA,SAAA,WAAA;AACE,aAAO,KAAK;AAAA,IACd;AAEAA,eAAI,UAAA,OAAJ,SAAK,SAAiB;AACpB,UAAI,OAAO,KAAK;AAChB,aAAO,QAAQ,WAAW,CAAC,KAAK,UAAU;AACxC,eAAO,KAAK;AAAA,MAAA;AAEP,aAAA;AAAA,IACT;AAEAA,eAAI,UAAA,OAAJ,SAAK,SAAiB;AACpB,UAAI,OAAO,KAAK;AAChB,aAAO,QAAQ,WAAW,CAAC,KAAK,UAAU;AACxC,eAAO,KAAK;AAAA,MAAA;AAEP,aAAA;AAAA,IACT;AAEAA,eAAK,UAAA,QAAL,SAAM,SAAiB;AACrB,UAAI,OAAO,KAAK;AAChB,aAAO,QAAQ,WAAW,CAAC,KAAK,UAAU;AACxC,eAAO,KAAK;AAAA,MAAA;AAEP,aAAA;AAAA,IACT;AAEAA,eAAI,UAAA,OAAJ,SAAK,SAAiB;AACpB,UAAI,OAAO,KAAK;AAChB,aAAO,QAAQ,WAAW,CAAC,KAAK,UAAU;AACxC,eAAO,KAAK;AAAA,MAAA;AAEP,aAAA;AAAA,IACT;AAEAA,eAAA,UAAA,QAAA,SAAS,SAA8B,SAAW;AAChD,UAAM,UAAU,QAAQ;AACxB,UAAM,UAAU,QAAQ;AACxB,UAAI,QAAQ,SAAS,QAAQ,MAAM,MAAM,OAAO,GAAG;AACjD;AAAA,MAAA;AAEE,UAAA;AACA,UAAA,OAAO,UAAU,KAAK,KAAK,OAAO,IAAI,KAAK,MAAM,OAAO;AAC5D,aAAQ,QAAQ,MAAO;AACrB,eAAO,UAAU,MAAM,KAAK,OAAO,IAAI,MAAM,KAAK,OAAO;AACzD,YAAI,MAAM,MAAM,SAAS,OAAO,GAAG;AAC1B,iBAAA;AAAA,QAAA;AAAA,MACT;AAEF,aAAO,QAAQ,OAAO,QAAQ,IAAI,MAAM,OAAO;AAAA,IACjD;AAIAA,eAAA,UAAA,SAAA,SAAO,OAAgC,MAAgB;AACjD,UAAA,MAAM,QAAQ,KAAK,GAAG;AACxB,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrCA,qBAAU,OAAO,MAAM,MAAM,CAAC,CAAC;AAAA,QAAA;AAAA,MACjC,WACS,OAAO,SAAS,aAAa;AAEtC,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzCA,qBAAU,OAAO,MAAM,UAAU,CAAC,CAAC;AAAA,QAAA;AAAA,MACrC,WACS,OAAO,UAAU;AAAaA,mBAAU,OAAO,MAAM,KAAK;AAE9D,aAAA;AAAA,IACT;AAIAA,eAAA,UAAA,UAAA,SAAQ,OAAgC,MAAgB;AAClD,UAAA,MAAM,QAAQ,KAAK,GAAG;AACxB,iBAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1CA,qBAAU,QAAQ,MAAM,MAAM,CAAC,CAAC;AAAA,QAAA;AAAA,MAClC,WACS,OAAO,SAAS,aAAa;AAEtC,iBAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC9CA,qBAAU,QAAQ,MAAM,UAAU,CAAC,CAAC;AAAA,QAAA;AAAA,MACtC,WACS,OAAO,UAAU;AAAaA,mBAAU,QAAQ,MAAM,KAAK;AAE/D,aAAA;AAAA,IACT;AAEAA,eAAQ,UAAA,WAAR,SAAS,QAAiB;AACxBA,iBAAU,OAAO,QAAQ,IAAI;AACtB,aAAA;AAAA,IACT;AAEAA,eAAS,UAAA,YAAT,SAAU,QAAiB;AACzBA,iBAAU,QAAQ,QAAQ,IAAI;AACvB,aAAA;AAAA,IACT;AAEAA,eAAA,UAAA,aAAA,SAAW,SAAoB,MAAgB;AACzC,UAAA,MAAM,QAAQ,OAAO,GAAG;AAC1B,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvCA,qBAAU,YAAY,QAAQ,CAAC,GAAG,IAAI;AAAA,QAAA;AAAA,MACxC,WACS,OAAO,SAAS,aAAa;AAEtC,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzCA,qBAAU,YAAY,UAAU,CAAC,GAAG,IAAI;AAAA,QAAA;AAAA,MAC1C,WACS,OAAO,YAAY,aAAa;AACzCA,mBAAU,YAAY,SAAS,IAAI;AAAA,MAAA;AAG9B,aAAA;AAAA,IACT;AAEAA,eAAA,UAAA,aAAA,SAAW,SAAoB,MAAgB;AACzC,UAAA,MAAM,QAAQ,OAAO,GAAG;AAC1B,iBAAS,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AAC5CA,qBAAU,aAAa,QAAQ,CAAC,GAAG,IAAI;AAAA,QAAA;AAAA,MACzC,WACS,OAAO,SAAS,aAAa;AAEtC,iBAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC9CA,qBAAU,aAAa,UAAU,CAAC,GAAG,IAAI;AAAA,QAAA;AAAA,MAC3C,WACS,OAAO,YAAY,aAAa;AACzCA,mBAAU,aAAa,SAAS,IAAI;AAAA,MAAA;AAG/B,aAAA;AAAA,IACT;AAEAA,eAAW,UAAA,cAAX,SAAY,MAAe;AACzBA,iBAAU,YAAY,MAAM,IAAI;AACzB,aAAA;AAAA,IACT;AAEAA,eAAY,UAAA,eAAZ,SAAa,MAAe;AAC1BA,iBAAU,aAAa,MAAM,IAAI;AAC1B,aAAA;AAAA,IACT;AAGOA,eAAA,SAAP,SAAc,QAAmB,OAAgB;AAC/C,iBAAW,KAAK;AAChB,iBAAW,MAAM;AAEjB,YAAM,OAAM;AAEZ,UAAI,OAAO,OAAO;AAChB,eAAO,MAAM,QAAQ;AACrB,cAAM,QAAQ,OAAO;AAAA,MAAA;AAGvB,YAAM,UAAU;AAChB,aAAO,QAAQ;AAEX,UAAA,CAAC,OAAO,QAAQ;AAClB,eAAO,SAAS;AAAA,MAAA;AAGZ,YAAA,QAAQ,MAAM,OAAO,IAAI;AAE/B,YAAM,aAAa,EAAE;AACrB,aAAO,eAAe,EAAE;AACxB,aAAO,MAAK;AAAA,IACd;AAGOA,eAAA,UAAP,SAAe,QAAmB,OAAgB;AAChD,iBAAW,KAAK;AAChB,iBAAW,MAAM;AAEjB,YAAM,OAAM;AAEZ,UAAI,OAAO,QAAQ;AACjB,eAAO,OAAO,QAAQ;AACtB,cAAM,QAAQ,OAAO;AAAA,MAAA;AAGvB,YAAM,UAAU;AAChB,aAAO,SAAS;AAEZ,UAAA,CAAC,OAAO,OAAO;AACjB,eAAO,QAAQ;AAAA,MAAA;AAGX,YAAA,QAAQ,MAAM,OAAO,IAAI;AAE/B,YAAM,aAAa,EAAE;AACrB,aAAO,eAAe,EAAE;AACxB,aAAO,MAAK;AAAA,IACd;AAGOA,eAAA,eAAP,SAAoB,MAAiB,MAAe;AAClD,iBAAW,IAAI;AACf,iBAAW,IAAI;AAEf,WAAK,OAAM;AAEX,UAAM,SAAS,KAAK;AACpB,UAAM,OAAO,KAAK;AAElB,UAAI,CAAC,QAAQ;AACX;AAAA,MAAA;AAGF,WAAK,QAAQ;AAEZ,eAAS,KAAK,QAAQ,SAAW,WAAW,OAAO,SAAS;AAE7D,WAAK,UAAU;AACf,WAAK,QAAQ;AACb,WAAK,QAAQ;AAER,WAAA,QAAQ,MAAM,MAAM,IAAI;AAE7B,WAAK,aAAa,EAAE;AACpB,WAAK,MAAK;AAAA,IACZ;AAGOA,eAAA,cAAP,SAAmB,MAAiB,MAAe;AACjD,iBAAW,IAAI;AACf,iBAAW,IAAI;AAEf,WAAK,OAAM;AAEX,UAAM,SAAS,KAAK;AACpB,UAAM,OAAO,KAAK;AAElB,UAAI,CAAC,QAAQ;AACX;AAAA,MAAA;AAGF,WAAK,QAAQ;AAEZ,eAAS,KAAK,QAAQ,SAAW,WAAW,OAAO,QAAQ;AAE5D,WAAK,UAAU;AACf,WAAK,QAAQ;AACb,WAAK,QAAQ;AAER,WAAA,QAAQ,MAAM,MAAM,IAAI;AAE7B,WAAK,aAAa,EAAE;AACpB,WAAK,MAAK;AAAA,IACZ;AAEAA,eAAA,UAAA,SAAA,SAAO,OAAmB,MAAU;AAC9B,UAAA,OAAO,UAAU,aAAa;AAC5B,YAAA,MAAM,QAAQ,KAAK,GAAG;AACxB,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,uBAAW,MAAM,CAAC,CAAC,EAAE,OAAM;AAAA,UAAA;AAAA,QAC7B,WACS,OAAO,SAAS,aAAa;AACtC,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,uBAAW,UAAU,CAAC,CAAC,EAAE,OAAM;AAAA,UAAA;AAAA,QACjC,OACK;AACM,qBAAA,KAAK,EAAE;;AAEb,eAAA;AAAA,MAAA;AAGT,UAAI,KAAK,OAAO;AACT,aAAA,MAAM,QAAQ,KAAK;AAAA,MAAA;AAE1B,UAAI,KAAK,OAAO;AACT,aAAA,MAAM,QAAQ,KAAK;AAAA,MAAA;AAG1B,UAAI,KAAK,SAAS;AACZ,YAAA,KAAK,QAAQ,WAAW,MAAM;AAC3B,eAAA,QAAQ,SAAS,KAAK;AAAA,QAAA;AAEzB,YAAA,KAAK,QAAQ,UAAU,MAAM;AAC1B,eAAA,QAAQ,QAAQ,KAAK;AAAA,QAAA;AAGvB,aAAA,QAAQ,MAAM,MAAM,KAAK;AAEzB,aAAA,QAAQ,eAAe,EAAE;AAC9B,aAAK,QAAQ;;AAGf,WAAK,QAAQ,KAAK,QAAQ,KAAK,UAAU;AACzC,WAAK,aAAa,EAAE;AAEb,aAAA;AAAA,IACT;AAEAA,eAAA,UAAA,QAAA,WAAA;AACE,UAAI,QAA0B;AAC9B,UAAI,OAAO,KAAK;AAChB,aAAQ,QAAQ,MAAO;AACrB,eAAO,MAAM;AACb,cAAM,QAAQ,MAAM,QAAQ,MAAM,UAAU;AAEvC,aAAA,MAAM,OAAO,KAAK;AAAA,MAAA;AAGpB,WAAA,SAAS,KAAK,QAAQ;AAE3B,WAAK,eAAe,EAAE;AACtB,WAAK,MAAK;AACH,aAAA;AAAA,IACT;AAEAA,eAAA,UAAA,QAAA,WAAA;AACE,WAAK,YAAY,EAAE;AACd,WAAA,WAAW,KAAK,QAAQ,MAAK;AAC3B,aAAA;AAAA,IACT;AASAA,eAAA,UAAA,QAAA,SAAM,KAAyB,OAAe;AACxC,UAAA,OAAO,UAAU,aAAa;AAChC,eAAQ,KAAK,WAAW,QAAQ,KAAK,OAAO,GAAa,KAAM;AAAA,MAAA;AAE7D,UAAA,OAAO,QAAQ,UAAU;AAC3B,YAAI,OAAO;AACJ,eAAA,SAAS,KAAK,UAAU;AAC7B,cAAI,CAAC,KAAK,OAAO,GAAG,KAAK,KAAK,SAAS;AAChC,iBAAA,QAAQ,MAAM,KAAK,IAAI;AAAA,UAAA;AAE9B,eAAK,OAAO,GAAG,KAAK,KAAK,OAAO,GAAG,KAAK,KAAK;AAAA,QAAA,WACpC,KAAK,UAAU,KAAK,OAAO,GAAG,IAAI,GAAG;AAC9C,cAAI,KAAK,OAAO,GAAG,KAAK,KAAK,KAAK,SAAS;AACpC,iBAAA,QAAQ,MAAM,KAAK,KAAK;AAAA,UAAA;AAE/B,eAAK,OAAO,GAAG,IAAI,KAAK,OAAO,GAAG,IAAI;AAAA,QAAA;AAAA,MACxC;AAEE,UAAA,OAAO,QAAQ,UAAU;AAC3B,YAAI,IAAI,QAAQ;AACH,mBAAA,QAAQ,IAAI,QAAQ;AAC7B,gBAAI,IAAI,OAAO,IAAI,IAAI,GAAG;AACnB,mBAAA,MAAM,MAAM,KAAK;AAAA,YAAA;AAAA,UACxB;AAAA,QACF;AAAA,MACF;AAEK,aAAA;AAAA,IACT;AAGAA,eAAO,UAAA,UAAP,SAAQ,KAAc;AACd,UAAA,QAAQ,KAAK,KAAK;AAClB,UAAA,SAAS,KAAK,KAAK;AAClB,aAAA,IAAI,KAAK,KAAK,IAAI,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,IAChE;AAGAA,eAAA,UAAA,YAAA,WAAA;AACM,UAAA,CAAC,KAAK,UAAU;AAClB;AAAA,MAAA;AAGF,WAAK,iBAAgB;AAEjB,UAAA;AACJ,UAAI,OAAO,KAAK;AAChB,aAAQ,QAAQ,MAAO;AACrB,eAAO,MAAM;AACb,cAAM,UAAS;AAAA,MAAA;AAAA,IAEnB;AAGAA,eAAA,UAAA,mBAAA,WAAA;AAAA,IAEA;AAMAA,eAAM,UAAA,SAAN,SAAO,SAAiC;AAClC,UAAA,CAAC,KAAK,UAAU;AAClB;AAAA,MAAA;AAEI,YAAA;AAEA,UAAA,IAAI,KAAK;AACf,cAAQ,aAAa,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAG5C,WAAA,SAAS,KAAK,KAAK,UAAU,KAAK,UAAU,KAAK,QAAQ,SAAS;AACvE,UAAM,QAAQ,KAAK,KAAK,gBAAgB,KAAK;AAEzC,UAAA,QAAQ,eAAe,OAAO;AAChC,gBAAQ,cAAc;AAAA,MAAA;AAGpB,UAAA,CAAC,KAAK,gBAAgB;AAExB,aAAK,iBAAgB;AAAA,MAAA;AAEvB,WAAK,iBAAiB;AAEtB,WAAK,cAAc,OAAO;AAEtB,UAAA,QAAQ,eAAe,KAAK,QAAQ;AACtC,gBAAQ,cAAc,KAAK;AAAA,MAAA;AAGzB,UAAA;AACJ,UAAI,OAAO,KAAK;AAChB,aAAQ,QAAQ,MAAO;AACrB,eAAO,MAAM;AACb,cAAM,OAAO,OAAO;AAAA,MAAA;AAAA,IAExB;AAGAA,eAAa,UAAA,gBAAb,SAAc,SAAiC;AAAA,IAE/C;AAGAA,eAAA,UAAA,QAAA,SAAM,SAAiB,KAAa,MAAY;AAC1C,UAAA,CAAC,KAAK,UAAU;AAClB;AAAA,MAAA;AAGE,UAAA,UAAU,KAAK,YAAY;AAC7B,kBAAU,KAAK;AAAA,MAAA;AAGjB,UAAI,SAAS;AAET,UAAA,KAAK,gBAAgB,MAAM;AAC7B,iBAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAC1C,gBAAA;AACA,cAAA,SAAS,KAAK,YAAY,CAAC;AACjC,mBAAS,OAAO,KAAK,MAAM,SAAS,KAAK,IAAI,MAAM,QAAQ;AAAA,QAAA;AAAA,MAC7D;AAGE,UAAA;AACJ,UAAI,OAAO,KAAK;AAChB,aAAQ,QAAQ,MAAO;AACrB,eAAO,MAAM;AACT,YAAA,MAAM,MAAM,OAAO,GAAG;AACxB,mBAAS,MAAM,MAAM,SAAS,KAAK,IAAI,MAAM,OAAO,OAAO;AAAA,QAAA;AAAA,MAC7D;AAGE,UAAA,KAAK,eAAe,MAAM;AAC5B,iBAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AACzC,gBAAA;AACA,cAAA,SAAS,KAAK,WAAW,CAAC;AAChC,mBAAS,OAAO,KAAK,MAAM,SAAS,KAAK,IAAI,MAAM,QAAQ;AAAA,QAAA;AAAA,MAC7D;AAGK,aAAA;AAAA,IACT;AAEAA,eAAA,UAAA,OAAA,SAAK,UAAuC,QAAc;;AAAd,UAAA,WAAA,QAAA;AAAc,iBAAA;AAAA,MAAA;AACpD,UAAA,OAAO,aAAa,YAAY;AAClC;AAAA,MAAA;AAEF,UAAI,QAAQ;AACN,YAAA,KAAK,gBAAgB,MAAM;AAC7B,eAAK,cAAc,CAAA;AAAA,QAAA;AAEhB,aAAA,YAAY,KAAK,QAAQ;AAAA,MAAA,OACzB;AACD,YAAA,KAAK,eAAe,MAAM;AAC5B,eAAK,aAAa,CAAA;AAAA,QAAA;AAEf,aAAA,WAAW,KAAK,QAAQ;AAAA,MAAA;AAE/B,UAAM,oBAAkB,KAAA,KAAK,oDAAY,UAAS,YAAK,KAAK,iBAAa,QAAA,OAAA,SAAA,SAAA,GAAA,UAAS;AAC7E,WAAA,MAAM,SAAS,eAAe;AAAA,IACrC;AAEAA,eAAM,UAAA,SAAN,SAAO,UAAqC;AACtC,UAAA,OAAO,aAAa,YAAY;AAClC;AAAA,MAAA;AAEE,UAAA;AACA,UAAA,KAAK,gBAAgB,SAAS,IAAI,KAAK,YAAY,QAAQ,QAAQ,MAAM,GAAG;AACzE,aAAA,YAAY,OAAO,GAAG,CAAC;AAAA,MAAA;AAE1B,UAAA,KAAK,eAAe,SAAS,IAAI,KAAK,WAAW,QAAQ,QAAQ,MAAM,GAAG;AACvE,aAAA,WAAW,OAAO,GAAG,CAAC;AAAA,MAAA;AAAA,IAE/B;AAEAA,eAAA,UAAA,UAAA,SAAQ,UAAqB,MAAY;AAClC,WAAA,WAAW,UAAU,IAAI;AAAA,IAChC;AAEAA,eAAA,UAAA,aAAA,SAAW,UAAqB,MAAY;AAC1C,eAAS,MAAM,GAAS;AACjB,aAAA,QAAQ,KAAK,GAAG;AACnB,eAAK,OAAO,KAAK;AACjB,mBAAS,KAAK,IAAI;AAAA,QAAA,OACb;AACE,iBAAA;AAAA,QAAA;AAAA,MACT;AAEF,WAAK,KAAK,KAAK;AACR,aAAA;AAAA,IACT;AAEAA,eAAY,UAAA,eAAZ,SAAa,OAAkC;AAC7C,WAAK,OAAO,KAAK;AAAA,IACnB;AAKAA,eAAA,UAAA,KAAA,SAAG,MAAyB,UAAsC;AAChE,UAAI,CAAC,QAAQ,CAAC,KAAK,UAAU,OAAO,aAAa,YAAY;AACpD,eAAA;AAAA,MAAA;AAET,UAAI,OAAO,SAAS,YAAY,OAAO,KAAK,SAAS,YAAY;AAE/D,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,eAAK,GAAG,KAAK,CAAC,GAAG,QAAQ;AAAA,QAAA;AAAA,MAC3B,WACS,OAAO,SAAS,YAAY,KAAK,QAAQ,GAAG,IAAI,IAAI;AAEtD,eAAA,KAAK,MAAM,MAAM;AACxB,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,eAAK,IAAI,KAAK,CAAC,GAAG,QAAQ;AAAA,QAAA;AAAA,MAC5B,WACS,OAAO,SAAS,UAAU;AAC9B,aAAA,IAAI,MAAM,QAAQ;AAAA,MAAA,MAClB;AAGA,aAAA;AAAA,IACT;AAGAA,eAAA,UAAA,MAAA,SAAI,MAAc,UAAsC;AACtD,UAAI,OAAO,SAAS,YAAY,OAAO,aAAa,YAAY;AAC9D;AAAA,MAAA;AAEF,WAAK,WAAW,IAAI,IAAI,KAAK,WAAW,IAAI,KAAK;AACjD,WAAK,WAAW,IAAI,EAAE,KAAK,QAAQ;AAE9B,WAAA,MAAM,MAAM,IAAI;AAAA,IACvB;AAKAA,eAAA,UAAA,MAAA,SAAI,MAAyB,UAAsC;AACjE,UAAI,CAAC,QAAQ,CAAC,KAAK,UAAU,OAAO,aAAa,YAAY;AACpD,eAAA;AAAA,MAAA;AAET,UAAI,OAAO,SAAS,YAAY,OAAO,KAAK,SAAS,YAAY;AAE/D,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,eAAK,IAAI,KAAK,CAAC,GAAG,QAAQ;AAAA,QAAA;AAAA,MAC5B,WACS,OAAO,SAAS,YAAY,KAAK,QAAQ,GAAG,IAAI,IAAI;AAEtD,eAAA,KAAK,MAAM,MAAM;AACxB,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,eAAK,KAAK,KAAK,CAAC,GAAG,QAAQ;AAAA,QAAA;AAAA,MAC7B,WACS,OAAO,SAAS,UAAU;AAC9B,aAAA,KAAK,MAAM,QAAQ;AAAA,MAAA,MACnB;AAGA,aAAA;AAAA,IACT;AAGAA,eAAA,UAAA,OAAA,SAAK,MAAc,UAAsC;AACvD,UAAI,OAAO,SAAS,YAAY,OAAO,aAAa,YAAY;AAC9D;AAAA,MAAA;AAEI,UAAA,YAAY,KAAK,WAAW,IAAI;AACtC,UAAI,CAAC,aAAa,CAAC,UAAU,QAAQ;AACnC;AAAA,MAAA;AAEI,UAAA,QAAQ,UAAU,QAAQ,QAAQ;AACxC,UAAI,SAAS,GAAG;AACJ,kBAAA,OAAO,OAAO,CAAC;AAKpB,aAAA,MAAM,MAAM,KAAK;AAAA,MAAA;AAAA,IAE1B;AAEAA,eAAS,UAAA,YAAT,SAAU,MAAY;AACb,aAAA,KAAK,WAAW,IAAI;AAAA,IAC7B;AAEAA,eAAA,UAAA,UAAA,SAAQ,MAAc,MAAU;AACxB,UAAA,YAAY,KAAK,UAAU,IAAI;AACrC,UAAI,CAAC,aAAa,CAAC,UAAU,QAAQ;AAC5B,eAAA;AAAA,MAAA;AAET,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,kBAAU,CAAC,EAAE,MAAM,MAAM,IAAI;AAAA,MAAA;AAE/B,aAAO,UAAU;AAAA,IACnB;AAGAA,eAAA,UAAA,UAAA,SAAQ,MAAc,MAAU;AACzB,WAAA,QAAQ,MAAM,IAAI;AAChB,aAAA;AAAA,IACT;AAEAA,eAAA,UAAA,OAAA,SAAK,GAAW,GAAS;AAElB,WAAA,IAAI,SAAS,CAAC;AACd,WAAA,IAAI,UAAU,CAAC;AACb,aAAA;AAAA,IACT;AAIAA,eAAK,UAAA,QAAL,SAAM,GAAU;AAEV,UAAA,OAAO,MAAM,aAAa;AACrB,eAAA,KAAK,IAAI,OAAO;AAAA,MAAA;AAEpB,WAAA,IAAI,SAAS,CAAC;AACZ,aAAA;AAAA,IACT;AAIAA,eAAM,UAAA,SAAN,SAAO,GAAU;AAEX,UAAA,OAAO,MAAM,aAAa;AACrB,eAAA,KAAK,IAAI,QAAQ;AAAA,MAAA;AAErB,WAAA,IAAI,UAAU,CAAC;AACb,aAAA;AAAA,IACT;AAIAA,eAAA,UAAA,SAAA,SAAO,GAAwB,GAAU;AAEnC,UAAA,OAAO,MAAM,UAAU;AACzB,YAAI,EAAE;AACN,YAAI,EAAE;AAAA,MAAA;AAEH,WAAA,IAAI,WAAW,CAAC;AAChB,WAAA,IAAI,WAAW,CAAC;AACd,aAAA;AAAA,IACT;AAEAA,eAAM,UAAA,SAAN,SAAO,GAAS;AAET,WAAA,IAAI,YAAY,CAAC;AACf,aAAA;AAAA,IACT;AAIAA,eAAA,UAAA,OAAA,SAAK,GAAwB,GAAU;AAEjC,UAAA,OAAO,MAAM,UAAU;AACzB,YAAI,EAAE;AACN,YAAI,EAAE;AAAA,MAAA,WACG,OAAO,MAAM;AAAiB,YAAA;AACpC,WAAA,IAAI,SAAS,CAAC;AACd,WAAA,IAAI,SAAS,CAAC;AACZ,aAAA;AAAA,IACT;AAKAA,eAAA,UAAA,QAAA,SAAM,GAAwB,GAAU;AAElC,UAAA,OAAO,MAAM,UAAU;AACzB,YAAI,EAAE;AACN,YAAI,EAAE;AAAA,MAAA,WACG,OAAO,MAAM;AAAiB,YAAA;AACpC,WAAA,IAAI,UAAU,CAAC;AACf,WAAA,IAAI,UAAU,CAAC;AACb,aAAA;AAAA,IACT;AAEAA,eAAA,UAAA,QAAA,SAAM,GAAW,IAAW;AAErB,WAAA,IAAI,SAAS,CAAC;AACf,UAAA,OAAO,OAAO,aAAa;AACxB,aAAA,IAAI,gBAAgB,EAAE;AAAA,MAAA;AAEtB,aAAA;AAAA,IACT;AAIAA,eAAA,UAAA,QAAA,SAAM,GAAqB,GAAY,GAAW;AAC5C,UAAA;AACJ,UAAI,OAAO,MAAM,YAAY,MAAM,MAAM;AAC7B,kBAAA;AAAA,MAAA,OACL;AACL,kBAAU;AACN,YAAA,OAAO,MAAM,UAAU;AACzB,kBAAQ,WAAW;AACf,cAAA,OAAO,MAAM,UAAU;AACzB,oBAAQ,QAAQ;AACZ,gBAAA,OAAO,MAAM,WAAW;AAC1B,sBAAQ,SAAS;AAAA,YAAA;AAAA,UACnB,WACS,OAAO,MAAM,WAAW;AACjC,oBAAQ,SAAS;AAAA,UAAA;AAAA,QACnB,WACS,OAAO,MAAM,WAAW;AACjC,kBAAQ,SAAS;AAAA,QAAA;AAAA,MACnB;AAGE,UAAA,CAAC,KAAK,wBAAwB;AAC3B,aAAA,KAAK,KAAK,iBAAiB,IAAI;AACpC,aAAK,yBAAyB;AAAA,MAAA;AAGhC,WAAK,MAAK;AAGN,UAAA,CAAC,QAAQ,QAAQ;AACnB,aAAK,aAAa,SAAS;AAAA,MAAA;AAG7B,UAAM,aAAa,IAAI,WAAW,MAAM,OAAO;AAC1C,WAAA,aAAa,KAAK,UAAU;AAC1B,aAAA;AAAA,IACT;AAmCAA,eAAG,UAAA,MAAH,SAAI,OAAa;AACV,WAAA,MAAM,OAAO,KAAK;AAChB,aAAA;AAAA,IACT;AAEAA,eAAM,UAAA,SAAN,SAAO,OAAa;AACb,WAAA,MAAM,UAAU,KAAK;AACnB,aAAA;AAAA,IACT;AAEAA,eAAA,UAAA,QAAA,SAAM,MAAwB,OAAa;AAA3C,UAkDC,QAAA;AAjDC,WAAK,WAAW,KAAK;AACrB,WAAK,WAAW,KAAK;AAErB,WAAK,iBAAiB,KAAK,OAAO,KAAK,aAAa;AAC/C,WAAA,KACF,KAAK,gBAAgB,WAAA;AAChB,YAAA,MAAK,WAAW,MAAK,WAAW;AAClC;AAAA,QAAA;AAEF,cAAK,UAAU,MAAK;AAEd,YAAA,gBAAgB,MAAK,gBAAgB,MAAK;AAChD,cAAK,eAAe,MAAK;AAEzB,YAAI,QAAQ;AACZ,YAAI,SAAS;AAET,YAAA;AACA,YAAA,OAAO,MAAK,MAAM,IAAI;AAC1B,YAAI,QAAQ;AACZ,eAAQ,QAAQ,MAAO;AACd,iBAAA,MAAM,KAAK,IAAI;AAEtB,gBAAM,OAAO,IAAI;AACX,cAAA,IAAI,MAAM,IAAI,UAAU;AACxB,cAAA,IAAI,MAAM,IAAI,WAAW;AAE/B,cAAI,QAAQ,UAAU;AACnB,aAAA,UAAU,UAAU,MAAK;AAC1B,kBAAM,IAAI,SAAS,KAAK,UAAU,MAAM,IAAI,WAAW,MAAM;AACrD,oBAAA,KAAK,IAAI,OAAO,CAAC;AACzB,qBAAS,SAAS;AACD,6BAAA,MAAM,IAAI,UAAU,KAAK;AAAA,UAAA,WACjC,QAAQ,OAAO;AACvB,aAAA,UAAU,SAAS,MAAK;AACzB,kBAAM,IAAI,SAAS,KAAK,SAAS,MAAM,IAAI,WAAW,KAAK;AAC3D,oBAAQ,QAAQ;AACP,qBAAA,KAAK,IAAI,QAAQ,CAAC;AACV,6BAAA,MAAM,IAAI,UAAU,KAAK;AAAA,UAAA;AAEpC,kBAAA;AAAA,QAAA;AAEV,iBAAS,IAAI,MAAK;AAClB,kBAAU,IAAI,MAAK;AACnB,cAAK,IAAI,OAAO,KAAK,SAAS,MAAK,IAAI,SAAS,KAAK;AACrD,cAAK,IAAI,QAAQ,KAAK,UAAU,MAAK,IAAI,UAAU,MAAM;AAAA,MAAA,CACzD;AAEG,aAAA;AAAA,IACT;AAGAA,eAAA,UAAA,MAAA,WAAA;AACE,aAAO,KAAK,SAAQ;AAAA,IACtB;AAGAA,eAAA,UAAA,QAAA,WAAA;AACE,aAAO,KAAK,SAAQ;AAAA,IACtB;AAKAA,eAAA,UAAA,WAAA,WAAA;AAAA,UA8BC,QAAA;AA7BC,WAAK,WAAW,KAAK;AAErB,WAAK,iBAAiB,KAAK,OAAO,KAAK,aAAa;AAC/C,WAAA,KACF,KAAK,gBAAgB,WAAA;AAChB,YAAA,MAAK,WAAW,MAAK,WAAW;AAClC;AAAA,QAAA;AAEF,cAAK,UAAU,MAAK;AAEpB,YAAI,QAAQ;AACZ,YAAI,SAAS;AACT,YAAA;AACA,YAAA,OAAO,MAAK,MAAM,IAAI;AAC1B,eAAQ,QAAQ,MAAO;AACd,iBAAA,MAAM,KAAK,IAAI;AACtB,gBAAM,OAAO,IAAI;AACX,cAAA,IAAI,MAAM,IAAI,UAAU;AACxB,cAAA,IAAI,MAAM,IAAI,WAAW;AACvB,kBAAA,KAAK,IAAI,OAAO,CAAC;AAChB,mBAAA,KAAK,IAAI,QAAQ,CAAC;AAAA,QAAA;AAE7B,iBAAS,IAAI,MAAK;AAClB,kBAAU,IAAI,MAAK;AACnB,cAAK,IAAI,OAAO,KAAK,SAAS,MAAK,IAAI,SAAS,KAAK;AACrD,cAAK,IAAI,QAAQ,KAAK,UAAU,MAAK,IAAI,UAAU,MAAM;AAAA,MAAA,CACzD;AAEG,aAAA;AAAA,IACT;AAKAA,eAAA,UAAA,WAAA,WAAA;AAAA,UAmBC,QAAA;AAlBC,WAAK,iBAAiB,KAAK,OAAO,KAAK,aAAa;AAC/C,WAAA,KACF,KAAK,gBAAgB,WAAA;AACd,YAAA,SAAS,MAAK;AACpB,YAAI,QAAQ;AACJ,cAAA,QAAQ,OAAO,IAAI,OAAO;AAChC,cAAI,MAAK,IAAI,OAAO,KAAK,OAAO;AACzB,kBAAA,IAAI,SAAS,KAAK;AAAA,UAAA;AAEnB,cAAA,SAAS,OAAO,IAAI,QAAQ;AAClC,cAAI,MAAK,IAAI,QAAQ,KAAK,QAAQ;AAC3B,kBAAA,IAAI,UAAU,MAAM;AAAA,UAAA;AAAA,QAC3B;AAAA,SAGJ,IAAI;AAEC,aAAA;AAAA,IACT;AAMAA,eAAO,UAAA,UAAP,SAAQ,KAAW;AACjB,WAAK,WAAW;AACT,aAAA;AAAA,IACT;AAKAA,eAAO,UAAA,UAAP,SAAQ,OAAa;AACnB,WAAK,WAAW;AACT,aAAA;AAAA,IACT;AACDA,WAAAA;AAAAA,EAAA,EAAA;AAAA;AC9pCK,SAAU,OAAO,OAA6B;AAC5CC,MAAAA,UAAS,IAAI;AACVA,WAAAA,QAAO,QAAQ,KAAK;AACtBA,SAAAA;AACT;AAEA,IAAA;AAAA;AAAA,EAAA,SAAA,QAAA;AAA4B,cAASC,SAAA,MAAA;AAOnC,aAAAA,UAAA;AACE,UAAA,QAAA,qBAAQ;AAPO,YAAA,WAA2B;AAE3B,YAAA,SAAyB;AACzB,YAAA,SAAkB;AAClB,YAAA,aAAsB;AAiD/B,YAAgB,mBAAG;AA7CzB,YAAK,MAAM,QAAQ;;;AAGrBA,YAAO,UAAA,UAAP,SAAQ,OAA4B;AAClC,WAAK,SAAS,QAAQ,KAAK,EAAE,IAAG;AAChC,UAAI,KAAK,QAAQ;AACf,aAAK,IAAI,SAAS,KAAK,OAAO,UAAU;AACxC,aAAK,IAAI,UAAU,KAAK,OAAO,WAAW;AAG1C,YAAI,KAAK,QAAQ;AACf,eAAK,WAAW,IAAI,iBAAiB,KAAK,QAAQ,MAAM;AAAA,QAAA,WAC/C,KAAK,YAAY;AAC1B,eAAK,WAAW,IAAI,iBAAiB,KAAK,QAAQ,SAAS;AAAA,QAAA,OACtD;AACL,eAAK,WAAW,IAAI,YAAY,KAAK,MAAM;AAAA,QAAA;AAAA,MAC7C,OACK;AACA,aAAA,IAAI,SAAS,CAAC;AACd,aAAA,IAAI,UAAU,CAAC;AACpB,aAAK,WAAW;AAAA,MAAA;AAEX,aAAA;AAAA,IACT;AAGAA,YAAK,UAAA,QAAL,SAAM,OAA4B;AACzB,aAAA,KAAK,QAAQ,KAAK;AAAA,IAC3B;AAEAA,YAAI,UAAA,OAAJ,SAAK,OAAa;AAChB,WAAK,SAAS;AACd,UAAMb,WAAU,IAAI,iBAAiB,KAAK,QAAQ,MAAM;AACxD,WAAK,WAAWA;AACT,aAAA;AAAA,IACT;AAEAa,YAAO,UAAA,UAAP,SAAQ,OAAa;AACnB,WAAK,aAAa;AAClB,UAAMb,WAAU,IAAI,iBAAiB,KAAK,QAAQ,SAAS;AAC3D,WAAK,WAAWA;AACT,aAAA;AAAA,IACT;AAMAa,YAAA,UAAA,mBAAA,WAAA;AACE,UAAI,CAAC,KAAK;AAAQ;AACZ,UAAA,aAAa,KAAK;AACxB,WAAK,iBAAiB,aAAa;AACnC,UAAM,UAAU,KAAK,OAAO,UAAU,KAAK,gBAAgB;AAC3D,UAAI,YAAY,MAAM;AAEd,YAAA,IAAI,KAAK,OAAO;AAChB,YAAA,IAAI,KAAK,OAAO;AACjB,aAAA,KAAK,GAAG,CAAC;AAAA,MAAA;AAAA,IAElB;AAGAA,YAAa,UAAA,gBAAb,SAAc,SAAiC;AAC7C,UAAI,CAAC,KAAK;AAAU;AAEhB,UAAA,KAAK,SAAS,aAAa,GAAG;AAChC,aAAK,SAAS,KAAK,KAAK,IAAI,OAAO;AACnC,aAAK,SAAS,KAAK,KAAK,IAAI,QAAQ;AAAA,MAAA;AAGjC,WAAA,SAAS,KAAK,OAAO;AAAA,IAC5B;AACDA,WAAAA;AAAAA,EAAA,EAjF2B,SAAS;AAAA;ACErC,IAAA;AAAA;AAAA,EAAA,SAAA,QAAA;AAAmC,cAAYC,gBAAA,MAAA;AAQ7C,aAAAA,iBAAA;AACE,UAAA,QAAA,kBAAM,SAAS,cAAc,QAAQ,CAAC,KAAE;AAJzB,YAAA,kBAAkB;;;AAUnCA,mBAAA,UAAA,UAAA,SAAQ,WAAmB,YAAoB,YAAc;AAAd,UAAA,eAAA,QAAA;AAAc,qBAAA;AAAA,MAAA;AACtD,WAAA,QAAQ,QAAQ,YAAY;AAC5B,WAAA,QAAQ,SAAS,aAAa;AACnC,WAAK,cAAc;AAAA,IACrB;AAEAA,mBAAA,UAAA,aAAA,SAAW,MAAa,YAAgB;AAA7B,UAAA,SAAA,QAAA;AAAW,eAAA;AAAA,MAAA;AACpB,aAAO,KAAK,QAAQ,WAAW,MAAM,UAAU;AAAA,IACjD;AAOAA,mBAAA,UAAA,sBAAA,WAAA;AACE,aAAO,KAAK;AAAA,IACd;AAIAA,mBAAA,UAAA,uBAAA,WAAA;AACE,aAAO,KAAK,oBAAmB;AAAA,IACjC;AAEAA,mBAAW,UAAA,cAAX,SAAY,UAA+B;AACzC,WAAK,YAAY;AAAA,IACnB;AAEAA,mBAAS,UAAA,YAAT,SAAU,QAA2B;AACnC,WAAK,UAAU;AAAA,IACjB;AAGAA,mBAAS,UAAA,YAAT,SAAU,SAAgC;AACxC,UAAM,gBAAgB,QAAQ;AAC9B,UAAM,iBAAiB,KAAK;AAE5B,UAAM,oBAAoB,iBAAiB;AAC3C,UAAM,oBACJ,mBAAmB,KAAK,oBAAoB,QAAQ,oBAAoB;AAE1E,UAAI,mBAAmB;AACrB,aAAK,kBAAkB;AAAA,MAAA;AAGzB,UAAM,aAAa,KAAK,YAAY,KAAK,UAAU,KAAK,IAAI,IAAI;AAC1D,UAAA,iBAAiB,KAAK,iBAAiB;AAE7C,UAAI,qBAAqB,gBAAgB;AACvC,aAAK,eAAe;AACpB,aAAK,kBAAkB;AAEnB,YAAA,OAAO,KAAK,YAAY,YAAY;AACjC,eAAA,QAAQ,KAAK,IAAI;AAAA,QAAA;AAEjB,eAAA;AAAA,MAAA;AAAA,IAEX;AAGAA,mBAAA,UAAA,OAAA,SAAK,OAAe,QAAgB,YAAkB;AAC/C,WAAA,QAAQ,OAAO,QAAQ,UAAU;AAC/B,aAAA;AAAA,IACT;AAGAA,mBAAA,UAAA,UAAA,SAAQ,MAAa,YAAgB;AAA7B,UAAA,SAAA,QAAA;AAAW,eAAA;AAAA,MAAA;AACV,aAAA,KAAK,WAAW,MAAM,UAAU;AAAA,IACzC;AAGAA,mBAAM,UAAA,SAAN,SAAO,qBAA8C;AAC/C,UAAA,OAAO,wBAAwB,YAAY;AAC7C,4BAAoB,KAAK,MAAM,KAAK,WAAA,CAAY;AAAA,MAAA,WACvC,OAAO,wBAAwB,aAAa;AACjD,YAAA,OAAO,KAAK,YAAY,YAAY;AACjC,eAAA,QAAQ,KAAK,IAAI;AAAA,QAAA;AAAA,MACxB;AAGK,aAAA;AAAA,IACT;AACDA,WAAAA;AAAAA,EAAA,EAlGkC,YAAY;AAAA;AAsH/B,SAAA,OAAO,MAAO,YAAa,qBAAoB;AACzD,MAAA,OAAO,SAAS,YAAY;AACxB,QAAA,YAAU,IAAI;AACE,0BAAA;AACtB,cAAQ,UAAU,WAAA;AAChB,0BAAoB,KAAK,WAAS,UAAQ,WAAA,CAAY;AAAA,IAAA,CACvD;AACM,WAAA;AAAA,EAAA,WACE,OAAO,eAAe,YAAY;AACrC,QAAA,YAAU,IAAI;AACE,0BAAA;AACtB,cAAQ,UAAU,WAAA;AAChB,0BAAoB,KAAK,WAAS,UAAQ,WAAW,IAAI,CAAC;AAAA,IAAA,CAC3D;AACM,WAAA;AAAA,EAAA,WACE,OAAO,wBAAwB,YAAY;AAC9C,QAAA,YAAU,IAAI;AACpB,cAAQ,UAAU,WAAA;AAChB,0BAAoB,KAAK,WAAS,UAAQ,WAAW,MAAM,UAAU,CAAC;AAAA,IAAA,CACvE;AACM,WAAA;AAAA,EAAA,OACF;AACC,QAAAd,WAAU,IAAI;AACb,WAAAA;AAAA,EAAA;AAEX;AAGgB,SAAA,YACd,oBACA,sBAA6D;AAA7D,MAAA,yBAAA,QAAA;AAAA,2BAAA,WAAA;AAAyD,aAAA;AAAA,IAAA;AAAA,EAAA;AAEnD,MAAAY,UAAS,IAAI;AACb,MAAAZ,WAAU,IAAI;AAEpB,EAAAY,QAAO,QAAQZ,QAAO;AAEtB,EAAAA,SAAQ,UAAU,WAAA;AAChB,uBAAmB,MAAMA,SAAQ,iBAAiBA,UAASY,OAAM;AAAA,EAAA,CAClE;AAED,EAAAZ,SAAQ,YAAY,oBAAoB;AAEjC,SAAAY;AACT;AC5KO,IAAM,gBAAgB;AACtB,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,aAAa;AACnB,IAAM,iBAAiB;AAGvB,IAAM,gBAAgB;AAEtB,IAAM,cAAc;AAE3B,IAAA;AAAA;AAAA,EAAA,WAAA;AAAA,aAAAG,cAAA;AAAA,IAAA;AAIEA,gBAAK,UAAA,QAAL,SAAM,KAAe;AACnB,UAAI,KAAK;AACP,YAAI,IAAI,KAAK;AACb,YAAI,IAAI,KAAK;AAAA,MAAA,OACR;AACC,cAAA;AAAA,UACJ,GAAG,KAAK;AAAA,UACR,GAAG,KAAK;AAAA;;AAGL,aAAA;AAAA,IACT;AAEAA,gBAAA,UAAA,WAAA,WAAA;AACE,cAAQ,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI;AAAA,IACxC;AACDA,WAAAA;AAAAA,EAAA,EAAA;AAAA;AAGD,IAAA;AAAA;AAAA,EAAA,WAAA;AAAA,aAAAC,yBAAA;AAGW,WAAA,MAAM,IAAI,WAAU;AAAA,IAAA;AAM7BA,2BAAK,UAAA,QAAL,SAAM,KAAe;AACnB,UAAI,KAAK;AACP,YAAI,IAAI,KAAK;AACb,YAAI,IAAI,KAAK;AAAA,MAAA,OACR;AACC,cAAA;AAAA,UACJ,GAAG,KAAK;AAAA,UACR,GAAG,KAAK;AAAA;;AAGL,aAAA;AAAA,IACT;AAEAA,2BAAA,UAAA,WAAA,WAAA;AACS,aAAA,KAAK,OAAO,QAAQ,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI;AAAA,IAC3D;AACDA,WAAAA;AAAAA,EAAA,EAAA;AAAA;AAGD,IAAA;AAAA;AAAA,EAAA,WAAA;AAAA,aAAAC,gBAAA;AACE,WAAI,OAAW;AACf,WAAC,IAAW;AACZ,WAAC,IAAW;AACZ,WAAS,YAAW;AACpB,WAAK,QAAY;AACjB,WAAI,OAAS;AACb,WAAS,YAAuB;AAAA,IAAA;AAChCA,kBAAA,UAAA,WAAA,WAAA;AACS,aAAA,KAAK,OAAO,QAAQ,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI;AAAA,IAC3D;AACDA,WAAAA;AAAAA,EAAA,EAAA;AAAA;AAGgB,IAAM,iBAAiB,IAAI;AAE3B,IAAM,UAAU,IAAI;AAGrC,IAAA;AAAA;AAAA,EAAA,WAAA;AAAA,aAAAC,WAAA;AAAA,UAyNC,QAAA;AAvNC,WAAK,QAAG;AAkDR,WAAS,YAAgB;AACzB,WAAU,aAAgB;AAEf,WAAA,cAAG,SAAC,OAA8B;AAC3CA,iBAAQ,SAAS,QAAQ,SAAS,QAAQ,MAAM,iBAAiB,MAAM,IAAI;AAC3E,cAAM,eAAc;AACpB,cAAK,WAAW,KAAK;AAChB,cAAA,cAAc,MAAM,MAAM,KAAK;AAE/B,cAAA,YAAY,SAAS,MAAK,SAAS;AACnC,cAAA,YAAY,eAAe,MAAK,UAAU;AAAA,MACjD;AAEU,WAAA,aAAG,SAAC,OAA8B;AAC1C,cAAM,eAAc;AACpB,cAAK,WAAW,KAAK;AAChB,cAAA,cAAc,MAAM,MAAM,KAAK;AAAA,MACtC;AAES,WAAA,YAAG,SAAC,OAA8B;;AACzC,cAAM,eAAc;AAEpBA,iBAAQ,SAAS,QAAQ,SAAS,QAAQ,MAAM,eAAe,MAAM,IAAI;AACpE,cAAA,cAAc,MAAM,MAAM,KAAK;AAEhC,YAAA,MAAK,UAAU,QAAQ;AACzBA,mBAAQ,SAAS,QAAQ,SAAS,QAAQ,MAAM,mBAAmB,MAAM,OAAM,KAAA,MAAK,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,MAAM;AACrG,gBAAK,cAAc,SAAS,OAAO,MAAK,SAAS;AAAA,QAAA;AAEnD,cAAK,WAAW,SAAS;AAAA,MAC3B;AAEY,WAAA,eAAG,SAAC,OAA2C;;AACrD,YAAA,MAAK,WAAW,QAAQ;AAC1BA,mBAAQ,SAAS,QAAQ,SAAS,QAAQ,MAAM,kBAAkB,MAAM,OAAM,KAAA,MAAK,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,MAAM;AACpG,gBAAK,cAAc,eAAe,OAAO,MAAK,UAAU;AAAA,QAAA;AAE1D,cAAK,UAAU,SAAS;AAAA,MAC1B;AAsFA,WAAA,aAAa,SAACR,YAAsB,SAAqB;AACvD,eAAO,CAACA,WAAU,MAAM,QAAQ,IAAI;AAAA,MACtC;AAEA,WAAA,WAAW,SAACA,YAAsB,SAAqB;AAErD,uBAAe,MAAM,QAAQ;AAC7B,uBAAe,OAAO,QAAQ;AAC9B,uBAAe,YAAY,QAAQ;AACpB,uBAAA,IAAI,IAAI,QAAQ;AAChB,uBAAA,IAAI,IAAI,QAAQ;AAE/B,YAAM,YAAYA,WAAU,UAAU,QAAQ,IAAI;AAClD,YAAI,CAAC,WAAW;AACd;AAAA,QAAA;AAGF,QAAAA,WAAU,SAAS,QAAU,EAAA,IAAI,SAAS,cAAc;AAKlD,YAAA,gBAAgBA,eAAc,QAAQ,QAAQA,WAAU,KAAK,KAAK,KAAKA,WAAU,QAAQ,cAAc;AAC7G,YAAI,CAAC,eAAe;AAClB;AAAA,QAAA;AAGF,YAAI,QAAQ,WAAW;AACb,kBAAA,UAAU,KAAKA,UAAS;AAAA,QAAA;AAIlC,YAAI,QAAQ,OAAO;AAEjB,cAAI,SAAO;AACX,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,qBAAO,UAAU,CAAC,EAAE,KAAKA,YAAW,cAAc,IAAI,OAAO;AAAA,UAAA;AAExD,iBAAA;AAAA,QAAA;AAAA,MAEX;AAAA,IAAA;AAjNAQ,aAAA,UAAA,QAAA,SAAM,OAAa,MAAiB;AAApC,UAyBC,QAAA;AAxBC,WAAK,QAAQ;AACb,WAAK,OAAO;AAEZ,WAAK,QAAQ,MAAM,SAAQ,EAAG,SAAS;AACjC,YAAA,GAAG,YAAY,SAAC,UAAkB;;AACjC,cAAA,SAAQ,KAAA,SAAS,WAAS,QAAA,OAAA,SAAA,KAAA,MAAK;AAAA,MAAA,CACrC;AAKI,WAAA,iBAAiB,cAAc,KAAK,WAAW;AAC/C,WAAA,iBAAiB,YAAY,KAAK,SAAS;AAC3C,WAAA,iBAAiB,aAAa,KAAK,UAAU;AAC7C,WAAA,iBAAiB,eAAe,KAAK,YAAY;AAEjD,WAAA,iBAAiB,aAAa,KAAK,WAAW;AAC9C,WAAA,iBAAiB,WAAW,KAAK,SAAS;AAC1C,WAAA,iBAAiB,aAAa,KAAK,UAAU;AAEzC,eAAA,iBAAiB,WAAW,KAAK,YAAY;AAC/C,aAAA,iBAAiB,QAAQ,KAAK,YAAY;AAE1C,aAAA;AAAA,IACT;AAEAA,aAAA,UAAA,UAAA,WAAA;AACE,UAAM,OAAO,KAAK;AAEb,WAAA,oBAAoB,cAAc,KAAK,WAAW;AAClD,WAAA,oBAAoB,YAAY,KAAK,SAAS;AAC9C,WAAA,oBAAoB,aAAa,KAAK,UAAU;AAChD,WAAA,oBAAoB,eAAe,KAAK,YAAY;AAEpD,WAAA,oBAAoB,aAAa,KAAK,WAAW;AACjD,WAAA,oBAAoB,WAAW,KAAK,SAAS;AAC7C,WAAA,oBAAoB,aAAa,KAAK,UAAU;AAE5C,eAAA,oBAAoB,WAAW,KAAK,YAAY;AAClD,aAAA,oBAAoB,QAAQ,KAAK,YAAY;AAE7C,aAAA;AAAA,IACT;AA6CAA,aAAU,UAAA,aAAV,SAAW,OAA8B;;AACvC,UAAM,OAAO,KAAK;AACd,UAAA;AACA,UAAA;gBAGC,MAAqB,aAAS,QAAA,OAAA,SAAA,SAAA,GAAA,QAAQ;AACpC,YAAA,MAAqB,QAAQ,CAAC,EAAE;AAChC,YAAA,MAAqB,QAAQ,CAAC,EAAE;AAAA,MAAA,OAChC;AACL,YAAK,MAAqB;AAC1B,YAAK,MAAqB;AAAA,MAAA;AAGtB,UAAA,OAAO,KAAK;AAClB,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK,aAAa;AACvB,WAAK,KAAK,YAAY;AAEd,cAAA,IAAI,IAAI,KAAK;AACb,cAAA,IAAI,IAAI,KAAK;AAAA,IACvB;AAKAA,aAAA,UAAA,cAAA,SAAY,MAAc,QAAmB;AAC3C,UAAM,UAAU;AAEhB,cAAQ,OAAO;AACf,cAAQ,OAAO,KAAK;AACpB,cAAQ,QAAQ;AAChB,cAAQ,YAAY;AACpB,cAAQ,UAAU,SAAS;AAE3B,WAAK,MAAM,MACT;AAAA,QACE,SAAS;AAAA,QACT,SAAS;AAAA,QACT,OAAO,KAAK;AAAA,QACZ,KAAK,KAAK;AAAA,SAEZ,OAAO;AAAA,IAEX;AAEAA,aAAA,UAAA,gBAAA,SAAc,MAAc,OAAgB,SAAqB;AAC/D,UAAM,UAAU;AAEhB,cAAQ,OAAO;AACf,cAAQ,OAAO,KAAK;AACpB,cAAQ,QAAQ;AACR,cAAA,YAAY,KAAK;AACzB,cAAQ,YAAY;AAEhB,UAAA,SAAS,eAAe,SAAS,aAAa;AAChDA,iBAAQ,SAAS,QAAQ,SAAS,QAAQ,MAAM,yBAAyB,SAAS,YAAA,QAAA,8BAAA,QAAS,MAAM;AAAA,MAAA;AAGnG,UAAI,SAAS;AACX,eAAO,QAAQ,QAAQ;AACf,cAAAR,aAAY,QAAQ;AAC1B,cAAI,KAAK,SAASA,YAAW,OAAO,GAAG;AACrC;AAAA,UAAA;AAAA,QACF;AAEF,gBAAQ,SAAS;AAAA,MAAA,OACZ;AACL,aAAK,MAAM,MACT;AAAA,UACE,SAAS;AAAA,UACT,SAAS;AAAA,UACT,OAAO,KAAK;AAAA,UACZ,KAAK,KAAK;AAAA,WAEZ,OAAO;AAAA,MAAA;AAAA,IAGb;AA7KOQ,aAAK,QAAG;AAwNhBA,WAAAA;AAAAA,EAAA,EAAA;AAAA;AAGM,IAAM,QAAQ;AAAA,EACnB,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM;AAAA,EACN,KAAK;AAAA,EACL,QAAQ;;ACpTO,IAAM,QAAgB;SAEvB,QAAK;AACnB,WAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AACpC,UAAA,CAAC,EAAE;;AAEb;SAEgB,SAAM;AACpB,WAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AACpC,UAAA,CAAC,EAAE;;AAEb;AAEM,SAAU,MAAM,SAAwB;AAAxB,MAAA,YAAA,QAAA;AAAA,cAAwB,CAAA;AAAA,EAAA;AACtC,MAAA,OAAO,IAAI;AAEjB,OAAK,MAAM,OAAO;AAElB,OAAK,UAAU,IAAI,QAAA,EAAU,MAAM,MAAM,KAAK,GAAkB;AACzD,SAAA;AACT;AA0BA,IAAA;AAAA;AAAA,EAAA,SAAA,QAAA;AAA0B,cAASC,OAAA,MAAA;AAwBjC,aAAAA,QAAA;AACE,UAAA,QAAA,qBAAQ;AAxBV,YAAM,SAA6B;AACnC,YAAG,MAA6B;AAChC,YAAO,UAAoC;AAE1B,YAAA,aAAa;AACb,YAAA,cAAc;AACd,YAAA,aAAa;AACb,YAAA,eAAe;AACf,YAAA,gBAAgB;AAEjC,YAAO,UAAG;AACV,YAAM,SAAG;AACT,YAAK,QAAG;AAgBH,YAAA,QAAG,SAAC,SAAwB;AAAxB,YAAA,YAAA,QAAA;AAAA,oBAAwB,CAAA;AAAA,QAAA;AAC3B,YAAA,OAAO,QAAQ,WAAW,UAAU;AACtC,gBAAK,SAAS,SAAS,eAAe,QAAQ,MAAM;AAChD,cAAA,CAAC,MAAK,QAAQ;AACR,oBAAA,MAAM,8BAA8B,QAAQ,MAAM;AAAA,UAAA;AAAA,QAC5D,WACS,QAAQ,kBAAkB,mBAAmB;AACtD,gBAAK,SAAS,QAAQ;AAAA,QAAA,WACb,QAAQ,QAAQ;AACjB,kBAAA,MAAM,6BAA6B,QAAQ,MAAM;AAAA,QAAA;AAGvD,YAAA,CAAC,MAAK,QAAQ;AAChB,gBAAK,SAAU,SAAS,eAAe,OAAO,KAC5C,SAAS,eAAe,OAAO;AAAA,QAAA;AAG/B,YAAA,CAAC,MAAK,QAAQ;AACR,kBAAA,SAAS,QAAQ,MAAM,4BAA4B;AACtD,gBAAA,SAAS,SAAS,cAAc,QAAQ;AACtC,iBAAA,OAAO,MAAK,OAAO,OAAO;AAAA,YAC/B,UAAU;AAAA,YACV,SAAS;AAAA,YACT,KAAK;AAAA,YACL,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,OAAO;AAAA,YACP,OAAO;AAAA,YACP,QAAQ;AAAA,UAAA,CACT;AAED,cAAM,OAAO,SAAS;AACtB,eAAK,aAAa,MAAK,QAAQ,KAAK,UAAU;AAAA,QAAA;AAGhD,cAAK,MAAM,MAAK;AAEhB,cAAK,UAAU,MAAK,OAAO,WAAW,IAAI;AAErC,cAAA,mBAAmB,OAAO,oBAAoB;AAC9C,cAAA,oBACH,MAAK,QAAQ,8BAA8B,KAC3C,MAAK,QAAQ,2BAA2B,KACxC,MAAK,QAAQ,0BAA0B,KACvC,MAAK,QAAQ,yBAAyB,KACtC,MAAK,QAAQ,wBAAwB,KACrC;AAEG,cAAA,aAAa,MAAK,mBAAmB,MAAK;AAM/C,cAAK,UAAU;AACf,cAAM,KAAK,KAAI;AACf,cAAK,aAAY;AAAA,MACnB;AAEiB,YAAA,iBAAiB;AAGlC,YAAA,eAAe,WAAA;AAET,YAAA,CAAC,MAAK,gBAAgB;AACxB,gBAAK,iBAAiB;AACtB,gCAAsB,MAAK,OAAO;AAAA,QAAA;AAAA,MAEtC;AAEiB,YAAA,iBAAiB;AACjB,YAAS,YAAkB;AAGrC,YAAA,UAAG,SAAC,KAAW;AACpB,cAAK,iBAAiB;AAElB,YAAA,CAAC,MAAK,WAAW,CAAC,MAAK,UAAU,CAAC,MAAK,SAAS;AAClD;AAAA,QAAA;AAGF,cAAK,aAAY;AAEX,YAAA,gBAAgB,MAAK,OAAO;AAC5B,YAAA,iBAAiB,MAAK,OAAO;AAEnC,YAAI,MAAK,eAAe,iBAAiB,MAAK,gBAAgB,gBAAgB;AAE5E,gBAAK,aAAa;AAClB,gBAAK,cAAc;AAEd,gBAAA,eAAe,gBAAgB,MAAK;AACpC,gBAAA,gBAAgB,iBAAiB,MAAK;AAEvC,cAAA,MAAK,OAAO,UAAU,MAAK,gBAAgB,MAAK,OAAO,WAAW,MAAK,eAAe;AAEnF,kBAAA,OAAO,QAAQ,MAAK;AACpB,kBAAA,OAAO,SAAS,MAAK;AAE1B,oBAAQ,SAAS,QAAQ,MACrB,cACE,MAAK,eACL,OACA,MAAK,gBACL,SACA,MAAK,aACL,SACA,MAAK,aACL,OACA,MAAK,cACL,GAAG;AAGT,kBAAK,SAAS;AAAA,cACZ,OAAO,MAAK;AAAA,cACZ,QAAQ,MAAK;AAAA,cACb,OAAO,MAAK;AAAA,YAAA,CACb;AAAA,UAAA;AAAA,QACH;AAGI,YAAA,OAAO,MAAK,kBAAkB;AACpC,YAAM,UAAU,MAAM;AAEtB,YAAI,CAAC,MAAK,WAAW,MAAK,UAAU,MAAK,OAAO;AAC9C;AAAA,QAAA;AAGF,cAAK,iBAAiB;AAEtB,cAAK,UAAS;AAEd,YAAM,cAAc,MAAK,MAAM,SAAS,KAAK,IAAI;AAC7C,YAAA,MAAK,aAAa,MAAK,WAAW;AAEpC,gBAAK,YAAY,MAAK;AACtB,gBAAK,QAAQ;AAEb,cAAI,MAAK,eAAe,KAAK,MAAK,gBAAgB,GAAG;AACnD,kBAAK,QAAQ,aAAa,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC1C,kBAAK,QAAQ,UAAU,GAAG,GAAG,MAAK,cAAc,MAAK,aAAa;AAC9D,gBAAA,MAAK,gBAAgB,GAAG;AACrB,oBAAA,YAAY,MAAK,OAAO;AAAA,YAAA;AAE1B,kBAAA,OAAO,MAAK,OAAO;AAAA,UAAA;AAAA,mBAEjB,aAAa;AAEtB,gBAAK,QAAQ;AAAA,QAAA,OACR;AAEL,gBAAK,QAAQ;AAAA,QAAA;AAGT,cAAA,MAAM,UAAU,MAAO,UAAU;AAAA,MACzC;AAGA,YAAa,gBAAG;AAjKd,YAAK,MAAM,MAAM;;;AAmKXA,UAAW,UAAA,cAAnB,SAAoB,SAAiC;AACnD,UAAM,OAAO,OAAO,KAAK,kBAAkB,WAAW,KAAK,gBAAgB;AACrE,UAAA,IAAI,KAAK;AACf,cAAQ,aAAa,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAC3C,UAAA,YAAY,IAAI,EAAE;AAExB,cAAQ,UAAS;AACT,cAAA,OAAO,GAAG,CAAC;AACX,cAAA,OAAO,GAAG,MAAM,IAAI;AAC5B,cAAQ,OAAO,OAAO,MAAM,MAAM,IAAI;AAC9B,cAAA,OAAO,GAAG,IAAI;AACtB,cAAQ,OAAO,MAAO,MAAM,MAAM,IAAI;AAC9B,cAAA,OAAO,GAAG,MAAM,IAAI;AAC5B,cAAQ,cAAc;AACtB,cAAQ,WAAW;AACnB,cAAQ,UAAU;AAClB,cAAQ,YAAY;AACpB,cAAQ,OAAM;AAEd,cAAQ,UAAS;AACT,cAAA,OAAO,GAAG,CAAC;AACX,cAAA,OAAO,MAAM,MAAM,CAAC;AAC5B,cAAQ,OAAO,MAAM,MAAM,OAAO,IAAI;AAC9B,cAAA,OAAO,MAAM,CAAC;AACtB,cAAQ,OAAO,MAAM,MAAM,MAAO,IAAI;AAC9B,cAAA,OAAO,MAAM,MAAM,CAAC;AAC5B,cAAQ,cAAc;AACtB,cAAQ,WAAW;AACnB,cAAQ,UAAU;AAClB,cAAQ,YAAY;AACpB,cAAQ,OAAM;AAAA,IAChB;AAEAA,UAAA,UAAA,SAAA,WAAA;AACM,UAAA,KAAK,SAAS,KAAK,QAAQ;AAC7B,aAAK,aAAY;AAAA,MAAA;AAEnB,WAAK,SAAS;AACd,WAAK,QAAQ;AACb,WAAK,QAAQ,QAAQ;AACd,aAAA;AAAA,IACT;AAEAA,UAAA,UAAA,QAAA,WAAA;AACM,UAAA,CAAC,KAAK,QAAQ;AAChB,aAAK,QAAQ,OAAO;AAAA,MAAA;AAEtB,WAAK,SAAS;AACP,aAAA;AAAA,IACT;AAGAA,UAAA,UAAA,QAAA,WAAA;AACM,UAAA,KAAK,SAAS,KAAK,QAAQ;AAC7B,aAAK,aAAY;AAAA,MAAA;AAEnB,WAAK,QAAQ;AACb,aAAO,OAAK,UAAC,MAAK,KAAA,IAAA;AAAA,IACpB;AAEAA,UAAA,UAAA,UAAA,WAAA;;AACE,WAAK,UAAU;AACT,UAAA,QAAQ,MAAM,QAAQ,IAAI;AAChC,UAAI,SAAS,GAAG;AACR,cAAA,OAAO,OAAO,CAAC;AAAA,MAAA;AAGvB,OAAA,KAAA,KAAK,aAAS,QAAA,OAAA,SAAA,SAAA,GAAA;AACP,aAAA;AAAA,IACT;AAEAA,UAAU,UAAA,aAAV,SAAW,OAAa;AACtB,UAAI,KAAK,KAAK;AACP,aAAA,IAAI,MAAM,kBAAkB;AAAA,MAAA;AAE5B,aAAA;AAAA,IACT;AAWAA,UAAA,UAAA,WAAA,SAAS,OAA2B,QAAiB,OAAc;AAC7D,UAAA,OAAO,UAAU,aAAa;AAEhC,eAAO,OAAO,OAAO,IAAI,KAAK,SAAS;AAAA,MAAA;AAGrC,UAAA,OAAO,UAAU,UAAU;AAC7B,YAAM,UAAU;AAChB,gBAAQ,QAAQ;AAChB,iBAAS,QAAQ;AACjB,gBAAQ,QAAQ;AAAA,MAAA;AAGlB,UAAI,OAAO,UAAU,YAAY,OAAO,WAAW,UAAU;AAC3D,aAAK,YAAY;AAAA,UACf;AAAA,UACA;AAAA,UACA,OAAO,OAAO,UAAU,WAAW,QAAQ;AAAA;AAE7C,aAAK,QAAO;AACZ,YAAM,SAAO,OAAO,OAAO,CAAA,GAAI,KAAK,SAAS;AAC7C,aAAK,MAAM;AAAA,UACT,OAAO,SAAUT,YAAS;AACxB,gBAAI,CAACA,WAAU,MAAM,UAAU,GAAG;AACzB,qBAAA;AAAA,YAAA;AAET,YAAAA,WAAU,QAAQ,YAAY,CAAC,MAAI,CAAC;AAAA,UAAA;AAAA,QACtC,CACD;AAAA,MAAA;AAGI,aAAA;AAAA,IACT;AAOAS,UAAA,UAAA,UAAA,SAAQ,OAA0B,QAAiB,MAAc;AAG/D,UAAI,OAAO,UAAU,YAAY,OAAO,WAAW,UAAU;AAC3D,aAAK,WAAW;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA;MAEO,WAAA,OAAO,UAAU,YAAY,UAAU,MAAM;AACtD,aAAK,WACA,SAAA,CAAA,GAAA,KAAK;AAAA,MAAA;AAIZ,WAAK,QAAO;AAEL,aAAA;AAAA,IACT;AAEAA,UAAM,UAAA,SAAN,SAAO,QAAc;AACnB,WAAK,UAAU;AACf,WAAK,QAAO;AACL,aAAA;AAAA,IACT;AAGAA,UAAA,UAAA,UAAA,WAAA;AACE,UAAM,UAAU,KAAK;AACrB,UAAM,WAAW,KAAK;AACtB,UAAM,SAAS,KAAK;AACpB,UAAI,YAAY,SAAS;AACvB,YAAM,gBAAgB,SAAS;AAC/B,YAAM,iBAAiB,SAAS;AAChC,YAAM,cAAc,eAAe,QAAQ,IAAI,IAAI,QAAQ,OAAO;AAClE,YAAM,eAAe,QAAQ;AAC7B,YAAM,gBAAgB,QAAQ;AAE9B,aAAK,IAAI;AAAA,UACP,OAAO;AAAA,UACP,QAAQ;AAAA,QAAA,CACT;AACI,aAAA,IAAI,eAAe,gBAAgB,WAAW;AAE7C,YAAA,WAAW,QAAQ,KAAK;AACxB,YAAA,WAAW,QAAQ,KAAK;AAE9B,YAAM,eAAc,WAAA,QAAA,WAAA,SAAA,SAAA,OAAQ,MAAK;AACjC,YAAM,eAAc,WAAA,QAAA,WAAA,SAAA,SAAA,OAAQ,MAAK;AACjC,YAAM,WAAU,WAAA,QAAA,WAAA,SAAA,SAAA,OAAQ,MAAK;AAC7B,YAAM,WAAU,WAAA,QAAA,WAAA,SAAA,SAAA,OAAQ,MAAK;AAEvB,YAAA,SAAS,KAAK,IAAI,QAAQ;AAC1B,YAAA,SAAS,KAAK,IAAI,QAAQ;AAE3B,aAAA,IAAI,UAAU,SAAS,WAAW;AAClC,aAAA,IAAI,UAAU,SAAS,WAAW;AAEvC,aAAK,IAAI,WAAW,UAAU,WAAW,SAAS,WAAW;AAC7D,aAAK,IAAI,WAAW,UAAU,WAAW,SAAS,WAAW;AAAA,iBACpD,UAAU;AACnB,aAAK,IAAI;AAAA,UACP,OAAO,SAAS;AAAA,UAChB,QAAQ,SAAS;AAAA,QAAA,CAClB;AAAA,MAAA;AAGI,aAAA;AAAA,IACT;AAGAA,UAAK,UAAA,QAAL,SAAM,GAAU;AACT,WAAA,KAAK,cAAc,IAAI,KAAK;AAC1B,aAAA;AAAA,IACT;AAGAA,UAAK,UAAA,QAAL,SAAM,GAAU;AACT,WAAA,KAAK,cAAc,IAAI,KAAK;AAC1B,aAAA;AAAA,IACT;AACDA,WAAAA;AAAAA,EAAA,EA5YyB,SAAS;AAAA;ACjDnB,SAAA,KAAK,QAA6C,KAAY;AACtEC,MAAAA,QAAO,IAAI;AACjBA,QAAK,OAAO,MAAM,EAAE,UAAU,CAAC;AACxBA,SAAAA,MAAK,IAAI,GAAG;AACZA,SAAAA;AACT;AAGiB,IAAM,MAAM;AAE7B,IAAA;AAAA;AAAA,EAAA,SAAA,QAAA;AAA0B,cAASC,OAAA,MAAA;AAcjC,aAAAA,QAAA;AACE,UAAA,QAAA,qBAAQ;AAdO,YAAA,WAA2B;AAE3B,YAAA,UAAqB,CAAA;AAKrB,YAAA,QAAgB;AAChB,YAAA,UAAkB;AAClB,YAAA,SAAiB;AAsB1B,YAAiB,oBAAG;AAEpB,YAAA,YAAY,SAAC,GAAW,KAAa,MAAY;AACvD,YAAI,MAAK,QAAQ,KAAK,MAAK,QAAQ,UAAU,GAAG;AAC9C;AAAA,QAAA;AAII,YAAA,SAAS,MAAK,qBAAqB;AACzC,cAAK,oBAAoB;AACzB,YAAI,QAAQ;AACH,iBAAA;AAAA,QAAA;AAGT,cAAK,SAAS;AACV,YAAA,MAAK,QAAQ,MAAK,KAAK;AAClB,iBAAA;AAAA,QAAA;AAET,YAAM,IAAK,MAAK,QAAQ,MAAK,MAAO;AAC/B,cAAA,SAAS,IAAI,MAAK;AACvB,cAAK,UAAU,CAAC;AAChB,YAAI,MAAK,UAAU,MAAM,MAAK,WAAW,MAAM,GAAG;AAChD,gBAAK,KAAI;AACJ,gBAAA,aAAa,MAAK;AAChB,iBAAA;AAAA,QAAA;AAEF,eAAA;AAAA,MACT;AA3CE,YAAK,MAAM,MAAM;AAEjB,YAAK,OAAO;AACP,YAAA,MAAM,MAAO,MAAK;AAElB,YAAA,KAAK,MAAK,WAAW,KAAK;;;AAIjCA,UAAa,UAAA,gBAAb,SAAc,SAAiC;AAC7C,UAAI,CAAC,KAAK;AAAU;AAEf,WAAA,SAAS,KAAK,OAAO;AAAA,IAC5B;AAgCAA,UAAG,UAAA,MAAH,SAAI,KAAY;AACV,UAAA,OAAO,QAAQ,aAAa;AAC9B,eAAO,KAAK;AAAA,MAAA;AAET,WAAA,OAAO,MAAM,IAAI,MAAM;AACvB,WAAA,MAAM,MAAO,KAAK;AAChB,aAAA;AAAA,IACT;AAGAA,UAAS,UAAA,YAAT,SAAU,QAA2C;AAC5C,aAAA,KAAK,OAAO,MAAM;AAAA,IAC3B;AAEAA,UAAM,UAAA,SAAN,SAAO,QAA2C;AAChD,WAAK,SAAS;AACd,WAAK,UAAU,QAAQ,MAAM,EAAE,MAAK;AACpC,WAAK,MAAK;AACH,aAAA;AAAA,IACT;AAEAA,UAAA,UAAA,SAAA,WAAA;AACE,aAAO,KAAK,UAAU,KAAK,QAAQ,SAAS;AAAA,IAC9C;AAEAA,UAAA,UAAA,YAAA,SAAU,OAAe,QAAc;AAAd,UAAA,WAAA,QAAA;AAAc,iBAAA;AAAA,MAAA;AACrC,WAAK,SAAS,KAAK,KAAK,OAAO,KAAK,QAAQ,MAAM,IAAI;AAC7C,eAAA,UAAU,CAAC,KAAK;AACzB,WAAK,WAAW,KAAK,QAAQ,KAAK,MAAM;AACxC,UAAI,QAAQ;AACV,aAAK,IAAI,SAAS,KAAK,SAAS,UAAU;AAC1C,aAAK,IAAI,UAAU,KAAK,SAAS,WAAW;AAAA,MAAA;AAE9C,WAAK,MAAK;AACH,aAAA;AAAA,IACT;AAEAA,UAAS,UAAA,YAAT,SAAU,MAAY;AACpB,aAAO,KAAK,UAAU,KAAK,SAAS,IAAI;AAAA,IAC1C;AAEAA,UAAA,UAAA,SAAA,SAAO,QAAgB,UAAqB;AAC1C,WAAK,UAAU,SAAS,KAAK,QAAQ,SAAS;AAC9C,WAAK,YAAY;AACjB,WAAK,KAAI;AACF,aAAA;AAAA,IACT;AAEAA,UAAI,UAAA,OAAJ,SAAK,OAAc;AACb,UAAA,OAAO,UAAU,aAAa;AAChC,aAAK,UAAU,KAAK;AACpB,aAAK,QAAQ;AAAA,MAAA,WACJ,KAAK,QAAQ,GAAG;AACzB,aAAK,QAAQ;AAAA,MAAA;AAGf,WAAK,MAAK;AACH,aAAA;AAAA,IACT;AAEAA,UAAI,UAAA,OAAJ,SAAK,OAAc;AACjB,WAAK,QAAQ;AACT,UAAA,OAAO,UAAU,aAAa;AAChC,aAAK,UAAU,KAAK;AAAA,MAAA;AAEf,aAAA;AAAA,IACT;AACDA,WAAAA;AAAAA,EAAA,EAhIyB,SAAS;AAAA;ACX7B,SAAU,SAAS,OAAqE;AAC5F,SAAO,IAAI,SAAA,EAAW,OAAO,KAAK;AACpC;AAEA,IAAA;AAAA;AAAA,EAAA,SAAA,QAAA;AAA8B,cAASC,WAAA,MAAA;AAMrC,aAAAA,YAAA;AACE,UAAA,QAAA,qBAAQ;AANO,YAAA,YAAuB,CAAA;AAOtC,YAAK,MAAM,UAAU;;;AAIvBA,cAAa,UAAA,gBAAb,SAAc,SAAiC;AAC7C,UAAI,CAAC,KAAK,aAAa,CAAC,KAAK,UAAU;AAAQ;AAEtC,eAAA,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,IAAI,GAAG,KAAK;AACrD,aAAK,UAAU,CAAC,EAAE,KAAK,OAAO;AAAA,MAAA;AAAA,IAElC;AAGAA,cAAO,UAAA,UAAP,SAAQ,QAAsE;AACrE,aAAA,KAAK,OAAO,MAAM;AAAA,IAC3B;AAEAA,cAAM,UAAA,SAAN,SAAO,QAAsE;AAC3E,WAAK,YAAY,CAAA;AACb,UAAA,OAAO,UAAU,UAAU;AACvB,YAAA,cAAY,QAAQ,MAAM;AAC3B,aAAA,QAAQ,SAAU,OAAa;AAC3B,iBAAA,YAAU,IAAI,KAAK;AAAA,QAC5B;AAAA,MAAA,WACS,OAAO,WAAW,UAAU;AAChC,aAAA,QAAQ,SAAU,OAAa;AAClC,iBAAO,OAAO,KAAK;AAAA,QACrB;AAAA,MAAA,WACS,OAAO,WAAW,YAAY;AACvC,aAAK,QAAQ;AAAA,MAAA;AAER,aAAA;AAAA,IACT;AAGAA,cAAQ,UAAA,WAAR,SAAS,OAA4C;AAC5C,aAAA,KAAK,MAAM,KAAK;AAAA,IACzB;AAGAA,cAAK,UAAA,QAAL,SAAM,OAA4C;AAC5C,UAAA,OAAO,UAAU,aAAa;AAChC,eAAO,KAAK;AAAA,MAAA;AAEV,UAAA,KAAK,WAAW,OAAO;AAClB,eAAA;AAAA,MAAA;AAET,WAAK,SAAS;AAEd,UAAI,UAAU,MAAM;AACV,gBAAA;AAAA,MAAA,WACC,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,KAAK,GAAG;AAC7D,gBAAQ,MAAM;;AAGX,WAAA,WAAW,KAAK,YAAY;AAEjC,UAAI,QAAQ;AACZ,UAAI,SAAS;AACb,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAC/B,YAAA,IAAI,MAAM,CAAC;AACjB,YAAM,YAAW,KAAK,UAAU,CAAC,IAAI,KAAK,MAAM,OAAO,MAAM,WAAW,IAAI,IAAI,EAAE;AACzE,iBAAA,IAAI,IAAI,KAAK,WAAW;AACzB,kBAAA,yBAAyB,OAAO,CAAC;AACjC,gBAAA,QAAQ,UAAQ;AACxB,iBAAS,KAAK,IAAI,QAAQ,UAAQ,WAAW;AAAA,MAAA;AAE1C,WAAA,IAAI,SAAS,KAAK;AAClB,WAAA,IAAI,UAAU,MAAM;AACpB,WAAA,UAAU,SAAS,MAAM;AACvB,aAAA;AAAA,IACT;AACDA,WAAAA;AAAAA,EAAA,EAhF6B,SAAS;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "x_google_ignoreList": [2]}