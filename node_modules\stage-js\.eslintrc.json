{"extends": ["plugin:@typescript-eslint/recommended", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": "tsconfig.json", "sourceType": "module"}, "plugins": ["@typescript-eslint"], "rules": {"no-var": "error", "prefer-rest-params": "off", "prefer-spread": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/ban-types": "off", "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-inferrable-types": "off", "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/typedef": ["error", {"arrowParameter": true, "propertyDeclaration": true}]}}