# Interface: AtlasDefinition

## Properties

### ~~filter()?~~

> `optional` **filter**: (`texture`) => [`AtlasTextureDefinition`](/api/interfaces/AtlasTextureDefinition)

#### Parameters

• **texture**: [`AtlasTextureDefinition`](/api/interfaces/AtlasTextureDefinition)

#### Returns

[`AtlasTextureDefinition`](/api/interfaces/AtlasTextureDefinition)

#### Deprecated

Use map

***

### image?

> `optional` **image**: `object` \| `object`

***

### ~~imagePath?~~

> `optional` **imagePath**: `string`

#### Deprecated

Use map

***

### ~~imageRatio?~~

> `optional` **imageRatio**: `number`

#### Deprecated

Use map

***

### map()?

> `optional` **map**: (`texture`) => [`AtlasTextureDefinition`](/api/interfaces/AtlasTextureDefinition)

#### Parameters

• **texture**: [`AtlasTextureDefinition`](/api/interfaces/AtlasTextureDefinition)

#### Returns

[`AtlasTextureDefinition`](/api/interfaces/AtlasTextureDefinition)

***

### name?

> `optional` **name**: `string`

***

### ppu?

> `optional` **ppu**: `number`

***

### ~~ratio?~~

> `optional` **ratio**: `number`

#### Deprecated

Use ppu

***

### textures?

> `optional` **textures**: `Record`\<`string`, [`Texture`](/api/classes/Texture) \| [`AtlasTextureDefinition`](/api/interfaces/AtlasTextureDefinition) \| `MonotypeAtlasTextureDefinition` \| `AnimAtlasTextureDefinition`\>

***

### ~~trim?~~

> `optional` **trim**: `number`

#### Deprecated
