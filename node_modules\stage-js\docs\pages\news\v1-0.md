
### V1.0 - Work in Progress

V1.0 comes with major and some backward incompatible changes, most notably:

- The code is rewritten in TypeScript, and the package type is changed to "module"
- `Stage` now is a namespace, it is not a class or callable function anymore
- The API for starting an application and preloading textures is changed
- Internal implementation of textures and components is refactored to make the library more extensible
- Some classes and functions are renamed, and some are removed and replaced by JavaScript features

#### Importing and starting

Stage.js v1.0 is rewritten in TypeScript, and ESM exports. In v1.0, all classes are exported under `Stage` namespace, and `Stage` is not a function or a type anymore.

- Use `stage = Stage.mount()` to create and mount a new app, instead of `Stage(function(stage){ }))`.
- Use `Stage.Component` as the base class for custom components, instead of `Stage`.

#### Texture atlas

New promises (async/await) are used for preloading images, and preloading textures is separated from creating and mounting an app. You need to use `await` to wait for the preloading to finish, instead of passing a callback function.

- Use `await Stage.atlas({ })` to preload images and create textures before using them, instead of `Stage({ })`.

#### Relative Paths

Relative image paths for textures are not resolved relative to absolute path anymore. You need to use absolute paths, or resolve them yourself using a build tool.

#### ESM and build files

In v1.0 there are two build files formats: ESM which is the default, and minified UMD for directly using in webpages. Build files are not CommonJS.

FastContext and Cordova custom build is dropped in v1. In previous versions FastContext was used to take advantage of native GPU rendering in Android devices when browser Canvas rendering was not GPU optimized, however this is not needed anymore.

#### API Changes

For a complete list of changes, see [CHANGELOG.md](CHANGELOG.md).
