interface RelativeIndexable<T> {
  at(index: number): T | undefined;
  values(): ArrayIterator<number>;
}

// class A implements RelativeIndexable<number> {
//   at(index: number) {
//     return 0;
//   }
//   values() {
//     return [];
//   }
// }

type RelativeIndexableConstructor<T> = new (...args: any[]) => RelativeIndexable<T>;

// class V {
//   static type<T extends RelativeIndexableConstructor<any>>(arrayType: T = A as T) {
//     return class V0 extends arrayType {
//       constructor(...args: any[]) {
//         super();
//       }
//     };
//   }
// }

// const v = V.type(A);

declare function create<T extends new(...args: any[]) => any>(type: T):
  T extends new (...args: infer A) => infer R ? { new (...args: A): R } : never;

class V {
  x = 0;
  y = 0;
  constructor(x: number, y: number) {
    this.x = x;
    this.y = y;
  }
}
const Vector2 = create(V);
type Vector2 = InstanceType<typeof Vector2>;

let r = new Vector2(4, 5);
let g: Vector2;