// @ts-check

import { PrimitiveTuple, <PERSON><PERSON> } from "../tuple/tuple.js";

/**
 * @template {any[]} T
 * @param {T} args
 * @param {{ [ K in keyof T ]: PrimitiveTuple<T[K]> }} signatures
 */
function handleOverload(signatures, args) {}


/**
 * @template T
 * @param {[ size: number ] | [ iterable: Iterable<T> ]} args
 */
function sampleFn(...args) {
  const overloadedFn = handleOverload([
    [
      new PrimitiveTuple("number"),
      (size) => new Array(size)
    ],
    [
      new PrimitiveTuple("object"),
      (iterable) => Array.from(iterable)
    ]
  ], args);
  return overloadedFn(...args);
}