
### References
- [<PERSON>'s Publications](https://box2d.org/publications/)
- Collision Detection in Interactive 3D Environments, <PERSON><PERSON> van den Bergen, 2004
- Real-Time Collision Detection, <PERSON><PERSON>, 2005

Box2D was created as part of a physics tutorial at the Game Developer
Conference. You can get these tutorials from the download section of
box2d.org.


### More Resources

After learning Planck.js basics and how to run your code, there are plenty of Box2D resources available online to learn advanced topics. Here are few great examples:

- [iforce2d](https://www.iforce2d.net/b2dtut/) - A collection of helpful Box2D tutorials  
- Box2D [tutorials](https://www.emanueleferonato.com/category/box2d/) by <PERSON><PERSON>  


If are interested in learning about algorithms used in Box2D/Planck.js following resources are helpful.

- [dyn4j Blog Posts](http://www.dyn4j.org/category/gamedev/) by <PERSON>   
