<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>{{title}}</title>
  <link rel="stylesheet" href="/styles/styles.css" />
  <script type="importmap">
    {
      "imports": {
        "planck": "../node_modules/planck/dist/planck.mjs"
      }
    }
  </script>
  <script type="module" src="./modules/main.js"></script>
</head>
<body>
  <!-- <h2>{{title}}</h2>
  <p>v{{version}}</p> -->
  <canvas id="gameCanvas" width="800" height="600"></canvas>
</body>
</html>