// @ts-check

import BiArray from "./bi-array.js";

/**
 * @template {number} X
 * @template {number} Y
 * @template T
 * @extends {BiArray<BiArray<T>>}
 */
export class BiArray2D extends BiArray {

  /** @type {[ x: X, y: Y ]} */
  #length;
  #filler;

  /**
   * @param {X} sizeX
   * @param {Y} sizeY
   * @param {(x: number, y: number) => T} [filler]
   */
  constructor(sizeX, sizeY, filler) {
    super(sizeX);
    this.#length = [ sizeX, sizeY ];
    this.#filler = filler ?? (() => null);
    this.#fill();
  }

  get length() {
    return this.#length[0] * this.#length[1];
  }
  get lengthX() {
    return this.#length[0];
  }
  get lengthY() {
    return this.#length[1];
  }

  /**
   * @template {0 | 1} K
   * @param {K} dimension
   */
  getLength(dimension) {
    return this.#length[dimension];
  }

  /**
   * @overload
   * @param {number} x
   * @param {number} y
   */
  has(x, y) {
    return super.has(x) && super.at(x).has(y);
  }

  /**
   * @param {number} x
   * @param {number} y
   */
  at(x, y) {
    return super.at(x).at(y);
  }

  /**
   * @param {number} x
   * @param {number} y
   */
  getSafe(x, y) {
    return super.at(x)?.at(y) ?? null;
  }

  /**
   * @param {number} x
   * @param {number} y
   * @param {T} value
   */
  set(x, y, value) {
    return super.at(x)?.set(y, value);
  }

  #fill() {
    this.reset(this.#length[0]);
    for(const x of super.keys()) {
      /** @type {BiArray<T>} */
      const column = new BiArray(this.#length[1]);
      super.set(x, column);
      for(const y of column.keys()) {
        column.set(y, this.#filler(x, y));
      }
    }
  }

  /**
   * @template {number} NX
   * @template {number} NY
   * @param {NX} sizeX
   * @param {NY} sizeY
   */
  toResized(sizeX, sizeY) {
    return new BiArray2D(sizeX, sizeY, (x, y) => {
      if(this.has(x, y)) {
        return super.at(x).at(y);
      }
      return this.#filler(x, y);
    });
  }

  /**
   * @template {BiArray2D<number, number, T>} A
   * @param {A} array
   * @param {{ x?: number, y?: number }} offset
   */
  replace(array, offset) {
    const startX = offset?.x ?? 0;
    const startY = offset?.y ?? 0;
    const endX = Math.min(this.#length[0], array.#length[0] + startX);
    const endY = Math.min(this.#length[1], array.#length[1] + startY);
    for(let x = startX; x < endX; x++) {
      for(let y = startY; y < endY; y++) {
        this.set(x, y, array.at(x, y));
      }
    }
  }

  /** @returns {Generator<[ x: number, y: number ]>} */
  *keys() {
    for(const x of super.keys()) {
      for(const y of super.at(x).keys()) {
        yield [x, y];
      }
    }
  }

  *values() {
    for(const x of super.keys()) {
      for(const value of super.at(x)) {
        yield value;
      }
    }
  }

  /** @returns {Generator<[ x: number, [ y: number, value: T ] ]>} */
  *entries() {
    for(const x of super.keys()) {
      for(const entry of super.at(x).entries()) {
        yield [x, entry];
      }
    }
  }

  /** @returns {Generator<[ x: number, y: number, value: T ]>} */
  *[Symbol.iterator]() {
    for(const x of super.keys()) {
      const array = super.at(x);
      for(const y of array.keys()) {
        yield [x, y, array.at(y)];
      }
    }
  }

  /** @returns {{ [k: number]: T }} */
  toObject() {
    return Object.fromEntries(this.entries());
  }

  /**
   * @template {number} X
   * @template {number} Y
   * @template T
   * @template [R=T]
   * @param {X} sizeX
   * @param {Y} sizeY
   * @param {Iterable<[number, number, T]>} iterable
   * @param {(value: T) => R} mapper
   */
  static from(sizeX, sizeY, iterable, mapper = value => value) {
    /** @type {BiArray2D<X, Y, R>} */
    const instance = new BiArray2D(sizeX, sizeY);
    for(const entry of iterable) {
      instance.set(entry[0], entry[1], mapper(entry[2]));
    }
    return instance;
  }

};