# Function: texture()

> **texture**(`query`): [`TextureSelection`](/api/classes/TextureSelection)

When query argument is string, this function parses the query; looks up registered atlases; and returns a texture selection object.

When query argument is an object, the object is used to create a new selection.

## Parameters

• **query**: [`TextureSelectionInput`](/api/type-aliases/TextureSelectionInput)

## Returns

[`TextureSelection`](/api/classes/TextureSelection)
