/**
 * <AUTHOR>
 * @link https://github.com/robashton/camera/
 */
export default class Camera {
  /**
   * @param {CanvasRenderingContext2D} context
   * @param {{ distance?: number; initialPosition?: [x: number, y?: number]; fieldOfView?: number; scaleX?: number; scaleY?: number }} [settings]
   */
  constructor(context, settings = {}) {
    this.distance = settings.distance ?? 1000.0;
    this.lookAt = settings.initialPosition ?? [0, 0];
    this.context = context;
    this.fieldOfView = settings.fieldOfView ?? (Math.PI / 4.0);
    this.viewport = {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      width: 0,
      height: 0,
      scale: [settings.scaleX ?? 1.0, settings.scaleY ?? 1.0]
    };
    this.init();
  }

  /**
   * Camera Initialization
   * -Add listeners.
   * -Initial calculations.
   */
  init() {
    this.addListeners();
    this.updateViewport();
  }

  /**
   * Applies to canvas context the parameters:
   *  -Scale
   *  -Translation
   */
  begin() {
    this.context.save();
    this.applyScale();
    this.applyTranslation();
  }

  /**
   * 2d Context restore() method
   */
  end() {
    this.context.restore();
  }

  /**
   * 2d Context scale(Camera.viewport.scale[0], Camera.viewport.scale[0]) method
   */
  applyScale() {
    this.context.scale(this.viewport.scale[0], this.viewport.scale[1]);
  }

  /**
   * 2d Context translate(-Camera.viewport.left, -Camera.viewport.top) method
   */
  applyTranslation() {
    this.context.translate(-this.viewport.left, -this.viewport.top);
  }

  /**
   * Camera.viewport data update
   */
  updateViewport() {
    this.aspectRatio = this.context.canvas.width / this.context.canvas.height;
    this.viewport.width = this.distance * Math.tan(this.fieldOfView);
    this.viewport.height = this.viewport.width / this.aspectRatio;
    this.viewport.left = this.lookAt[0] - (this.viewport.width / 2.0);
    this.viewport.top = this.lookAt[1] - (this.viewport.height / 2.0);
    this.viewport.right = this.viewport.left + this.viewport.width;
    this.viewport.bottom = this.viewport.top + this.viewport.height;
    this.viewport.scale[0] = this.context.canvas.width / this.viewport.width;
    this.viewport.scale[1] = this.context.canvas.height / this.viewport.height;
  }

  /**
   * Zooms to certain z distance
   * @param {number} z
   */
  zoomTo(z) {
    this.distance = z;
    this.updateViewport();
  }

  /**
   * Moves the centre of the viewport to new x, y coords (updates Camera.lookAt)
   * @param {number} x
   * @param {number} y
   */
  moveTo(x, y) {
    this.lookAt[0] = x;
    this.lookAt[1] = y;
    this.updateViewport();
  }

  /**
   * Transform a coordinate pair from screen coordinates (relative to the canvas) into world coordinates (useful for intersection between mouse and entities)
   * Optional: obj can supply an object to be populated with the x/y (for object-reuse in garbage collection efficient code)
   * @param {number} x
   * @param {number} y
   * @param {{}} obj obj can supply an object to be populated with the x/y
   */
  screenToWorld(x, y, obj) {
    obj = obj ?? {};
    obj.x = (x / this.viewport.scale[0]) + this.viewport.left;
    obj.y = (y / this.viewport.scale[1]) + this.viewport.top;
    return obj;
  }

  /**
   * Transform a coordinate pair from world coordinates into screen coordinates (relative to the canvas) - useful for placing DOM elements over the scene.
   * Optional: obj can supply an object to be populated with the x/y (for object-reuse in garbage collection efficient code).
   * @param {number} x
   * @param {number} y
   * @param {{}} obj obj can supply an object to be populated with the x/y
   */
  worldToScreen(x, y, obj) {
    obj = obj ?? {};
    obj.x = (x - this.viewport.left) * (this.viewport.scale[0]);
    obj.y = (y - this.viewport.top) * (this.viewport.scale[1]);
    return obj;
  }

  /**
   * Event Listeners for:
   *  -Zoom and scroll around world
   *  -Center camera on "R" key
   */
  addListeners() {
    window.onwheel = e => {
      if (e.ctrlKey) {
        // Your zoom/scale factor
        let zoomLevel = this.distance - (e.deltaY * 20);
        if (zoomLevel <= 1) {
          zoomLevel = 1;
        }

        this.zoomTo(zoomLevel);
      } else {
        // Your track-pad X and Y positions
        const x = this.lookAt[0] + (e.deltaX * 2);
        const y = this.lookAt[1] + (e.deltaY * 2);

        this.moveTo(x, y);
      }
    };

    window.addEventListener("keydown", e => {
      if (e.key === "r") {
        this.zoomTo(1000);
        this.moveTo(0, 0);
      }
    });
  }
};