// @ts-check

import { WeakTuple } from "../tuple/tuple.js";

/**
 * @typedef {new (...args: any[]) => any} ConstructorType
 */

/** @typedef {{}[]} AnyType */

/**
 * @template {Template} T
 * @typedef {T extends Template<infer I> ? I[number] : never} TemplateValue
 */

//  * @template {"type" | "value"} P
/**
 * @template {AnyType} [T=AnyType]
 */
export class Template {
  /** @param {T} type */
  constructor(...type) {
    this.types = new Set(type);
  }
  /** @param {AnyType[number]} value */
  canAssign(value) {
    if(this.types.has(value)) {
      return true;
    }
    return false;
  }
  /** @type {Template} */
  static ANY = new Template(String, Number, Boolean, Object, Array, Function, Symbol);
}
/**
 * @template {Template[]} T
 * @template {{ [K in keyof T]: TemplateValue<T[K]> }} A
 * @template {ConstructorType} R
*/
export default class TypeBuilder {
  #from;
  /** @type {Map<WeakTuple<{ [ K in keyof T ]: TemplateValue<T[K]> }>, R>} */
  #map = new Map;
  /**
   * @param {[...T]} templates
   * @param {(...templates: T) => ((...args: A) => R)} fn
   * */
  constructor(templates, fn) {
    this.#from = fn(...templates);
  }

  /**
   * @template {A} TA
   * @param {TA} args
   */
  from(...args) {
    const tuple = new WeakTuple(...args);
    let Type = this.#map.get(tuple);
    if(Type === undefined) {
      Type = this.#from(...args);
      this.#map.set(tuple, Type);
    }
    return Type;
  };

};

const MapTemplate = new TypeBuilder([
  new Template(String, Number, Symbol),
  Template.ANY
], function(_K, _V) {
  return function(key, value) {
    return class Mape {
      constructor() {
        this.map = new Map();
      }
    };
  };
});