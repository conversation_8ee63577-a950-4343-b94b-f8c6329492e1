<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   version="1.1"
   width="256"
   height="256"
   id="svg2">
  <defs
     id="defs4">
    <linearGradient
       id="linearGradient3800">
      <stop
         id="stop3802"
         style="stop-color:#434343;stop-opacity:1"
         offset="0" />
      <stop
         id="stop3804"
         style="stop-color:#1a1a1a;stop-opacity:1"
         offset="1" />
    </linearGradient>
    <radialGradient
       cx="128"
       cy="924.36218"
       r="128"
       fx="128"
       fy="924.36218"
       id="radialGradient3806"
       xlink:href="#linearGradient3800"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(1.1632342,-1.1632342,1.1632342,1.1632342,-1096.1437,-1.9935365)" />
  </defs>
  <metadata
     id="metadata7">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title></dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     transform="translate(0,-796.3622)"
     id="layer1">
    <rect
       width="256"
       height="256"
       x="0"
       y="796.36218"
       id="rect3030"
       style="fill:url(#radialGradient3806);fill-opacity:1;stroke:none" />
  </g>
</svg>
