# Function: testOverlap()

> **testOverlap**(`shapeA`, `indexA`, `shapeB`, `indexB`, `xfA`, `xfB`): `boolean`

Determine if two generic shapes overlap.

## Parameters

• **shapeA**: [`Shape`](/api/classes/Shape)

• **indexA**: `number`

• **shapeB**: [`Shape`](/api/classes/Shape)

• **indexB**: `number`

• **xfA**: [`TransformValue`](/api/type-aliases/TransformValue)

• **xfB**: [`TransformValue`](/api/type-aliases/TransformValue)

## Returns

`boolean`
