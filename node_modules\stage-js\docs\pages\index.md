![Stage.js](https://static.piqnt.com/stage.js/stage.png "Stage.js")

Stage.js is a lightweight and fast 2D rendering and layout library for web and mobile game development.

Stage.js provides an intuitive api to create interactive graphics on HTM5 2D Canvas.

##### Features

- Optimized rendering loop
- Pointer events processing, and dispatching events to target components

- Texture atlas, sprites, and image preloading

- Components tree object model
- Layouts, positioning, and transitions
