import { Vec2 } from "planck";

export default class BaseEntity {
  /**
   * @param {Vec2} position
   * @param {BaseGame} game
   */
  constructor(position = Vec2.zero(), angle = 0) {
    // this.game = game;
    this.position = position;
    this.angle = angle;
  }
  get direction() {
    return new Vec2(Math.cos(this.angle), Math.sin(this.angle));
  }
  set direction(direction) {
    this.angle = Math.atan2(direction.y, direction.x);
  }
  /** @param {number} delta */
  update(delta) {}
  /** @param {Renderer} renderer */
  render(renderer) {}
};

export class BaseRenderer {};

export class BaseGame {

  /** @type {number} */
  #timeoutId;
  #lastFrameTime = 0;
  #tickFrequency = Number.parseInt(1000 / 60);
  #lag = 0;
  #isRunning = false;

  async startLooping() {
    if(this.#isRunning) {
      return;
    }
    this.#isRunning = true;
    this.#lastFrameTime = Date.now();
    this.#loop();
  }

  async stopLooping() {
    clearTimeout(this.#timeoutId);
    this.#isRunning = false;
  }

  #setTimeout() {
    clearTimeout(this.#timeoutId);
    this.#timeoutId = setTimeout(this.#loop.bind(this), this.#tickFrequency);
  }

  #loop() {
    const currentTime = Date.now();
    const elapsedTime = currentTime - this.#lastFrameTime;
    this.#lastFrameTime = currentTime;
    this.#lag += elapsedTime;
    // let updateIterations = Math.ceil(elapsedTime / this.#tickFrequency);
    while(this.#lag >= this.#tickFrequency) {
      const delta = this.#tickFrequency / 1000;
      this.update(delta);
      this.#lag -= this.#tickFrequency;
    }
    this.render();
    this.#setTimeout();
  }

  update(delta) {}
  render() {}
};