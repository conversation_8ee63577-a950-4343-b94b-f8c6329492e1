// @ts-check

import PhysicsWorld from "./physics-world.js";
import Renderer from "../../renderer.js";
import { BoxShape, CircleShape, EdgeShape, Vec2 } from "planck";
import { RectangleShape } from "../../../library/math/shape.js";

export default class RenderablePhysicsWorld extends PhysicsWorld {
  #renderer;

  /** @param {Renderer} renderer */
  constructor(renderer) {
    super();
    this.#renderer = renderer;
  }

  /** @param {Renderer} renderer */
  render(renderer) {
    // return false;
    for(const body of this.bodies()) {
      const userData = body.getUserData() ?? {};
      if(!body.isAwake()) {
        // continue;
      }

      const position = body.getPosition();

      if(userData?.type === "dead-body") {
        renderer.imageSourceDestination(userData.image, userData.source, userData.destination);
      }

      fixtureLoop: for(const fixture of PhysicsWorld.fixturesOf(body)) {
        const shape = fixture.getShape();
        // if(userData.type === "player") {
        //   console.log(shape.constructor)
        // }
        if(shape instanceof EdgeShape) {
          const v1 = Vec2.add(shape.m_vertex1, position);
          const v2 = Vec2.add(shape.m_vertex2, position);
          this.#renderer.line(v1, v2, 0.1, "#fff");
          continue fixtureLoop;
        }

        if(shape instanceof CircleShape) {
          const center = Vec2.add(shape.m_p, position);
          renderer.circle(center, shape.m_radius, "#0000", 0.1, "#fff");
          continue fixtureLoop;
        }


        if(shape instanceof BoxShape) {
          const center = Vec2.add(shape.m_centroid, position);
          this.#renderer.rectangle(shape.m_vertices[3], shape.m_vertices[1], 0, "#0000", 0.1, "#fff");
          // this.#renderer.circleLines(center, shape.m_radius * 100, 0.1, "#fff");
          continue fixtureLoop;
        }

      }
    }

    // console.log(Array.from(this.fixturesMap).map(fixture => fixture.getShape().m_type).join(", "))
    for(const fixture of this.fixturesMap) {

      /** @type {{ texture: { image: HTMLImageElement; destination: RectangleShape } }} */
      const userData = fixture.getBody().getUserData();
      const position = fixture.getBody().getPosition();
      const shape = fixture.getShape();

      if(shape instanceof CircleShape) {
        // const center = Vec2.add(shape.m_p, position);
        // this.#renderer.circle(center, shape.m_radius, userData?.color ?? "#fff");
        if("spritesheet" in userData && userData.spritesheet !== null) {
          userData.spritesheet.render();
        }
        continue;
      }

      if(shape instanceof BoxShape) {
        // this.#renderer.rectangle(shape.m_vertices[1], shape.m_vertices[3], "#fff2");
        if(userData.texture?.image.complete) {
          this.#renderer.imageDestination(userData.texture.image, userData.texture.destination);
        }
        // const center = Vec2.add(shape.m_p, position);
        // this.#renderer.circleLines(center, shape.m_radius, 1, "#fff");
        continue;
      }

    }

  }

};