/**
 * @template {string | number | boolean | bigint} K
 * @template V
 * @extends {Map<K, Set<V>>}
*/
export default class OrderedMap extends Map {

  /** @type {K[]} */
  #order = [];

  /**
   * @param {K} key
   * @param {V} value
  */
  set(key, value) {
    if(!this.has(key)) {
      super.set(key, new Set);
      this.#addKey(key);
    }
    super.get(key).add(value);
  }

  /**
   * @param {K} key
   * @param {V} value
  */
  delete(key, value) {
    const set = this.get(key);
    if(set === undefined) {
      return false;
    }
    if(!set.delete(value)) {
      return false;
    }
    if(set.size === 0) {
      super.delete(key);
      this.#removeKey(key);
    }
    return true;
  }

  // /**
  //  * @param {K} key
  //  * @param {V} value
  // */
  // move(key, value) {
  //   if(this.deleteValue(key, value)) {
  //     this.set(key, value);
  //   }
  // }

  /** @param {K} key */
  #removeKey(key) {
    const index = this.#order.indexOf(key);
    if(index === -1) {
      return false;
    }
    this.#order.splice(index, 1);
    return true;
  }

  /** @param {K} key */
  #addKey(key) {
    for(const entry of this.#order.entries()) {
      if(entry[1] === key) {
        return;
      }
      if(entry[1] > key) {
        this.#order.splice(entry[0], 0, key);
        return;
      }
    }
    this.#order.push(key);
  }

  *[Symbol.iterator]() {
    for(const key of this.#order) {
      for(const value of this.get(key)) {
        yield value;
      }
    }
  }

  *entries() {
    for(const key of this.#order) {
      for(const value of this.get(key)) {
        yield /** @type {const} */ ([ key, value ]);
      }
    }
  }

};