{"compilerOptions": {"module": "ES2015", "strictNullChecks": false, "noImplicitAny": false, "lib": ["ES2015", "DOM"], "stripInternal": true, "target": "ES5", "esModuleInterop": true, "moduleResolution": "Node", "resolveJsonModule": true, "allowJs": true, "typeRoots": ["./types"], "skipLibCheck": true, "noEmit": true}, "exclude": ["node_modules", "example", "benchmark", "docs", "vite.config.*", "test-d/", "dist"]}