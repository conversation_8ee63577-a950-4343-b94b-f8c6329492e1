# Interface: MassData

MassData This holds the mass data computed for a shape.

## Properties

### center

> **center**: [`Vec2Value`](/api/interfaces/Vec2Value)

The position of the shape's centroid relative to the shape's origin.

***

### I

> **I**: `number`

The rotational inertia of the shape about the local origin.

***

### mass

> **mass**: `number`

The mass of the shape, usually in kilograms.
