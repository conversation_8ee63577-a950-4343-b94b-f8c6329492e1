// @ts-check

import { SharedPrimitive } from "../tuple/tuple.js";
import Vector2Template from "./vector.js";

const Vector2 = Vector2Template.from(Float32Array, SharedPrimitive.create(0));
/** @typedef {InstanceType<typeof Vector2>} Vector2 */

export class Shape {
  /**
   * @param {number} x
   * @param {number} y
   */
  constructor(x = 0, y = 0) {
    this.position = new Vector2(x, y);
  }
  get x() {
    return this.x;
  }
  get y() {
    return this.y;
  }
};

export class RectangleShape extends Shape {

  /**
   * @param {number} width
   * @param {number} height
   * @param {number} x
   * @param {number} y
   */
  constructor(width, height, x, y) {
    super(x, y);
    this.width = width;
    this.height = height;
  }

  get x() {
    return this.position.x;
  }
  get y() {
    return this.position.y;
  }

  get dimensionRatio() {
    return this.width / this.height;
  }

  /**
   * @param {number} [x]
   * @param {number} [y]
  */
  copy(x, y) {
    return new RectangleShape(this.width, this.height, x ?? this.position.x, y ?? this.position.y);
  }

  /** @param {{ x: number; y: number; width: number; height: number }} rectange */
  static from(rectange) {
    return new RectangleShape(rectange.width, rectange.height, rectange.x, rectange.y);
  }

  toJSON() {
    return {
      x: this.position.x,
      y: this.position.y,
      width: this.width,
      height: this.height
    };
  }

};

export class CircleShape extends Shape {
  /**
   * @param {number} radius
   * @param {number} x
   * @param {number} y
   */
  constructor(radius, x, y) {
    super(x, y);
    this.radius = radius;
  }
};

export class EllipseShape extends Shape {
  /**
   * @param {number} radiusX
   * @param {number} radiusY
   * @param {number} x
   * @param {number} y
   */
  constructor(radiusX, radiusY, x, y) {
    super(x, y);
    this.radius = new Vector2(radiusX, radiusY);
  }
};

export class Line {
  /**
   * @param {number} x1
   * @param {number} y1
   * @param {number} x2
   * @param {number} y2
   */
  constructor(x1, y1, x2, y2) {
    this.points = Object.freeze(
      /** @type {const} */ ([ new Vector2(x1, y1), new Vector2(x2, y2) ])
    );
  }
};

export class Polygon {

  /** @type {readonly Readonly<Vector2>[]} */
  #absoluteVertices;

  /**
   * @param {Iterable<Vector2>} vertices
   * @param {Vector2} position
   */
  constructor(position, vertices, origin = Vector2.zero(), angle = 0) {
    this.position = position;
    this.transformOrigin = origin;
    this.angle = angle;
    this.vertices = [];
    for(const vertex of vertices) {
      this.vertices.push(vertex);
    }
    this.processAbsoluteVertices();
  }

  get absoluteVertices() {
    return this.#absoluteVertices;
  }

  /** @param {Vector2} translate */
  processAbsoluteVertices(translate = Vector2.zero()) {
    const absoluteVertices = [];
    const shapeCenter = Vector2.add(this.position, translate);
    for(const vertex of this.vertices) {
      const absoluteVertex = Vector2.add(shapeCenter, vertex).add(this.transformOrigin);
      const rotatedVertex = Vector2.rotate(absoluteVertex, Vector2.add(shapeCenter, this.transformOrigin), this.angle);
      absoluteVertices.push(Object.freeze(rotatedVertex));
    }
    this.#absoluteVertices = absoluteVertices;
    return Object.freeze(this.#absoluteVertices);
  }

};

export class RegularPolygon extends Polygon {

  /**
   * @param {Vector2} position
   * @param {number} sides
   * @param {number} radius
   * @param {number} angle
   */
  constructor(position, sides, radius, origin = Vector2.zero(), angle = 0) {
    super(position, [], origin, angle);
    this.sides = sides;
    this.radius = radius;
    this.processVertices();
    this.processAbsoluteVertices();
  }

  processVertices() {
    this.vertices = new Array(this.sides);
    const unitAngle = Math.PI * 2 / this.sides;
    for(let i = 0; i < this.sides; i++) {
      const angleShift = unitAngle * i;
      const vertex = new Vector2(Math.cos(angleShift) * this.radius, Math.sin(angleShift) * this.radius);
      this.vertices[i] = Object.freeze(vertex);
    }
    return Object.freeze(this.vertices);
  }

  /**
   * Note: `RegularPolygon.processVertices` needs to be invoked just before this method to get the updated vertices.
   * @param {Vector2} translate
   */
  processAbsoluteVertices(translate = Vector2.zero()) {
    return super.processAbsoluteVertices(translate);
  }

  /**
   * @param {Vector2} center
   * @param {number} radius
   */
  pointCollision(center, radius) {
    const verticesCount = this.vertices.length;
    for(let v = 0; v < verticesCount; v++) {
      const vertexA = this.absoluteVertices[v];
      const vertexB = this.absoluteVertices[(v + 1) % this.sides];
      const netArea = Triangle.pointCollision(vertexA, vertexB, this.position, center);
      // console.log(netArea)
      if(netArea >= -0.001) {
        return true;
      }
    }
    return false;
  }

  /**
   * @param {{ line: (v1: Vector2, v2: Vector2, lineWidth: number, color: string) => void; borderWidth: number }} renderer
   * @param {Vector2} center
   */
  debug(renderer, center) {
    const verticesCount = this.vertices.length;
    for(let v = 0; v < verticesCount; v++) {
      const vertexA = this.absoluteVertices[v];
      const vertexB = this.absoluteVertices[(v + 1) % this.sides];
      let netArea = Triangle.pointCollision(vertexA, vertexB, this.position, center);
      renderer.line(vertexA, vertexB, renderer.borderWidth, "skyblue");
      renderer.line(vertexA, this.position, renderer.borderWidth, "skyblue");
      renderer.line(vertexB, this.position, renderer.borderWidth, "skyblue");
      // console.log(netArea)
      // netArea = Math.round(netArea);
      if(netArea >= -0.001) {
        return console.log("colliding");
      }
    }
    console.log("not colliding")
  }

};

class Triangle {
  /**
   * @param {Vector2} v1
   * @param {Vector2} v2
   * @param {Vector2} v3
   * @param {Vector2} pointPosition
   * @param {number} circleRadius
   */
  static pointCollision(v1, v2, v3, pointPosition, circleRadius = 0) {
    const side_1_2 = Vector2.subtract(v1, v2).mag();
    const side_2_3 = Vector2.subtract(v2, v3).mag();
    const side_3_1 = Vector2.subtract(v3, v1).mag();
    const distance_1 = Vector2.subtract(v1, pointPosition).mag();
    const distance_2 = Vector2.subtract(v2, pointPosition).mag();
    const distance_3 = Vector2.subtract(v3, pointPosition).mag();
    const area_1_2_c = this.heronArea(distance_1, distance_2, side_1_2);
    const area_2_3_c = this.heronArea(distance_2, distance_3, side_2_3);
    const area_3_1_c = this.heronArea(distance_3, distance_1, side_3_1);
    const area_1_2_3 = this.heronArea(side_1_2, side_2_3, side_3_1);
    return area_1_2_3 - (area_1_2_c + area_2_3_c + area_3_1_c);
  }

  /**
   * @param {number} a
   * @param {number} b
   * @param {number} c
   */
  static heronArea(a, b, c) {
    const s = (a + b + c) / 2;
    return Math.sqrt(s * (s - a) * (s - b) * (s - c));
  }
};

// chainmail