// @ts-check

import { Body, Fixture, Vec2, World } from "planck";
import OrderedMap from "../../../library/util/sorted-key-map.js";

export default class PhysicsWorld extends World {

  /** @type {OrderedMap<number, Fixture>} */
  fixturesMap = new OrderedMap;

  meterPixelRatio = 1 / 10;

  /** @type {Set<Body>} */
  #nonPlayerBodies = new Set;

  constructor() {
    super({
      gravity: new Vec2(0, 0)
    });
  }

  // getBodyList() {
  //   return /** @type {Body & { userData: { type: string }; getNext: () => Body & { userData: { type: string } } | null} */ (super.getBodyList());
  // }

  *bodies() {
    let body = this.getBodyList();
    while(body !== null) {
      yield body;
      body = body.getNext();
    }
  }

  /** @param {Body} body */
 static *fixturesOf(body) {
    let fixture = body.getFixtureList();
    while(fixture !== null) {
      yield fixture;
      fixture = fixture.getNext();
    }
  }

  update(delta) {
    this.step(1 / 60);
  }

  /** @param {Body} body */
  destroyNonPlayerBody(body) {
    if(!this.#nonPlayerBodies.delete(body)) {
      return false;
    }
    return this.destroyBody(body);
  }

  destroyNonPlayerBodies() {
    for(const body of this.#nonPlayerBodies) {
      this.destroyBody(body);
    }
    this.#nonPlayerBodies.clear()
  }

  /** @param {Fixture} fixture */
  static fixtureUserData(fixture) {
    return fixture.getUserData() ?? null;
  }


};