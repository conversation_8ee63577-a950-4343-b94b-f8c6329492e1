/** @type {<T, F extends (...args: any[]) => any>(fn: F, thisArg: T, args: F extends (...args: infer A) => any ? A : never) => F extends (...args: any[]) => infer R ? R : never} */
export function fnApply(fn, thisArg, args) {
  return fn.apply(thisArg, args);
}

/** @type {<T, F extends (...args: any[]) => any, A extends any[]>(fn: F, thisArg: T, ...args: A) => F extends (...args: infer P) => infer R ? (this: T, ...args: P extends A ? never[] : P ) => R : F} */
export function fnBind(fn, thisArg, ...args) {
  return fn.bind(thisArg, ...args);
}

/** @type {<T extends Iterable<readonly [PropertyKey, any]>>(iterable: T) => T extends (readonly [infer K, infer V])[] ? { [k in K]: V } : never} */
export function fromEntries(iterable) {
  return Object.fromEntries(iterable);
}