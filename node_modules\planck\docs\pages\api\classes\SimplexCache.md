# Class: SimplexCache

Used to warm start Distance. Set count to zero on first call.

## Constructors

### new SimplexCache()

> **new SimplexCache**(): [`SimplexCache`](/api/classes/SimplexCache)

#### Returns

[`SimplexCache`](/api/classes/SimplexCache)

## Properties

### count

> **count**: `number` = `0`

***

### indexA

> **indexA**: `number`[] = `[]`

vertices on shape A

***

### indexB

> **indexB**: `number`[] = `[]`

vertices on shape B

***

### metric

> **metric**: `number` = `0`

length or area

## Methods

### recycle()

> **recycle**(): `void`

#### Returns

`void`
