# Class: DistanceOutput

Output for Distance.

## Constructors

### new DistanceOutput()

> **new DistanceOutput**(): [`DistanceOutput`](/api/classes/DistanceOutput)

#### Returns

[`DistanceOutput`](/api/classes/DistanceOutput)

## Properties

### distance

> **distance**: `number` = `0`

***

### iterations

> **iterations**: `number` = `0`

iterations number of GJK iterations used

***

### pointA

> **pointA**: [`Vec2Value`](/api/interfaces/Vec2Value)

closest point on shapeA

***

### pointB

> **pointB**: [`Vec2Value`](/api/interfaces/Vec2Value)

closest point on shapeB

## Methods

### recycle()

> **recycle**(): `void`

#### Returns

`void`
