# Class: VelocityConstraintPoint

## Constructors

### new VelocityConstraintPoint()

> **new VelocityConstraintPoint**(): [`VelocityConstraintPoint`](/api/classes/VelocityConstraintPoint)

#### Returns

[`VelocityConstraintPoint`](/api/classes/VelocityConstraintPoint)

## Properties

### normalImpulse

> **normalImpulse**: `number` = `0`

***

### normalMass

> **normalMass**: `number` = `0`

***

### rA

> **rA**: [`Vec2Value`](/api/interfaces/Vec2Value)

***

### rB

> **rB**: [`Vec2Value`](/api/interfaces/Vec2Value)

***

### tangentImpulse

> **tangentImpulse**: `number` = `0`

***

### tangentMass

> **tangentMass**: `number` = `0`

***

### velocityBias

> **velocityBias**: `number` = `0`

## Methods

### recycle()

> **recycle**(): `void`

#### Returns

`void`
