// @ts-check

import Color from "../../library/color/color.js";
import BaseEntity from "./base-entity.js";
import Renderer from "../renderer.js";
import Vector2Template from "../../library/math/vector.js";
import { SharedPrimitive } from "../../library/tuple/tuple.js";

const Vector2 = Vector2Template.from(Float32Array, SharedPrimitive.create(0));
/** @typedef {InstanceType<typeof Vector2>} Vector2 */

export class Bot extends BaseEntity {
  radius = 4;
  velocity = Vector2.zero();
  acceleration = Vector2.zero();
  #direction = super.direction;

  speed = 60;
  rotationalSpeed = 2;
  thrust = 0;

  constructor() {
    super(undefined, -Math.PI / 2);
  }

  get direction() {
    return this.#direction;
  }

  /** @param {number} angle */
  rotate(angle) {
    this.angle += angle * this.rotationalSpeed;
    this.#direction = super.direction;
  }

  update(delta) {
    const netForce = Vector2.zero();
    // console.log(theta * 180 / Math.PI, Math.cos(theta));
    // console.log((Math.atan2(diff.y, diff.x) + Math.PI) * 180 / Math.PI);
    // console.log(thruster.angle * 180 / Math.PI);
    // this.angle = 
    this.acceleration.setFrom(netForce);
    this.velocity.add(this.acceleration.scale(delta));
    this.velocity.scale(delta * this.speed);
    // this.velocity.clamp(10 / this.speed);
    this.position.add(this.velocity);
    this.acceleration.set(0, 0);
  }

  /** @param {Renderer} renderer */
  render(renderer) {
    renderer.regularPolygon(this.position, this.radius, 3, this.angle, Vector2.zero(), Color.dark("44"), 2, "#aaa");
  }

};