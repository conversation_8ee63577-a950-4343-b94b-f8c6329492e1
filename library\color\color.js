export default class Color {
  static transparent = "transparent";
  static light(alpha = "ff") {
    return "#ffffff" + alpha;
  }
  static dark(alpha = "ff") {
    return "#000000" + alpha;
  }
  static opaque(color, alpha = "ff") {
    return color + alpha;
  }
  static *palette(colorsCount = 4, saturation = 0.5, lightness = 0.5) {
    const angleFraction = Math.floor(360 / colorsCount);
    for(let angle = 0; angle < 360; angle += angleFraction) {
      yield `hsl(${ angle }, ${ saturation * 100 }%, ${ lightness * 100 }%)`;
    }
  }
}