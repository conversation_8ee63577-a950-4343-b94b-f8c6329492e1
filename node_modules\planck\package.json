{"name": "planck", "version": "1.3.0", "description": "2D JavaScript/TypeScript physics engine for cross-platform HTML5 game development", "homepage": "https://github.com/piqnt/planck.js", "keywords": ["box2d", "html5", "javascript", "typescript", "game", "physics", "engine", "2d", "mobile"], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/piqnt/planck.js.git"}, "author": "<PERSON>", "contributors": ["<PERSON>", "<PERSON>", "<PERSON>"], "type": "commonjs", "sideEffects": ["./dist/planck-with-testbed.*"], "module": "dist/planck.mjs", "main": "dist/planck.js", "jsdelivr": "dist/planck.min.js", "unpkg": "dist/planck.min.js", "types": "index.d.ts", "exports": {".": {"types": "./dist/planck.d.ts", "import": "./dist/planck.mjs", "default": "./dist/planck.js"}, "./with-testbed": {"types": "./dist/planck-with-testbed.d.ts", "import": "./dist/planck-with-testbed.mjs", "default": "./dist/planck-with-testbed.js"}, "./dist/*": "./dist/*"}, "engines": {"node": ">=14.0"}, "peerDependencies": {"stage-js": "^1.0.0-alpha.12"}, "devDependencies": {"@changesets/cli": "^2.27.11", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.17.0", "@types/chai": "^4.3.5", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "@vitest/coverage-v8": "^2.1.5", "ajv": "^6.10.2", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "prettier": "^3.4.2", "replace-in-files-cli": "^3.0.0", "rimraf": "^6.0.1", "rollup-plugin-license": "^3.5.3", "sinon": "^15.2.0", "terser": "^5.36.0", "tsd": "^0.31.2", "typedoc": "^0.26.11", "typedoc-plugin-markdown": "^4.2.10", "vite": "^5.4.11", "vite-plugin-dts-bundle-generator": "^2.0.5", "vite-plugin-typescript": "^1.0.4", "vite-plugin-typescript-transform": "^1.3.1", "vitest": "^2.1.5"}, "scripts": {"lint": "eslint './src/**/*.ts' './testbed/**/*.ts'", "pretty": "prettier --write './src/**/*.ts' './testbed/**/*.ts'", "lint:example": "eslint --fix './example/**/*.ts'", "pretty:example": "prettier --write './example/**/*.ts'", "test": "vitest run", "test:coverage": "vitest run --coverage", "test:types": "tsd --typings=./dist/planck.d.ts && tsd --typings=./dist/planck-with-testbed.d.ts", "preflight": "npm run lint && npm run test:types && npm run test", "testbed": "npm run dev", "dev": "vite", "build": "rimraf ./dist/* && vite build && BUILD_TESTBED=true vite build && npm run terser-core && npm run terser-testbed", "terser-core": "terser ./dist/planck.js -o ./dist/planck.min.js --source-map", "terser-testbed": "terser ./dist/planck-with-testbed.js -o ./dist/planck-with-testbed.min.js --source-map", "watch": "vite build --watch", "preview": "vite preview", "benchmark": "git log -n 1 && vite-node ./benchmark/node", "change": "changeset", "version": "changeset version", "publish": "changeset publish", "typedoc": "typedoc --options typedoc.json && mv ./docs/pages/api/README.md ./docs/pages/api/index.md && ./node_modules/.bin/replace-in-files ./docs/pages/api/* --string '.md' --replacement ''"}}