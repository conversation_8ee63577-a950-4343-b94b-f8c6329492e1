# Class: ShapeCastOutput

Output results for b2ShapeCast

## Constructors

### new ShapeCastOutput()

> **new ShapeCastOutput**(): [`ShapeCastOutput`](/api/classes/ShapeCastOutput)

#### Returns

[`ShapeCastOutput`](/api/classes/ShapeCastOutput)

## Properties

### iterations

> **iterations**: `number` = `0`

***

### lambda

> **lambda**: `number` = `1.0`

***

### normal

> **normal**: [`Vec2`](/api/classes/Vec2)

***

### point

> **point**: [`Vec2`](/api/classes/Vec2)
