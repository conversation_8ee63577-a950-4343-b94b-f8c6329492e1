# Class: DistanceInput

Input for Distance. You have to option to use the shape radii in the
computation. Even

## Constructors

### new DistanceInput()

> **new DistanceInput**(): [`DistanceInput`](/api/classes/DistanceInput)

#### Returns

[`DistanceInput`](/api/classes/DistanceInput)

## Properties

### proxyA

> `readonly` **proxyA**: [`DistanceProxy`](/api/classes/DistanceProxy)

***

### proxyB

> `readonly` **proxyB**: [`DistanceProxy`](/api/classes/DistanceProxy)

***

### transformA

> `readonly` **transformA**: [`Transform`](/api/classes/Transform)

***

### transformB

> `readonly` **transformB**: [`Transform`](/api/classes/Transform)

***

### useRadii

> **useRadii**: `boolean` = `false`

## Methods

### recycle()

> **recycle**(): `void`

#### Returns

`void`
