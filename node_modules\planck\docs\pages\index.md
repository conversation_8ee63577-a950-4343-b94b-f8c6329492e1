
### Planck.js

Planck.js is JavaScript/TypeScript rewrite of the [Box2D](https://box2d.org/) C++ physics engine for cross-platform game development.

### Box2D

Box2D is a 2D rigid-body physics simulation library for games. You can use it in your games to make objects move in realistic ways and make the game world more interactive.
From the game engine's point of view, a physics engine is just a system for procedural animation.

Planck.js documentation is based on the Box2D manual with adjustments and additions for JavaScript. Both projects' names are used interchangeably in the documentation.

### Before You Start

Planck.js is a physics engine, so to use the library you need to be familiar with basic physics concepts, such as mass, force, torque, and impulses. If not, you can ask ChatGPT to explain them.

Since Planck.js is written in JavaScript, you need to be familiar with JavaScript programming.
