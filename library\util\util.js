// @ts-check

/**
 * @template T
 * @param {T} object
 * @param {(this: T, object: T) => any} scope
*/
export function using(object, scope) {
  if (object !== null && object instanceof Object) {
    return scope.call(object, object);
  }
}

/**
 * Wait for an event to be dispatched on a target.
 * @template {EventTarget} T
 * @template {Parameters<T["addEventListener"]>[0]} K
 * @param {T} target
 * @param {K} type
 * @param {{ signal?: AbortSignal }} options
*/
export async function untilEvent(target, type, options = {}) {
  return new Promise(function (resolve) {

    options?.signal?.addEventListener("abort", handleAbortion);
    target.addEventListener(type, handleEvent, { once: true });

    function handleAbortion() {
      target.removeEventListener(type, handleEvent);
      resolve(null);
    }

    /** @param {Event} event */
    function handleEvent(event) {
      options?.signal?.removeEventListener("abort", handleAbortion);
      resolve(event);
    }

  });
}

/**
 * @template {Record<string, (value?: string) => any>} P
 * @template {{ [ name: string ]: any }} T
 * @typedef {unknown[] & { scope: object; exposed: T; params: { [K in keyof P]: ReturnType<P[K]> } }} MainArgsType
*/
/**
 * @template {{ [ name: string ]: any }} T
 * @typedef {{ expose(value: Partial<T>): void }} MainActionsType
*/
/**
 * @exports
 * @template {Record<string, (value?: string) => any>} P
 * @template {{ [ name: string ]: any }} T
 * @typedef {{ args: MainArgsType<P, T>; actions: MainActionsType<T> }} Main */

/**
 * @template {Record<string, (value?: string) => any>} P
 * @template {{ [ name: string ]: any }} T
 * @param {P} expectedParams
 * @returns {Promise<Main<P, T>>}
*/
export async function main(expectedParams = /** @type {P} */ ({})) {
  if (document.readyState !== "complete") {
    await untilEvent(document, "DOMContentLoaded");
  }
  const params = /** @type {{ [K in keyof P]: ReturnType<P[K]> }} */ (Object.fromEntries(
    Array.from(new URLSearchParams(location.search).entries()).map(entry => {
      return ([ entry[0], expectedParams[entry[0]](entry[1]) ]);
    })
  ));
  const args = Object.assign([], {
    scope: window,
    exposed: /** @type {T} */ ({}),
    params
  });

  return {
    args,
    actions: {
      expose(value) {
        Object.assign(window, value);
        Object.assign(args.exposed, value);
      }
    }
  };
}