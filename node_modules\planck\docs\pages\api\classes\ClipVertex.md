# Class: ClipVertex

Used for computing contact manifolds.

## Constructors

### new ClipVertex()

> **new ClipVertex**(): [`ClipVertex`](/api/classes/ClipVertex)

#### Returns

[`ClipVertex`](/api/classes/ClipVertex)

## Properties

### id

> **id**: [`ContactID`](/api/classes/ContactID)

***

### v

> **v**: [`Vec2Value`](/api/interfaces/Vec2Value)

## Methods

### recycle()

> **recycle**(): `void`

#### Returns

`void`

***

### set()

> **set**(`o`): `void`

#### Parameters

• **o**: [`ClipVertex`](/api/classes/ClipVertex)

#### Returns

`void`
