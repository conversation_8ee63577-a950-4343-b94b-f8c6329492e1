
#### Credits

Box2D is a popular C++ 2D rigid-body physics engine created by <PERSON>. Box2D is used in several popular games, such as Angry Birds, Limbo and Crayon Physics, as well as game development tools and libraries such as Apple's SpriteKit.

Planck.js is developed and maintained by <PERSON>.

TypeScript definitions for planck.js are developed by <PERSON>.

#### License

Box2D and Planck.js are released under the MIT license. You can use them for all purposes, including commercial applications for no fee.