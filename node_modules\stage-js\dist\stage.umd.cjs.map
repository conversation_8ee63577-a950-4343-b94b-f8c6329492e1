{"version": 3, "file": "stage.umd.cjs", "sources": ["../src/common/math.ts", "../src/common/matrix.ts", "../node_modules/tslib/tslib.es6.js", "../src/common/is.ts", "../src/common/stats.ts", "../src/common/uid.ts", "../src/texture/texture.ts", "../src/texture/image.ts", "../src/texture/pipe.ts", "../src/texture/atlas.ts", "../src/texture/selection.ts", "../src/texture/resizable.ts", "../src/common/browser.ts", "../src/core/pin.ts", "../src/core/easing.ts", "../src/core/transition.ts", "../src/core/component.ts", "../src/core/sprite.ts", "../src/texture/canvas.ts", "../src/core/pointer.ts", "../src/core/root.ts", "../src/core/anim.ts", "../src/core/monotype.ts"], "sourcesContent": ["/** @internal */ const math_random = Math.random;\n/** @internal */ const math_sqrt = Math.sqrt;\n\n/** @internal */\nexport function random(min?: number, max?: number): number {\n  if (typeof min === \"undefined\") {\n    max = 1;\n    min = 0;\n  } else if (typeof max === \"undefined\") {\n    max = min;\n    min = 0;\n  }\n  return min == max ? min : math_random() * (max - min) + min;\n}\n\n/** @internal */\nexport function wrap(num: number, min?: number, max?: number): number {\n  if (typeof min === \"undefined\") {\n    max = 1;\n    min = 0;\n  } else if (typeof max === \"undefined\") {\n    max = min;\n    min = 0;\n  }\n  if (max > min) {\n    num = (num - min) % (max - min);\n    return num + (num < 0 ? max : min);\n  } else {\n    num = (num - max) % (min - max);\n    return num + (num <= 0 ? min : max);\n  }\n}\n\n/** @internal */\nexport function clamp(num: number, min: number, max: number): number {\n  if (num < min) {\n    return min;\n  } else if (num > max) {\n    return max;\n  } else {\n    return num;\n  }\n}\n\n/** @internal */\nexport function length(x: number, y: number): number {\n  return math_sqrt(x * x + y * y);\n}\n\nexport const math = Object.create(Math);\n\nmath.random = random;\nmath.wrap = wrap;\nmath.clamp = clamp;\nmath.length = length;\n\n/** @hidden @deprecated @internal */\nmath.rotate = wrap;\n/** @hidden @deprecated @internal */\nmath.limit = clamp;\n", "export interface MatrixValue {\n  a: number;\n  b: number;\n  c: number;\n  d: number;\n  e: number;\n  f: number;\n}\n\nexport interface Vec2Value {\n  x: number;\n  y: number;\n}\n\nexport class Matrix {\n  /** x-scale */\n  a = 1;\n  b = 0;\n  c = 0;\n  /** y-scale */\n  d = 1;\n  /** x-translate */\n  e = 0;\n  /** y-translate */\n  f = 0;\n\n  /** @internal */ private _dirty: boolean;\n  /** @internal */ private inverted?: Matrix;\n\n  constructor(a: number, b: number, c: number, d: number, e: number, f: number);\n  constructor(m: MatrixValue);\n  constructor();\n  constructor(\n    a?: number | MatrixValue,\n    b?: number,\n    c?: number,\n    d?: number,\n    e?: number,\n    f?: number,\n  ) {\n    if (typeof a === \"object\") {\n      this.reset(a);\n    } else {\n      this.reset(a, b, c, d, e, f);\n    }\n  }\n\n  toString() {\n    return (\n      \"[\" +\n      this.a +\n      \", \" +\n      this.b +\n      \", \" +\n      this.c +\n      \", \" +\n      this.d +\n      \", \" +\n      this.e +\n      \", \" +\n      this.f +\n      \"]\"\n    );\n  }\n\n  clone() {\n    return new Matrix(this.a, this.b, this.c, this.d, this.e, this.f);\n  }\n\n  reset(a: number, b: number, c: number, d: number, e: number, f: number): this;\n  reset(m: MatrixValue): this;\n  reset(\n    a?: number | MatrixValue,\n    b?: number,\n    c?: number,\n    d?: number,\n    e?: number,\n    f?: number,\n  ): this {\n    this._dirty = true;\n    if (typeof a === \"object\") {\n      this.a = a.a;\n      this.d = a.d;\n      this.b = a.b;\n      this.c = a.c;\n      this.e = a.e;\n      this.f = a.f;\n    } else {\n      this.a = typeof a === \"number\" ? a : 1;\n      this.b = typeof b === \"number\" ? b : 0;\n      this.c = typeof c === \"number\" ? c : 0;\n      this.d = typeof d === \"number\" ? d : 1;\n      this.e = typeof e === \"number\" ? e : 0;\n      this.f = typeof f === \"number\" ? f : 0;\n    }\n    return this;\n  }\n\n  identity() {\n    this._dirty = true;\n    this.a = 1;\n    this.b = 0;\n    this.c = 0;\n    this.d = 1;\n    this.e = 0;\n    this.f = 0;\n    return this;\n  }\n\n  rotate(angle: number) {\n    if (!angle) {\n      return this;\n    }\n\n    this._dirty = true;\n\n    const u = angle ? Math.cos(angle) : 1;\n    // android bug may give bad 0 values\n    const v = angle ? Math.sin(angle) : 0;\n\n    const a = u * this.a - v * this.b;\n    const b = u * this.b + v * this.a;\n    const c = u * this.c - v * this.d;\n    const d = u * this.d + v * this.c;\n    const e = u * this.e - v * this.f;\n    const f = u * this.f + v * this.e;\n\n    this.a = a;\n    this.b = b;\n    this.c = c;\n    this.d = d;\n    this.e = e;\n    this.f = f;\n\n    return this;\n  }\n\n  translate(x: number, y: number) {\n    if (!x && !y) {\n      return this;\n    }\n    this._dirty = true;\n    this.e += x;\n    this.f += y;\n    return this;\n  }\n\n  scale(x: number, y: number) {\n    if (!(x - 1) && !(y - 1)) {\n      return this;\n    }\n    this._dirty = true;\n    this.a *= x;\n    this.b *= y;\n    this.c *= x;\n    this.d *= y;\n    this.e *= x;\n    this.f *= y;\n    return this;\n  }\n\n  skew(x: number, y: number) {\n    if (!x && !y) {\n      return this;\n    }\n    this._dirty = true;\n\n    const a = this.a + this.b * x;\n    const b = this.b + this.a * y;\n    const c = this.c + this.d * x;\n    const d = this.d + this.c * y;\n    const e = this.e + this.f * x;\n    const f = this.f + this.e * y;\n\n    this.a = a;\n    this.b = b;\n    this.c = c;\n    this.d = d;\n    this.e = e;\n    this.f = f;\n    return this;\n  }\n\n  concat(m: MatrixValue) {\n    this._dirty = true;\n\n    const a = this.a * m.a + this.b * m.c;\n    const b = this.b * m.d + this.a * m.b;\n    const c = this.c * m.a + this.d * m.c;\n    const d = this.d * m.d + this.c * m.b;\n    const e = this.e * m.a + m.e + this.f * m.c;\n    const f = this.f * m.d + m.f + this.e * m.b;\n\n    this.a = a;\n    this.b = b;\n    this.c = c;\n    this.d = d;\n    this.e = e;\n    this.f = f;\n\n    return this;\n  }\n\n  inverse() {\n    if (this._dirty) {\n      this._dirty = false;\n      if (!this.inverted) {\n        this.inverted = new Matrix();\n        // todo: link-back the inverted matrix\n        // todo: should the inverted be editable or readonly?\n      }\n      const z = this.a * this.d - this.b * this.c;\n      this.inverted.a = this.d / z;\n      this.inverted.b = -this.b / z;\n      this.inverted.c = -this.c / z;\n      this.inverted.d = this.a / z;\n      this.inverted.e = (this.c * this.f - this.e * this.d) / z;\n      this.inverted.f = (this.e * this.b - this.a * this.f) / z;\n    }\n    return this.inverted;\n  }\n\n  map(p: Vec2Value, q?: Vec2Value) {\n    q = q || { x: 0, y: 0 };\n    q.x = this.a * p.x + this.c * p.y + this.e;\n    q.y = this.b * p.x + this.d * p.y + this.f;\n    return q;\n  }\n\n  mapX(x: number | Vec2Value, y?: number) {\n    if (typeof x === \"object\") {\n      y = x.y;\n      x = x.x;\n    }\n    return this.a * x + this.c * y + this.e;\n  }\n\n  mapY(x: number | Vec2Value, y?: number) {\n    if (typeof x === \"object\") {\n      y = x.y;\n      x = x.x;\n    }\n    return this.b * x + this.d * y + this.f;\n  }\n}\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "/** @internal */\nconst objectToString = Object.prototype.toString;\n\n/** @internal */\nexport function isFn(value: any) {\n  const str = objectToString.call(value);\n  return (\n    str === \"[object Function]\" ||\n    str === \"[object GeneratorFunction]\" ||\n    str === \"[object AsyncFunction]\"\n  );\n}\n\n/** @internal */\nexport function isHash(value: any) {\n  return objectToString.call(value) === \"[object Object]\" && value.constructor === Object;\n  // return value && typeof value === 'object' && !Array.isArray(value) && !isFn(value);\n}\n", "/** @hidden */\nexport default {\n  create: 0,\n  tick: 0,\n  component: 0,\n  draw: 0,\n  fps: 0,\n};\n", "/** @internal */\nexport const uid = function () {\n  return Date.now().toString(36) + Math.random().toString(36).slice(2);\n};\n", "import { uid } from \"../common/uid\";\n\n/** @internal */\nexport interface TexturePrerenderContext {\n  pixelRatio: number;\n}\n\n// todo: unify 9-patch and padding?\n// todo: prerender is used to get texture width and height, replace it with a function to only get width and height\n\n// todo: add reusable shape textures\n\n/**\n * Textures are used to clip and resize image objects.\n */\nexport abstract class Texture {\n  /** @internal */ uid = \"texture:\" + uid();\n\n  /** @hidden */ sx = 0;\n  /** @hidden */ sy = 0;\n  /** @hidden */ sw: number;\n  /** @hidden */ sh: number;\n  /** @hidden */ dx = 0;\n  /** @hidden */ dy = 0;\n  /** @hidden */ dw: number;\n  /** @hidden */ dh: number;\n\n  // 9-patch resizing specification\n  // todo: rename to something more descriptive\n  /** @internal */ top: number;\n  /** @internal */ bottom: number;\n  /** @internal */ left: number;\n  /** @internal */ right: number;\n\n  // Geometrical values\n  setSourceCoordinate(x: number, y: number) {\n    this.sx = x;\n    this.sy = y;\n  }\n\n  // Geometrical values\n  setSourceDimension(w: number, h: number) {\n    this.sw = w;\n    this.sh = h;\n  }\n\n  // Geometrical values\n  setDestinationCoordinate(x: number, y: number) {\n    this.dx = x;\n    this.dy = y;\n  }\n\n  // Geometrical values\n  setDestinationDimension(w: number, h: number) {\n    this.dw = w;\n    this.dh = h;\n  }\n\n  abstract getWidth(): number;\n\n  abstract getHeight(): number;\n\n  /**\n   * @internal\n   * This is used by subclasses, such as CanvasTexture, to rerender offscreen texture if needed.\n   */\n  abstract prerender(context: TexturePrerenderContext): boolean;\n\n  /**\n   * Defer draw spec to texture config. This is used when a sprite draws its textures.\n   */\n  draw(context: CanvasRenderingContext2D): void;\n  /**\n   * This is probably unused.\n   * Note: dx, dy are added to this.dx, this.dy.\n   */\n  draw(context: CanvasRenderingContext2D, dx: number, dy: number, dw: number, dh: number): void;\n  /**\n   * This is used when a piped texture passes drawing to it backend.\n   * Note: sx, sy, dx, dy are added to this.sx, this.sy, this.dx, this.dy.\n   */\n  draw(\n    context: CanvasRenderingContext2D,\n    sx: number,\n    sy: number,\n    sw: number,\n    sh: number,\n    dx: number,\n    dy: number,\n    dw: number,\n    dh: number,\n  ): void;\n  draw(\n    context: CanvasRenderingContext2D,\n    x1?: number,\n    y1?: number,\n    w1?: number,\n    h1?: number,\n    x2?: number,\n    y2?: number,\n    w2?: number,\n    h2?: number,\n  ): void {\n    let sx: number, sy: number, sw: number, sh: number;\n    let dx: number, dy: number, dw: number, dh: number;\n\n    if (arguments.length > 5) {\n      // two sets of [x, y, w, h] arguments\n      sx = this.sx + x1;\n      sy = this.sy + y1;\n      sw = w1 ?? this.sw;\n      sh = h1 ?? this.sh;\n\n      dx = this.dx + x2;\n      dy = this.dy + y2;\n      dw = w2 ?? this.dw;\n      dh = h2 ?? this.dh;\n    } else if(arguments.length > 1) {\n      // one set of [x, y, w, h] arguments\n      sx = this.sx;\n      sy = this.sy;\n      sw = this.sw;\n      sh = this.sh;\n\n      dx = this.dx + x1;\n      dy = this.dy + y1;\n      dw = w1 ?? this.dw;\n      dh = h1 ?? this.dh;\n    } else {\n      // no additional arguments\n      sx = this.sx;\n      sy = this.sy;\n      sw = this.sw;\n      sh = this.sh;\n\n      dx = this.dx;\n      dy = this.dy;\n      dw = this.dw;\n      dh = this.dh;\n    }\n\n    this.drawWithNormalizedArgs(context, sx, sy, sw, sh, dx, dy, dw, dh);\n  }\n\n  /** @internal */\n  abstract drawWithNormalizedArgs(\n    context: CanvasRenderingContext2D,\n    sx: number,\n    sy: number,\n    sw: number,\n    sh: number,\n    dx: number,\n    dy: number,\n    dw: number,\n    dh: number,\n  ): void;\n}\n", "import stats from \"../common/stats\";\nimport { clamp } from \"../common/math\";\n\nimport { Texture, TexturePrerenderContext } from \"./texture\";\n\ntype TextureImageSource =\n  | HTMLImageElement\n  | HTMLVideoElement\n  | HTMLCanvasElement\n  | ImageBitmap\n  | OffscreenCanvas;\n\nexport class ImageTexture extends Texture {\n  /** @internal */ _source: TextureImageSource;\n\n  /** @internal */ _pixelRatio = 1;\n\n  /** @internal */ _draw_failed: boolean;\n\n  /** @internal */\n  private padding = 0;\n\n  constructor(source?: TextureImageSource, pixelRatio?: number) {\n    super();\n    if (typeof source === \"object\") {\n      this.setSourceImage(source, pixelRatio);\n    }\n  }\n\n  setSourceImage(image: TextureImageSource, pixelRatio = 1) {\n    this._source = image;\n    this._pixelRatio = pixelRatio;\n  }\n\n  /**\n   * Add padding to the image texture. Padding can be negative.\n   */\n  setPadding(padding: number): void {\n    this.padding = padding;\n  }\n\n  getWidth(): number {\n    return this._source.width / this._pixelRatio + (this.padding + this.padding);\n  }\n\n  getHeight(): number {\n    return this._source.height / this._pixelRatio + (this.padding + this.padding);\n  }\n\n  /** @internal */\n  prerender(context: TexturePrerenderContext): boolean {\n    return false;\n  }\n\n  /** @internal */\n  drawWithNormalizedArgs(\n    context: CanvasRenderingContext2D,\n    sx: number,\n    sy: number,\n    sw: number,\n    sh: number,\n    dx: number,\n    dy: number,\n    dw: number,\n    dh: number,\n  ): void {\n    const image = this._source;\n    if (image === null || typeof image !== \"object\") {\n      return;\n    }\n\n    sw = sw ?? this._source.width / this._pixelRatio;\n    sh = sh ?? this._source.height / this._pixelRatio;\n\n    dw = dw ?? sw;\n    dh = dh ?? sh;\n\n    dx += this.padding;\n    dy += this.padding;\n\n    const ix = sx * this._pixelRatio;\n    const iy = sy * this._pixelRatio;\n    const iw = sw * this._pixelRatio;\n    const ih = sh * this._pixelRatio;\n\n    try {\n      stats.draw++;\n\n      // sx = clamp(sx, 0, this._source.width);\n      // sy = clamp(sx, 0, this._source.height);\n      // sw = clamp(sw, 0, this._source.width - sw);\n      // sh = clamp(sh, 0, this._source.height - sh);\n\n      context.drawImage(image, ix, iy, iw, ih, dx, dy, dw, dh);\n    } catch (ex) {\n      if (!this._draw_failed) {\n        console.log(\"Unable to draw: \", image);\n        console.log(ex);\n        this._draw_failed = true;\n      }\n    }\n  }\n}\n", "import { Texture, TexturePrerenderContext } from \"./texture\";\n\nexport class PipeTexture extends Texture {\n  /** @internal */ _source: Texture;\n\n  constructor(source: Texture) {\n    super();\n    this._source = source;\n  }\n\n  setSourceTexture(texture: Texture) {\n    this._source = texture;\n  }\n\n  getWidth(): number {\n    return this.dw ?? this.sw ?? this._source.getWidth();\n  }\n\n  getHeight(): number {\n    return this.dh ?? this.sh ?? this._source.getHeight();\n  }\n\n  /** @internal */\n  prerender(context: TexturePrerenderContext): boolean {\n    return this._source.prerender(context);\n  }\n\n  /** @internal */\n  drawWithNormalizedArgs(\n    context: CanvasRenderingContext2D,\n    sx: number,\n    sy: number,\n    sw: number,\n    sh: number,\n    dx: number,\n    dy: number,\n    dw: number,\n    dh: number,\n  ): void {\n    const texture = this._source;\n    if (texture === null || typeof texture !== \"object\") {\n      return;\n    }\n\n    texture.draw(context, sx, sy, sw, sh, dx, dy, dw, dh);\n  }\n}\n", "import { isFn, isHash } from \"../common/is\";\n\nimport { Texture } from \"./texture\";\nimport { TextureSelection } from \"./selection\";\nimport { ImageTexture } from \"./image\";\nimport { PipeTexture } from \"./pipe\";\n\nexport interface AtlasTextureDefinition {\n  x: number;\n  y: number;\n  width: number;\n  height: number;\n\n  left?: number;\n  top?: number;\n  right?: number;\n  bottom?: number;\n}\n\ntype MonotypeAtlasTextureDefinition = Record<string, AtlasTextureDefinition | Texture | string>;\ntype AnimAtlasTextureDefinition = (AtlasTextureDefinition | Texture | string)[];\n\nexport interface AtlasDefinition {\n  name?: string;\n  image?:\n    | {\n        src: string;\n        ratio?: number;\n      }\n    | {\n        /** @deprecated Use src instead of url */\n        url: string;\n        ratio?: number;\n      };\n\n  ppu?: number;\n  textures?: Record<\n    string,\n    AtlasTextureDefinition | Texture | MonotypeAtlasTextureDefinition | AnimAtlasTextureDefinition\n  >;\n\n  map?: (texture: AtlasTextureDefinition) => AtlasTextureDefinition;\n\n  /** @deprecated Use map */\n  filter?: (texture: AtlasTextureDefinition) => AtlasTextureDefinition;\n\n  /** @deprecated */\n  trim?: number;\n  /** @deprecated Use ppu */\n  ratio?: number;\n\n  /** @deprecated Use map */\n  imagePath?: string;\n  /** @deprecated Use map */\n  imageRatio?: number;\n}\n\nexport class Atlas extends ImageTexture {\n  /** @internal */ name: any;\n\n  /** @internal */ _ppu: any;\n  /** @internal */ _trim: any;\n  /** @internal */ _map: any;\n  /** @internal */ _textures: any;\n  /** @internal */ _imageSrc: string;\n\n  constructor(def: AtlasDefinition = {}) {\n    super();\n\n    this.name = def.name;\n    this._ppu = def.ppu || def.ratio || 1;\n    this._trim = def.trim || 0;\n\n    this._map = def.map || def.filter;\n    this._textures = def.textures;\n\n    if (typeof def.image === \"object\" && isHash(def.image)) {\n      if (\"src\" in def.image) {\n        this._imageSrc = def.image.src;\n      } else if (\"url\" in def.image) {\n        this._imageSrc = def.image.url;\n      }\n      if (typeof def.image.ratio === \"number\") {\n        this._pixelRatio = def.image.ratio;\n      }\n    } else {\n      if (typeof def.imagePath === \"string\") {\n        this._imageSrc = def.imagePath;\n      } else if (typeof def.image === \"string\") {\n        this._imageSrc = def.image;\n      }\n      if (typeof def.imageRatio === \"number\") {\n        this._pixelRatio = def.imageRatio;\n      }\n    }\n\n    deprecatedWarning(def);\n  }\n\n  async load() {\n    if (this._imageSrc) {\n      const image = await asyncLoadImage(this._imageSrc);\n      this.setSourceImage(image, this._pixelRatio);\n    }\n  }\n\n  /**\n   * @internal\n   * Uses the definition to create a texture object from this atlas.\n   */\n  pipeSpriteTexture = (def: AtlasTextureDefinition): Texture => {\n    const map = this._map;\n    const ppu = this._ppu;\n    const trim = this._trim;\n\n    if (!def) {\n      return undefined;\n    }\n\n    def = Object.assign({}, def);\n\n    if (isFn(map)) {\n      def = map(def);\n    }\n\n    if (ppu != 1) {\n      def.x *= ppu;\n      def.y *= ppu;\n      def.width *= ppu;\n      def.height *= ppu;\n      def.top *= ppu;\n      def.bottom *= ppu;\n      def.left *= ppu;\n      def.right *= ppu;\n    }\n\n    if (trim != 0) {\n      def.x += trim;\n      def.y += trim;\n      def.width -= 2 * trim;\n      def.height -= 2 * trim;\n      def.top -= trim;\n      def.bottom -= trim;\n      def.left -= trim;\n      def.right -= trim;\n    }\n\n    const texture = new PipeTexture(this);\n    texture.top = def.top;\n    texture.bottom = def.bottom;\n    texture.left = def.left;\n    texture.right = def.right;\n    texture.setSourceCoordinate(def.x, def.y);\n    texture.setSourceDimension(def.width, def.height);\n    return texture;\n  };\n\n  /**\n   * @internal\n   * Looks up and returns texture definition.\n   */\n  findSpriteDefinition = (query: string) => {\n    const textures = this._textures;\n\n    if (textures) {\n      if (isFn(textures)) {\n        return textures(query);\n      } else if (isHash(textures)) {\n        return textures[query];\n      }\n    }\n  };\n\n  // returns Selection, and then selection.one/array returns actual texture/textures\n  select = (query?: string) => {\n    if (!query) {\n      // TODO: if `textures` is texture def, map or fn?\n      return new TextureSelection(new PipeTexture(this));\n    }\n    const textureDefinition = this.findSpriteDefinition(query);\n    if (textureDefinition) {\n      return new TextureSelection(textureDefinition, this);\n    }\n  };\n}\n\n/** @internal */\nfunction asyncLoadImage(src: string) {\n  console.debug && console.debug(\"Loading image: \" + src);\n  return new Promise<HTMLImageElement>(function (resolve, reject) {\n    const img = new Image();\n    img.onload = function () {\n      console.debug && console.debug(\"Image loaded: \" + src);\n      resolve(img);\n    };\n    img.onerror = function (error) {\n      console.error(\"Loading failed: \" + src);\n      reject(error);\n    };\n    img.src = src;\n  });\n}\n\n/** @internal */\nfunction deprecatedWarning(def: AtlasDefinition) {\n  if (\"filter\" in def) console.warn(\"'filter' field of atlas definition is deprecated\");\n\n  // todo: throw error here?\n  if (\"cutouts\" in def) console.warn(\"'cutouts' field of atlas definition is deprecated\");\n\n  // todo: throw error here?\n  if (\"sprites\" in def) console.warn(\"'sprites' field of atlas definition is deprecated\");\n\n  // todo: throw error here?\n  if (\"factory\" in def) console.warn(\"'factory' field of atlas definition is deprecated\");\n\n  if (\"ratio\" in def) console.warn(\"'ratio' field of atlas definition is deprecated\");\n\n  if (\"imagePath\" in def) console.warn(\"'imagePath' field of atlas definition is deprecated\");\n\n  if (\"imageRatio\" in def) console.warn(\"'imageRatio' field of atlas definition is deprecated\");\n\n  if (typeof def.image === \"object\" && \"url\" in def.image)\n    console.warn(\"'image.url' field of atlas definition is deprecated\");\n}\n", "import { isFn, isHash } from \"../common/is\";\n\nimport { Atlas, AtlasDefinition, AtlasTextureDefinition } from \"./atlas\";\nimport { Texture, TexturePrerenderContext } from \"./texture\";\n\nexport type TextureSelectionInputOne = Texture | AtlasTextureDefinition | string;\nexport type TextureSelectionInputMap = Record<string, TextureSelectionInputOne>;\nexport type TextureSelectionInputArray = TextureSelectionInputOne[];\nexport type TextureSelectionInputFactory = (subquery: string) => TextureSelectionInputOne;\n\n/**\n * Texture selection input could be one:\n * - texture\n * - sprite definition (and an atlas): atlas sprite texture\n * - string (with an atlas): string used as key to find sprite in the atlas, re-resolve\n * - hash object: use subquery as key, then re-resolve value\n * - array: re-resolve first item\n * - function: call function with subquery, then re-resolve\n */\nexport type TextureSelectionInput =\n  | TextureSelectionInputOne\n  | TextureSelectionInputMap\n  | TextureSelectionInputArray\n  | TextureSelectionInputFactory;\n\n/** @internal */\nfunction isAtlasSpriteDefinition(selection: any) {\n  return (\n    typeof selection === \"object\" &&\n    isHash(selection) &&\n    \"number\" === typeof selection.width &&\n    \"number\" === typeof selection.height\n  );\n}\n\n/**\n * TextureSelection holds reference to one or many textures or something that\n * can be resolved to one or many textures. This is used to decouple resolving\n * references to textures from rendering them in various ways.\n */\nexport class TextureSelection {\n  selection: TextureSelectionInput;\n  atlas: Atlas;\n  constructor(selection: TextureSelectionInput, atlas?: Atlas) {\n    this.selection = selection;\n    this.atlas = atlas;\n  }\n\n  /**\n   * @internal\n   * Resolves the selection to a texture.\n   */\n  resolve(selection: TextureSelectionInput, subquery?: string): Texture {\n    if (!selection) {\n      return NO_TEXTURE;\n    } else if (Array.isArray(selection)) {\n      return this.resolve(selection[0]);\n    } else if (selection instanceof Texture) {\n      return selection;\n    } else if (isAtlasSpriteDefinition(selection)) {\n      if (!this.atlas) {\n        return NO_TEXTURE;\n      }\n      return this.atlas.pipeSpriteTexture(selection as AtlasTextureDefinition);\n    } else if (\n      typeof selection === \"object\" &&\n      isHash(selection) &&\n      typeof subquery !== \"undefined\"\n    ) {\n      return this.resolve(selection[subquery]);\n    } else if (typeof selection === \"function\" && isFn(selection)) {\n      return this.resolve(selection(subquery));\n    } else if (typeof selection === \"string\") {\n      if (!this.atlas) {\n        return NO_TEXTURE;\n      }\n      return this.resolve(this.atlas.findSpriteDefinition(selection));\n    }\n  }\n\n  one(subquery?: string): Texture {\n    return this.resolve(this.selection, subquery);\n  }\n\n  array(arr?: Texture[]): Texture[] {\n    const array = Array.isArray(arr) ? arr : [];\n    if (Array.isArray(this.selection)) {\n      for (let i = 0; i < this.selection.length; i++) {\n        array[i] = this.resolve(this.selection[i]);\n      }\n    } else {\n      array[0] = this.resolve(this.selection);\n    }\n    return array;\n  }\n}\n\n/** @internal */\nconst NO_TEXTURE = new (class extends Texture {\n  getWidth(): number {\n    return 0;\n  }\n  getHeight(): number {\n    return 0;\n  }\n  prerender(context: TexturePrerenderContext): boolean {\n    return false;\n  }\n  drawWithNormalizedArgs(\n    context: CanvasRenderingContext2D,\n    sx: number,\n    sy: number,\n    sw: number,\n    sh: number,\n    dx: number,\n    dy: number,\n    dw: number,\n    dh: number,\n  ): void {}\n  constructor() {\n    super();\n    this.setSourceDimension(0, 0);\n  }\n  setSourceCoordinate(x: any, y: any): void {}\n  setSourceDimension(w: any, h: any): void {}\n  setDestinationCoordinate(x: number, y: number): void {}\n  setDestinationDimension(w: number, h: number): void {}\n  draw(): void {}\n})();\n\n/** @internal */\nconst NO_SELECTION = new TextureSelection(NO_TEXTURE);\n\n/** @internal */\nconst ATLAS_MEMO_BY_NAME: Record<string, Atlas> = {};\n\n/** @internal */\nconst ATLAS_ARRAY: Atlas[] = [];\n\n// TODO: print subquery not found error\n// TODO: index textures\n\nexport async function atlas(def: AtlasDefinition | Atlas): Promise<Atlas> {\n  // todo: where is this used?\n  let atlas: Atlas;\n  if (def instanceof Atlas) {\n    atlas = def;\n  } else {\n    atlas = new Atlas(def);\n  }\n\n  if (atlas.name) {\n    ATLAS_MEMO_BY_NAME[atlas.name] = atlas;\n  }\n  ATLAS_ARRAY.push(atlas);\n\n  await atlas.load();\n\n  return atlas;\n}\n\n/**\n * When query argument is string, this function parses the query; looks up registered atlases; and returns a texture selection object.\n *\n * When query argument is an object, the object is used to create a new selection.\n */\nexport function texture(query: string | TextureSelectionInput): TextureSelection {\n  if (\"string\" !== typeof query) {\n    return new TextureSelection(query);\n  }\n\n  let result: TextureSelection | undefined | null = null;\n\n  // parse query as atlas-name:texture-name\n  const colonIndex = query.indexOf(\":\");\n  if (colonIndex > 0 && query.length > colonIndex + 1) {\n    const atlas = ATLAS_MEMO_BY_NAME[query.slice(0, colonIndex)];\n    result = atlas && atlas.select(query.slice(colonIndex + 1));\n  }\n\n  if (!result) {\n    // use query as \"atlas-name\", return entire atlas\n    const atlas = ATLAS_MEMO_BY_NAME[query];\n    result = atlas && atlas.select();\n  }\n\n  if (!result) {\n    // use query as \"texture-name\", search over all atlases\n    for (let i = 0; i < ATLAS_ARRAY.length; i++) {\n      result = ATLAS_ARRAY[i].select(query);\n      if (result) {\n        break;\n      }\n    }\n  }\n\n  if (!result) {\n    console.error(\"Texture not found: \" + query);\n    result = NO_SELECTION;\n  }\n\n  return result;\n}\n", "import { Texture, TexturePrerenderContext } from \"./texture\";\n\nexport type ResizableTextureMode = \"stretch\" | \"tile\";\n\nexport class ResizableTexture extends Texture {\n  /** @internal */ _source: Texture;\n\n  /** @internal */ _resizeMode: ResizableTextureMode;\n  /** @internal */ _innerSize: boolean;\n\n  constructor(source: Texture, mode: ResizableTextureMode) {\n    super();\n    this._source = source;\n    this._resizeMode = mode;\n  }\n\n  getWidth(): number {\n    // this is the last known width\n    return this.dw ?? this._source.getWidth();\n  }\n\n  getHeight(): number {\n    // this is the last known height\n    return this.dh ?? this._source.getHeight();\n  }\n\n  /** @internal */\n  prerender(context: TexturePrerenderContext): boolean {\n    return false;\n  }\n\n  drawWithNormalizedArgs(\n    context: CanvasRenderingContext2D,\n    sx: number,\n    sy: number,\n    sw: number,\n    sh: number,\n    dx: number,\n    dy: number,\n    dw: number,\n    dh: number,\n  ): void {\n    const texture = this._source;\n    if (texture === null || typeof texture !== \"object\") {\n      return;\n    }\n\n    let outWidth = dw;\n    let outHeight = dh;\n\n    const left = Number.isFinite(texture.left) ? texture.left : 0;\n    const right = Number.isFinite(texture.right) ? texture.right : 0;\n    const top = Number.isFinite(texture.top) ? texture.top : 0;\n    const bottom = Number.isFinite(texture.bottom) ? texture.bottom : 0;\n\n    const width = texture.getWidth() - left - right;\n    const height = texture.getHeight() - top - bottom;\n\n    if (!this._innerSize) {\n      outWidth = Math.max(outWidth - left - right, 0);\n      outHeight = Math.max(outHeight - top - bottom, 0);\n    }\n\n    // corners\n    if (top > 0 && left > 0) {\n      texture.draw(context, 0, 0, left, top, 0, 0, left, top);\n    }\n    if (bottom > 0 && left > 0) {\n      texture.draw(context, 0, height + top, left, bottom, 0, outHeight + top, left, bottom);\n    }\n    if (top > 0 && right > 0) {\n      texture.draw(context, width + left, 0, right, top, outWidth + left, 0, right, top);\n    }\n    if (bottom > 0 && right > 0) {\n      texture.draw(\n        context,\n        width + left,\n        height + top,\n        right,\n        bottom,\n        outWidth + left,\n        outHeight + top,\n        right,\n        bottom,\n      );\n    }\n\n    if (this._resizeMode === \"stretch\") {\n      // sides\n      if (top > 0) {\n        texture.draw(context, left, 0, width, top, left, 0, outWidth, top);\n      }\n      if (bottom > 0) {\n        texture.draw(\n          context,\n          left,\n          height + top,\n          width,\n          bottom,\n          left,\n          outHeight + top,\n          outWidth,\n          bottom,\n        );\n      }\n      if (left > 0) {\n        texture.draw(context, 0, top, left, height, 0, top, left, outHeight);\n      }\n      if (right > 0) {\n        texture.draw(\n          context,\n          width + left,\n          top,\n          right,\n          height,\n          outWidth + left,\n          top,\n          right,\n          outHeight,\n        );\n      }\n      // center\n      texture.draw(context, left, top, width, height, left, top, outWidth, outHeight);\n    } else if (this._resizeMode === \"tile\") {\n      // tile\n      let l = left;\n      let r = outWidth;\n      let w: number;\n      while (r > 0) {\n        w = Math.min(width, r);\n        r -= width;\n        let t = top;\n        let b = outHeight;\n        let h: number;\n        while (b > 0) {\n          h = Math.min(height, b);\n          b -= height;\n          texture.draw(context, left, top, w, h, l, t, w, h);\n          if (r <= 0) {\n            if (left) {\n              texture.draw(context, 0, top, left, h, 0, t, left, h);\n            }\n            if (right) {\n              texture.draw(context, width + left, top, right, h, l + w, t, right, h);\n            }\n          }\n          t += h;\n        }\n        if (top) {\n          texture.draw(context, left, 0, w, top, l, 0, w, top);\n        }\n        if (bottom) {\n          texture.draw(context, left, height + top, w, bottom, l, t, w, bottom);\n        }\n        l += w;\n      }\n    }\n  }\n}\n", "/** @internal */\nexport function getDevicePixelRatio() {\n  // todo: do we need to divide by backingStoreRatio?\n  return typeof window !== \"undefined\" ? window.devicePixelRatio || 1 : 1;\n}\n", "import { Matrix, Vec2Value } from \"../common/matrix\";\nimport { uid } from \"../common/uid\";\n\nimport type { Component } from \"./component\";\n\n/**\n *  @hidden @deprecated\n * - 'in-pad': same as 'contain'\n * - 'in': similar to 'contain' without centering\n * - 'out-crop': same as 'cover'\n * - 'out': similar to 'cover' without centering\n */\nexport type LegacyFitMode = \"in\" | \"out\" | \"out-crop\" | \"in-pad\";\n\n/**\n * - 'contain': contain within the provided space, maintain aspect ratio\n * - 'cover': cover the provided space, maintain aspect ratio\n * - 'fill': fill provided space without maintaining aspect ratio\n */\nexport type FitMode = \"contain\" | \"cover\" | \"fill\" | LegacyFitMode;\n\n/** @internal */\nexport function isValidFitMode(value: string) {\n  return (\n    value &&\n    (value === \"cover\" ||\n      value === \"contain\" ||\n      value === \"fill\" ||\n      value === \"in\" ||\n      value === \"in-pad\" ||\n      value === \"out\" ||\n      value === \"out-crop\")\n  );\n}\n\n/** @internal */ let iid = 0;\n\n/** @hidden */\nexport interface Pinned {\n  pin(pin: object): this;\n  pin(key: string, value: any): this;\n  pin(key: string): any;\n\n  size(w: number, h: number): this;\n\n  width(): number;\n  width(w: number): this;\n\n  height(): number;\n  height(h: number): this;\n\n  offset(a: Vec2Value): this;\n  offset(a: number, b: number): this;\n\n  rotate(a: number): this;\n\n  skew(a: Vec2Value): this;\n  skew(a: number, b: number): this;\n\n  scale(a: Vec2Value): this;\n  scale(a: number, b: number): this;\n\n  alpha(a: number, ta?: number): this;\n}\n\nexport class Pin {\n  /** @internal */ uid = \"pin:\" + uid();\n\n  /** @internal */ _owner: Component;\n\n  // todo: maybe this should be a getter instead?\n  /** @internal */ _parent: Pin | null;\n\n  /** @internal */ _relativeMatrix: Matrix;\n  /** @internal */ _absoluteMatrix: Matrix;\n\n  /** @internal */ _x: number;\n  /** @internal */ _y: number;\n\n  /** @internal */ _unscaled_width: number;\n  /** @internal */ _unscaled_height: number;\n\n  /** @internal */ _width: number;\n  /** @internal */ _height: number;\n\n  /** @internal */ _textureAlpha: number;\n  /** @internal */ _alpha: number;\n\n  /** @internal */ _scaleX: number;\n  /** @internal */ _scaleY: number;\n\n  /** @internal */ _skewX: number;\n  /** @internal */ _skewY: number;\n  /** @internal */ _rotation: number;\n\n  /** @internal */ _pivoted: boolean;\n  /** @internal */ _pivotX: number;\n  /** @internal */ _pivotY: number;\n\n  /** @internal */ _handled: boolean;\n  /** @internal */ _handleX: number;\n  /** @internal */ _handleY: number;\n\n  /** @internal */ _aligned: boolean;\n  /** @internal */ _alignX: number;\n  /** @internal */ _alignY: number;\n\n  /** @internal */ _offsetX: number;\n  /** @internal */ _offsetY: number;\n\n  /** @internal */ _boxX: number;\n  /** @internal */ _boxY: number;\n  /** @internal */ _boxWidth: number;\n  /** @internal */ _boxHeight: number;\n\n  /** @internal */ _ts_transform: number;\n  /** @internal */ _ts_translate: number;\n  /** @internal */ _ts_matrix: number;\n\n  /** @internal */ _mo_handle: number;\n  /** @internal */ _mo_align: number;\n  /** @internal */ _mo_abs: number;\n  /** @internal */ _mo_rel: number;\n\n  /** @internal */ _directionX = 1;\n  /** @internal */ _directionY = 1;\n\n  /** @internal */\n  constructor(owner: Component) {\n    this._owner = owner;\n    this._parent = null;\n\n    // relative to parent\n    this._relativeMatrix = new Matrix();\n\n    // relative to stage\n    this._absoluteMatrix = new Matrix();\n\n    this.reset();\n  }\n\n  reset() {\n    this._textureAlpha = 1;\n    this._alpha = 1;\n\n    this._width = 0;\n    this._height = 0;\n\n    this._scaleX = 1;\n    this._scaleY = 1;\n    this._skewX = 0;\n    this._skewY = 0;\n    this._rotation = 0;\n\n    // scale/skew/rotate center\n    this._pivoted = false;\n    // todo: this used to be null\n    this._pivotX = 0;\n    this._pivotY = 0;\n\n    // self pin point\n    this._handled = false;\n    this._handleX = 0;\n    this._handleY = 0;\n\n    // parent pin point\n    this._aligned = false;\n    this._alignX = 0;\n    this._alignY = 0;\n\n    // as seen by parent px\n    this._offsetX = 0;\n    this._offsetY = 0;\n\n    this._boxX = 0;\n    this._boxY = 0;\n    this._boxWidth = this._width;\n    this._boxHeight = this._height;\n\n    // TODO: also set for owner\n    this._ts_translate = ++iid;\n    this._ts_transform = ++iid;\n    this._ts_matrix = ++iid;\n  }\n\n  /** @internal */\n  _update() {\n    this._parent = this._owner._parent && this._owner._parent._pin;\n\n    // if handled and transformed then be translated\n    if (this._handled && this._mo_handle != this._ts_transform) {\n      this._mo_handle = this._ts_transform;\n      this._ts_translate = ++iid;\n    }\n\n    if (this._aligned && this._parent && this._mo_align != this._parent._ts_transform) {\n      this._mo_align = this._parent._ts_transform;\n      this._ts_translate = ++iid;\n    }\n\n    return this;\n  }\n\n  toString() {\n    return this._owner + \" (\" + (this._parent ? this._parent._owner : null) + \")\";\n  }\n\n  // TODO: ts fields require refactoring\n  absoluteMatrix() {\n    this._update();\n    const ts = Math.max(\n      this._ts_transform,\n      this._ts_translate,\n      this._parent ? this._parent._ts_matrix : 0,\n    );\n    if (this._mo_abs == ts) {\n      return this._absoluteMatrix;\n    }\n    this._mo_abs = ts;\n\n    const abs = this._absoluteMatrix;\n    abs.reset(this.relativeMatrix());\n\n    this._parent && abs.concat(this._parent._absoluteMatrix);\n\n    this._ts_matrix = ++iid;\n\n    return abs;\n  }\n\n  relativeMatrix() {\n    this._update();\n    const ts = Math.max(\n      this._ts_transform,\n      this._ts_translate,\n      this._parent ? this._parent._ts_transform : 0,\n    );\n    if (this._mo_rel == ts) {\n      return this._relativeMatrix;\n    }\n    this._mo_rel = ts;\n\n    const rel = this._relativeMatrix;\n\n    rel.identity();\n    if (this._pivoted) {\n      rel.translate(-this._pivotX * this._width, -this._pivotY * this._height);\n    }\n    rel.scale(this._scaleX * this._directionX, this._scaleY * this._directionY);\n    rel.skew(this._skewX, this._skewY);\n    rel.rotate(this._rotation);\n    if (this._pivoted) {\n      rel.translate(this._pivotX * this._width, this._pivotY * this._height);\n    }\n\n    // calculate effective box\n    if (this._pivoted) {\n      // origin\n      this._boxX = 0;\n      this._boxY = 0;\n      this._boxWidth = this._width;\n      this._boxHeight = this._height;\n    } else {\n      // aabb\n      let p;\n      let q;\n      if ((rel.a > 0 && rel.c > 0) || (rel.a < 0 && rel.c < 0)) {\n        p = 0;\n        q = rel.a * this._width + rel.c * this._height;\n      } else {\n        p = rel.a * this._width;\n        q = rel.c * this._height;\n      }\n      if (p > q) {\n        this._boxX = q;\n        this._boxWidth = p - q;\n      } else {\n        this._boxX = p;\n        this._boxWidth = q - p;\n      }\n      if ((rel.b > 0 && rel.d > 0) || (rel.b < 0 && rel.d < 0)) {\n        p = 0;\n        q = rel.b * this._width + rel.d * this._height;\n      } else {\n        p = rel.b * this._width;\n        q = rel.d * this._height;\n      }\n      if (p > q) {\n        this._boxY = q;\n        this._boxHeight = p - q;\n      } else {\n        this._boxY = p;\n        this._boxHeight = q - p;\n      }\n    }\n\n    this._x = this._offsetX;\n    this._y = this._offsetY;\n\n    this._x -= this._boxX + this._handleX * this._boxWidth * this._directionX;\n    this._y -= this._boxY + this._handleY * this._boxHeight * this._directionY;\n\n    if (this._aligned && this._parent) {\n      this._parent.relativeMatrix();\n      this._x += this._alignX * this._parent._width;\n      this._y += this._alignY * this._parent._height;\n    }\n\n    rel.translate(this._x, this._y);\n\n    return this._relativeMatrix;\n  }\n\n  /** @internal */\n  get(key: string) {\n    if (typeof getters[key] === \"function\") {\n      return getters[key](this);\n    }\n  }\n\n  // TODO: Use defineProperty instead? What about multi-field pinning?\n  /** @internal */\n  set(a, b?) {\n    if (typeof a === \"string\") {\n      if (typeof setters[a] === \"function\" && typeof b !== \"undefined\") {\n        setters[a](this, b);\n      }\n    } else if (typeof a === \"object\") {\n      for (b in a) {\n        if (typeof setters[b] === \"function\" && typeof a[b] !== \"undefined\") {\n          setters[b](this, a[b], a);\n        }\n      }\n    }\n    if (this._owner) {\n      this._owner._ts_pin = ++iid;\n      this._owner.touch();\n    }\n    return this;\n  }\n\n  // todo: should this be public?\n  /** @internal */\n  fit(width: number | null, height: number | null, mode?: FitMode) {\n    this._ts_transform = ++iid;\n    if (mode === \"contain\") {\n      mode = \"in-pad\";\n    }\n    if (mode === \"cover\") {\n      mode = \"out-crop\";\n    }\n    if (typeof width === \"number\") {\n      this._scaleX = width / this._unscaled_width;\n      this._width = this._unscaled_width;\n    }\n    if (typeof height === \"number\") {\n      this._scaleY = height / this._unscaled_height;\n      this._height = this._unscaled_height;\n    }\n    if (typeof width === \"number\" && typeof height === \"number\" && typeof mode === \"string\") {\n      if (mode === \"fill\") {\n      } else if (mode === \"out\" || mode === \"out-crop\") {\n        this._scaleX = this._scaleY = Math.max(this._scaleX, this._scaleY);\n      } else if (mode === \"in\" || mode === \"in-pad\") {\n        this._scaleX = this._scaleY = Math.min(this._scaleX, this._scaleY);\n      }\n      if (mode === \"out-crop\" || mode === \"in-pad\") {\n        this._width = width / this._scaleX;\n        this._height = height / this._scaleY;\n      }\n    }\n  }\n}\n\n/** @internal */ const getters = {\n  alpha: function (pin: Pin) {\n    return pin._alpha;\n  },\n\n  textureAlpha: function (pin: Pin) {\n    return pin._textureAlpha;\n  },\n\n  width: function (pin: Pin) {\n    return pin._width;\n  },\n\n  height: function (pin: Pin) {\n    return pin._height;\n  },\n\n  boxWidth: function (pin: Pin) {\n    return pin._boxWidth;\n  },\n\n  boxHeight: function (pin: Pin) {\n    return pin._boxHeight;\n  },\n\n  // scale : function(pin: Pin) {\n  // },\n\n  scaleX: function (pin: Pin) {\n    return pin._scaleX;\n  },\n\n  scaleY: function (pin: Pin) {\n    return pin._scaleY;\n  },\n\n  // skew : function(pin: Pin) {\n  // },\n\n  skewX: function (pin: Pin) {\n    return pin._skewX;\n  },\n\n  skewY: function (pin: Pin) {\n    return pin._skewY;\n  },\n\n  rotation: function (pin: Pin) {\n    return pin._rotation;\n  },\n\n  // pivot : function(pin: Pin) {\n  // },\n\n  pivotX: function (pin: Pin) {\n    return pin._pivotX;\n  },\n\n  pivotY: function (pin: Pin) {\n    return pin._pivotY;\n  },\n\n  // offset : function(pin: Pin) {\n  // },\n\n  offsetX: function (pin: Pin) {\n    return pin._offsetX;\n  },\n\n  offsetY: function (pin: Pin) {\n    return pin._offsetY;\n  },\n\n  // align : function(pin: Pin) {\n  // },\n\n  alignX: function (pin: Pin) {\n    return pin._alignX;\n  },\n\n  alignY: function (pin: Pin) {\n    return pin._alignY;\n  },\n\n  // handle : function(pin: Pin) {\n  // },\n\n  handleX: function (pin: Pin) {\n    return pin._handleX;\n  },\n\n  handleY: function (pin: Pin) {\n    return pin._handleY;\n  },\n};\n\ntype ResizeParams = {\n  resizeMode: FitMode;\n  resizeWidth: number;\n  resizeHeight: number;\n};\n\ntype ScaleParams = {\n  scaleMode: FitMode;\n  scaleWidth: number;\n  scaleHeight: number;\n};\n\n/** @internal */ const setters = {\n  alpha: function (pin: Pin, value: number) {\n    pin._alpha = value;\n  },\n\n  textureAlpha: function (pin: Pin, value: number) {\n    pin._textureAlpha = value;\n  },\n\n  width: function (pin: Pin, value: number) {\n    pin._unscaled_width = value;\n    pin._width = value;\n    pin._ts_transform = ++iid;\n  },\n\n  height: function (pin: Pin, value: number) {\n    pin._unscaled_height = value;\n    pin._height = value;\n    pin._ts_transform = ++iid;\n  },\n\n  scale: function (pin: Pin, value: number) {\n    pin._scaleX = value;\n    pin._scaleY = value;\n    pin._ts_transform = ++iid;\n  },\n\n  scaleX: function (pin: Pin, value: number) {\n    pin._scaleX = value;\n    pin._ts_transform = ++iid;\n  },\n\n  scaleY: function (pin: Pin, value: number) {\n    pin._scaleY = value;\n    pin._ts_transform = ++iid;\n  },\n\n  skew: function (pin: Pin, value: number) {\n    pin._skewX = value;\n    pin._skewY = value;\n    pin._ts_transform = ++iid;\n  },\n\n  skewX: function (pin: Pin, value: number) {\n    pin._skewX = value;\n    pin._ts_transform = ++iid;\n  },\n\n  skewY: function (pin: Pin, value: number) {\n    pin._skewY = value;\n    pin._ts_transform = ++iid;\n  },\n\n  rotation: function (pin: Pin, value: number) {\n    pin._rotation = value;\n    pin._ts_transform = ++iid;\n  },\n\n  pivot: function (pin: Pin, value: number) {\n    pin._pivotX = value;\n    pin._pivotY = value;\n    pin._pivoted = true;\n    pin._ts_transform = ++iid;\n  },\n\n  pivotX: function (pin: Pin, value: number) {\n    pin._pivotX = value;\n    pin._pivoted = true;\n    pin._ts_transform = ++iid;\n  },\n\n  pivotY: function (pin: Pin, value: number) {\n    pin._pivotY = value;\n    pin._pivoted = true;\n    pin._ts_transform = ++iid;\n  },\n\n  offset: function (pin: Pin, value: number) {\n    pin._offsetX = value;\n    pin._offsetY = value;\n    pin._ts_translate = ++iid;\n  },\n\n  offsetX: function (pin: Pin, value: number) {\n    pin._offsetX = value;\n    pin._ts_translate = ++iid;\n  },\n\n  offsetY: function (pin: Pin, value: number) {\n    pin._offsetY = value;\n    pin._ts_translate = ++iid;\n  },\n\n  align: function (pin: Pin, value: number) {\n    this.alignX(pin, value);\n    this.alignY(pin, value);\n  },\n\n  alignX: function (pin: Pin, value: number) {\n    pin._alignX = value;\n    pin._aligned = true;\n    pin._ts_translate = ++iid;\n\n    this.handleX(pin, value);\n  },\n\n  alignY: function (pin: Pin, value: number) {\n    pin._alignY = value;\n    pin._aligned = true;\n    pin._ts_translate = ++iid;\n\n    this.handleY(pin, value);\n  },\n\n  handle: function (pin: Pin, value: number) {\n    this.handleX(pin, value);\n    this.handleY(pin, value);\n  },\n\n  handleX: function (pin: Pin, value: number) {\n    pin._handleX = value;\n    pin._handled = true;\n    pin._ts_translate = ++iid;\n  },\n\n  handleY: function (pin: Pin, value: number) {\n    pin._handleY = value;\n    pin._handled = true;\n    pin._ts_translate = ++iid;\n  },\n\n  resizeMode: function (pin: Pin, value: FitMode, all: ResizeParams) {\n    if (all) {\n      if (value == \"in\") {\n        value = \"in-pad\";\n      } else if (value == \"out\") {\n        value = \"out-crop\";\n      }\n      pin.fit(all.resizeWidth, all.resizeHeight, value);\n    }\n  },\n\n  resizeWidth: function (pin: Pin, value: number, all: ResizeParams) {\n    if (!all || !all.resizeMode) {\n      pin.fit(value, null);\n    }\n  },\n\n  resizeHeight: function (pin: Pin, value: number, all: ResizeParams) {\n    if (!all || !all.resizeMode) {\n      pin.fit(null, value);\n    }\n  },\n\n  scaleMode: function (pin: Pin, value: FitMode, all: ScaleParams) {\n    if (all) {\n      pin.fit(all.scaleWidth, all.scaleHeight, value);\n    }\n  },\n\n  scaleWidth: function (pin: Pin, value: number, all: ScaleParams) {\n    if (!all || !all.scaleMode) {\n      pin.fit(value, null);\n    }\n  },\n\n  scaleHeight: function (pin: Pin, value: number, all: ScaleParams) {\n    if (!all || !all.scaleMode) {\n      pin.fit(null, value);\n    }\n  },\n\n  matrix: function (pin: Pin, value: Matrix) {\n    this.scaleX(pin, value.a);\n    this.skewX(pin, value.c / value.d);\n    this.skewY(pin, value.b / value.a);\n    this.scaleY(pin, value.d);\n    this.offsetX(pin, value.e);\n    this.offsetY(pin, value.f);\n    this.rotation(pin, 0);\n  },\n};\n", "/** @internal */\nfunction IDENTITY(x: any) {\n  return x;\n}\n\n/**\n * Easing function formats are:\n * - [name]\n * - [name\\]([params])\n * - [name]-[mode]\n * - [name]-[mode\\]([params])\n *\n * Easing function names are 'linear', 'quad', 'cubic', 'quart', 'quint', 'sin' (or 'sine'), 'exp' (or 'expo'), 'circle' (or 'circ'), 'bounce', 'poly', 'elastic', 'back'.\n *\n * Easing modes are 'in', 'out', 'in-out', 'out-in'.\n *\n * For example, 'linear', 'cubic-in', and 'poly(2)'.\n */\nexport type EasingFunctionName = string;\n\nexport type EasingFunction = (p: number) => number;\n/** @internal */\ntype EasingFunctionFactory = (...paras: any[]) => EasingFunction;\n\n/** @internal */\ntype EasingMode = (f: EasingFunction) => EasingFunction;\n\n/** @internal */\ntype EasingType =\n  | {\n      name: string;\n      fn: EasingFunction;\n    }\n  | {\n      name: string;\n      fc: EasingFunctionFactory;\n    };\n\n/** @internal */ const LOOKUP_CACHE: Record<string, EasingFunction> = {};\n/** @internal */ const MODE_BY_NAME: Record<string, EasingMode> = {};\n\n// split this to functions and factories\n/** @internal */ const EASE_BY_NAME: Record<string, EasingType> = {};\n\n// todo: make easing names and list statics?\n// todo: pass additional params as ...rest, instead of factories/curring? (`fc`)\n// todo: simplify add functions as (name, fn)?\n\nexport class Easing {\n  static get(\n    token: EasingFunctionName | EasingFunction,\n    fallback?: EasingFunction,\n  ): EasingFunction {\n    fallback = fallback || IDENTITY;\n    if (typeof token === \"function\") {\n      return token;\n    }\n    if (typeof token !== \"string\") {\n      return fallback;\n    }\n    let easeFn = LOOKUP_CACHE[token];\n    if (easeFn) {\n      return easeFn;\n    }\n    const tokens = /^(\\w+)(-(in|out|in-out|out-in))?(\\((.*)\\))?$/i.exec(token);\n    if (!tokens || !tokens.length) {\n      return fallback;\n    }\n\n    const easeName = tokens[1];\n    const easing = EASE_BY_NAME[easeName];\n\n    const modeName = tokens[3];\n    const modeFn = MODE_BY_NAME[modeName];\n\n    const params = tokens[5];\n\n    if (!easing) {\n      easeFn = fallback;\n    } else if (\"fn\" in easing && typeof easing.fn === \"function\") {\n      easeFn = easing.fn;\n    } else if (\"fc\" in easing && typeof easing.fc === \"function\") {\n      const args = params ? params.replace(/\\s+/, \"\").split(\",\") : undefined;\n      easeFn = easing.fc.apply(easing.fc, args);\n    } else {\n      easeFn = fallback;\n    }\n\n    if (modeFn) {\n      easeFn = modeFn(easeFn);\n    }\n    // TODO: It can be a memory leak with different `params`.\n    LOOKUP_CACHE[token] = easeFn;\n\n    return easeFn;\n  }\n}\n\n/** @internal */\nfunction addMode(name: string, fn: EasingMode) {\n  MODE_BY_NAME[name] = fn;\n}\n\n/** @internal */\nfunction addEaseFn(data: EasingType) {\n  const names = data.name.split(/\\s+/);\n  for (let i = 0; i < names.length; i++) {\n    const key = names[i];\n    if (key) {\n      EASE_BY_NAME[key] = data;\n    }\n  }\n}\n\naddMode(\"in\", function (f: EasingFunction) {\n  return f;\n});\n\naddMode(\"out\", function (f: EasingFunction) {\n  return function (t: number) {\n    return 1 - f(1 - t);\n  };\n});\n\naddMode(\"in-out\", function (f: EasingFunction) {\n  return function (t: number) {\n    return t < 0.5 ? f(2 * t) / 2 : 1 - f(2 * (1 - t)) / 2;\n  };\n});\n\naddMode(\"out-in\", function (f: EasingFunction) {\n  return function (t: number) {\n    return t < 0.5 ? 1 - f(2 * (1 - t)) / 2 : f(2 * t) / 2;\n  };\n});\n\naddEaseFn({\n  name: \"linear\",\n  fn: function (t: number) {\n    return t;\n  },\n});\n\naddEaseFn({\n  name: \"quad\",\n  fn: function (t: number) {\n    return t * t;\n  },\n});\n\naddEaseFn({\n  name: \"cubic\",\n  fn: function (t: number) {\n    return t * t * t;\n  },\n});\n\naddEaseFn({\n  name: \"quart\",\n  fn: function (t: number) {\n    return t * t * t * t;\n  },\n});\n\naddEaseFn({\n  name: \"quint\",\n  fn: function (t: number) {\n    return t * t * t * t * t;\n  },\n});\n\naddEaseFn({\n  name: \"sin sine\",\n  fn: function (t: number) {\n    return 1 - Math.cos((t * Math.PI) / 2);\n  },\n});\n\naddEaseFn({\n  name: \"exp expo\",\n  fn: function (t: number) {\n    return t == 0 ? 0 : Math.pow(2, 10 * (t - 1));\n  },\n});\n\naddEaseFn({\n  name: \"circle circ\",\n  fn: function (t: number) {\n    return 1 - Math.sqrt(1 - t * t);\n  },\n});\n\naddEaseFn({\n  name: \"bounce\",\n  fn: function (t: number) {\n    return t < 1 / 2.75\n      ? 7.5625 * t * t\n      : t < 2 / 2.75\n        ? 7.5625 * (t -= 1.5 / 2.75) * t + 0.75\n        : t < 2.5 / 2.75\n          ? 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375\n          : 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;\n  },\n});\n\naddEaseFn({\n  name: \"poly\",\n  fc: function (e) {\n    return function (t: number) {\n      return Math.pow(t, e);\n    };\n  },\n});\n\naddEaseFn({\n  name: \"elastic\",\n  fc: function (a, p) {\n    p = p || 0.45;\n    a = a || 1;\n    const s = (p / (2 * Math.PI)) * Math.asin(1 / a);\n    return function (t: number) {\n      return 1 + a * Math.pow(2, -10 * t) * Math.sin(((t - s) * (2 * Math.PI)) / p);\n    };\n  },\n});\n\naddEaseFn({\n  name: \"back\",\n  fc: function (s) {\n    s = typeof s !== \"undefined\" ? s : 1.70158;\n    return function (t: number) {\n      return t * t * ((s + 1) * t - s);\n    };\n  },\n});\n", "import { Vec2Value } from \"../common/matrix\";\nimport { uid } from \"../common/uid\";\n\nimport { Easing, EasingFunction, EasingFunctionName } from \"./easing\";\nimport { Component } from \"./component\";\nimport { Pinned } from \"./pin\";\n\nexport type TransitionOptions = {\n  duration?: number;\n  delay?: number;\n  append?: boolean;\n};\n\nexport type TransitionEndListener = (this: Component) => void;\n\nexport class Transition implements Pinned {\n  /** @internal */ uid = \"transition:\" + uid();\n\n  /** @internal */ _end: object;\n  /** @internal */ _start: object;\n\n  /** @internal */ _ending: TransitionEndListener[] = [];\n\n  /** @internal */ _duration: number;\n  /** @internal */ _delay: number;\n\n  /** @internal */ _owner: Component;\n\n  /** @internal */ _time: number;\n\n  /** @internal */ _easing: any;\n  /** @internal */ _next: any;\n\n  /** @internal */ _hide: boolean;\n  /** @internal */ _remove: boolean;\n\n  constructor(owner: Component, options: TransitionOptions = {}) {\n    this._end = {};\n    this._duration = options.duration || 400;\n    this._delay = options.delay || 0;\n\n    this._owner = owner;\n    this._time = 0;\n  }\n\n  /** @internal */\n  tick(component: Component, elapsed: number, now: number, last: number) {\n    this._time += elapsed;\n\n    if (this._time < this._delay) {\n      return;\n    }\n\n    const time = this._time - this._delay;\n\n    if (!this._start) {\n      this._start = {};\n      for (const key in this._end) {\n        this._start[key] = this._owner.pin(key);\n      }\n    }\n\n    let p = Math.min(time / this._duration, 1);\n    const ended = p >= 1;\n\n    if (typeof this._easing == \"function\") {\n      p = this._easing(p);\n    }\n\n    const q = 1 - p;\n\n    for (const key in this._end) {\n      this._owner.pin(key, this._start[key] * q + this._end[key] * p);\n    }\n\n    return ended;\n  }\n\n  /** @internal */\n  finish() {\n    this._ending.forEach((callback: TransitionEndListener) => {\n      try {\n        callback.call(this._owner);\n      } catch (e) {\n        console.error(e);\n      }\n    });\n    return this._next;\n  }\n\n  tween(opts?: TransitionOptions): Transition;\n  tween(duration?: number, delay?: number): Transition;\n  tween(a?: object | number, b?: number) {\n    let options: TransitionOptions;\n    if (typeof a === \"object\" && a !== null) {\n      options = a;\n    } else {\n      options = {};\n      if (typeof a === \"number\") {\n        options.duration = a;\n        if (typeof b === \"number\") {\n          options.delay = b;\n        }\n      }\n    }\n\n    return (this._next = new Transition(this._owner, options));\n  }\n\n  duration(duration: number) {\n    this._duration = duration;\n    return this;\n  }\n\n  delay(delay: number) {\n    this._delay = delay;\n    return this;\n  }\n\n  ease(easing: EasingFunctionName | EasingFunction) {\n    this._easing = Easing.get(easing);\n    return this;\n  }\n\n  done(fn: TransitionEndListener) {\n    this._ending.push(fn);\n    return this;\n  }\n\n  hide() {\n    this._ending.push(function () {\n      this.hide();\n    });\n    this._hide = true;\n    return this;\n  }\n\n  remove() {\n    this._ending.push(function () {\n      this.remove();\n    });\n    this._remove = true;\n    return this;\n  }\n\n  pin(key: string, value: any): this;\n  pin(obj: object): this;\n  pin(key: string): any;\n  pin(a?, b?) {\n    if (typeof a === \"object\") {\n      for (const attr in a) {\n        pinning(this._owner, this._end, attr, a[attr]);\n      }\n    } else if (typeof b !== \"undefined\") {\n      pinning(this._owner, this._end, a, b);\n    }\n    return this;\n  }\n\n  /**\n   *  @hidden @deprecated Use .done(fn) instead.\n   */\n  then(fn: TransitionEndListener) {\n    this.done(fn);\n    return this;\n  }\n\n  /**\n   *  @hidden @deprecated this doesn't do anything anymore, call transition on the component instead.\n   */\n  clear(forward: boolean) {\n    return this;\n  }\n\n  size(w: number, h: number) {\n    // Pin shortcut, used by Transition and Component\n    this.pin(\"width\", w);\n    this.pin(\"height\", h);\n    return this;\n  }\n\n  width(w: number): this;\n  width(): number;\n  width(w?: number) {\n    // Pin shortcut, used by Transition and Component\n    if (typeof w === \"undefined\") {\n      return this.pin(\"width\");\n    }\n    this.pin(\"width\", w);\n    return this;\n  }\n\n  height(h: number): this;\n  height(): number;\n  height(h?: number) {\n    // Pin shortcut, used by Transition and Component\n    if (typeof h === \"undefined\") {\n      return this.pin(\"height\");\n    }\n    this.pin(\"height\", h);\n    return this;\n  }\n\n  offset(value: Vec2Value): this;\n  offset(x: number, y: number): this;\n  offset(a: number | Vec2Value, b?: number) {\n    // Pin shortcut, used by Transition and Component\n    if (typeof a === \"object\") {\n      b = a.y;\n      a = a.x;\n    }\n    this.pin(\"offsetX\", a);\n    this.pin(\"offsetY\", b);\n    return this;\n  }\n\n  rotate(a: number) {\n    // Pin shortcut, used by Transition and Component\n    this.pin(\"rotation\", a);\n    return this;\n  }\n\n  skew(value: Vec2Value): this;\n  skew(x: number, y: number): this;\n  skew(a: number | Vec2Value, b?: number) {\n    // Pin shortcut, used by Transition and Component\n    if (typeof a === \"object\") {\n      b = a.y;\n      a = a.x;\n    } else if (typeof b === \"undefined\") {\n      b = a;\n    }\n    this.pin(\"skewX\", a);\n    this.pin(\"skewY\", b);\n    return this;\n  }\n\n  scale(value: Vec2Value): this;\n  scale(x: number, y: number): this;\n  scale(s: number): this;\n  scale(a: number | Vec2Value, b?: number) {\n    // Pin shortcut, used by Transition and Component\n    if (typeof a === \"object\") {\n      b = a.y;\n      a = a.x;\n    } else if (typeof b === \"undefined\") {\n      b = a;\n    }\n    this.pin(\"scaleX\", a);\n    this.pin(\"scaleY\", b);\n    return this;\n  }\n\n  alpha(a: number, ta?: number) {\n    // Pin shortcut, used by Transition and Component\n    this.pin(\"alpha\", a);\n    if (typeof ta !== \"undefined\") {\n      this.pin(\"textureAlpha\", ta);\n    }\n    return this;\n  }\n}\n\n/** @internal */\nfunction pinning(component: Component, map: object, key: string, value: number) {\n  if (typeof component.pin(key) === \"number\") {\n    map[key] = value;\n  } else if (typeof component.pin(key + \"X\") === \"number\" && typeof component.pin(key + \"Y\") === \"number\") {\n    map[key + \"X\"] = value;\n    map[key + \"Y\"] = value;\n  }\n}\n", "import stats from \"../common/stats\";\nimport { Vec2Value } from \"../common/matrix\";\nimport { uid } from \"../common/uid\";\nimport { getDevicePixelRatio } from \"../common/browser\";\n\nimport { Pin, Pinned, FitMode } from \"./pin\";\nimport { Transition, TransitionOptions } from \"./transition\";\n\n// todo: why there are two iids (other in pin)?\n/** @internal */\nlet iid = 0;\nstats.create = 0;\n\n/** @internal */\nfunction assertType<T>(obj: T): T {\n  if (obj && obj instanceof Component) {\n    return obj;\n  }\n  throw \"Invalid component: \" + obj;\n}\n\ninterface ComponentVisitor<D> {\n  reverse?: boolean;\n  visible?: boolean;\n  start?: (component: Component, data?: D) => boolean | void;\n  end?: (component: Component, data?: D) => boolean | void;\n}\n\nexport type ComponentTickListener<T> = (\n  this: T,\n  elapsed: number,\n  now: number,\n  last: number,\n) => boolean | void;\n\nexport type ComponentEventListener<T> = (this: T, ...args: any[]) => void;\n\n/** @hidden @deprecated Use component() */\nexport function create() {\n  return component();\n}\n\n/** @hidden @deprecated Use maximize() */\nexport function layer() {\n  return maximize();\n}\n\n/** @hidden @deprecated Use minimize() */\nexport function box() {\n  return minimize();\n}\n\n// todo: create a new subclass called layout, and make component abstract\n// discussion: in some cases sprites are used as parent component, like a window\n\n/** @hidden @deprecated */\nexport function layout() {\n  return component();\n}\n\nexport function component() {\n  return new Component();\n}\n\nexport function row(align: number) {\n  return new Component().row(align).label(\"Row\");\n}\n\nexport function column(align: number) {\n  return new Component().column(align).label(\"Column\");\n}\n\nexport function minimize() {\n  return new Component().minimize().label(\"Minimize\");\n}\n\nexport function maximize() {\n  return new Component().maximize().label(\"Maximize\");\n}\n\n// TODO: do not clear next/prev/parent on remove (why?)\n\n// There are three sets of core functions:\n// - tree model manipulation functions\n// - frame loop and rendering\n// - events handling\n\nexport class Component implements Pinned {\n  /** @internal */ uid = \"component:\" + uid();\n\n  /** @internal */ _label = \"\";\n\n  /** @internal */ _parent: Component | null = null;\n  /** @internal */ _next: Component | null = null;\n  /** @internal */ _prev: Component | null = null;\n\n  /** @internal */ _first: Component | null = null;\n  /** @internal */ _last: Component | null = null;\n\n  /** @internal */ _visible = true;\n\n  // this is computed on every render, and used by children\n  /** @internal */ _alpha: number = 1;\n\n  /** @internal */ _padding: number = 0;\n  /** @internal */ _spacing: number = 0;\n\n  /** @internal */ _pin = new Pin(this);\n\n  /** @internal */ _ts_pin: number;\n  /** @internal */ _ts_parent: number;\n  /** @internal */ _ts_children: number;\n  /** @internal */ _ts_touch: number;\n\n  // todo: don't need to check if these fields are initialized anymore\n  /** @internal */ _listeners: Record<string, ComponentEventListener<Component>[]> = {};\n  /** @internal */ _attrs: Record<string, any> = {};\n  /** @internal */ _flags: Record<string, any> = {};\n  /** @internal */ _transitions: Transition[] = [];\n\n  /** @internal */ _tickBefore: ComponentTickListener<any>[] = [];\n  /** @internal */ _tickAfter: ComponentTickListener<any>[] = [];\n\n  /** @internal */ _layoutTicker?: () => void;\n\n  // todo: remove this\n  MAX_ELAPSE = Infinity;\n\n  /** @internal */ _mo_seq: number;\n  /** @internal */ _mo_seqAlign: number;\n  /** @internal */ _mo_box: number;\n\n  constructor() {\n    stats.create++;\n    if (this instanceof Component) {\n      this.label(this.constructor.name);\n    }\n  }\n\n  matrix(relative = false) {\n    if (relative === true) {\n      return this._pin.relativeMatrix();\n    }\n    return this._pin.absoluteMatrix();\n  }\n\n  /** @hidden @deprecated */\n  getPixelRatio() {\n    // todo: remove this function\n    const m = this._parent?.matrix();\n    const pixelRatio = !m ? 1 : Math.max(Math.abs(m.a), Math.abs(m.b)) / getDevicePixelRatio();\n    return pixelRatio;\n  }\n\n  /** @hidden This is not accurate before first tick */\n  getDevicePixelRatio() {\n    // todo: parent matrix is not available in the first call\n    const parentMatrix = this._parent?.matrix();\n    const pixelRatio = !parentMatrix\n      ? 1\n      : Math.max(Math.abs(parentMatrix.a), Math.abs(parentMatrix.b));\n    return pixelRatio;\n  }\n\n  /** @hidden This is not accurate before first tick */\n  getLogicalPixelRatio() {\n    return this.getDevicePixelRatio() / getDevicePixelRatio();\n  }\n\n  pin(key: string): any;\n  pin(key: string, value: any): this;\n  pin(obj: object): this;\n  pin(): Pin;\n  pin(a?: object | string, b?: any) {\n    if (typeof a === \"object\") {\n      this._pin.set(a);\n      return this;\n    } else if (typeof a === \"string\") {\n      if (typeof b === \"undefined\") {\n        return this._pin.get(a);\n      } else {\n        this._pin.set(a, b);\n        return this;\n      }\n    } else if (typeof a === \"undefined\") {\n      return this._pin;\n    }\n  }\n\n  fit(width: number, height: number, mode?: FitMode): this;\n  fit(fit: object): this;\n  fit(a, b?, c?) {\n    if (typeof a === \"object\") {\n      c = b;\n      b = a.y;\n      a = a.x;\n    }\n    this._pin.fit(a, b, c);\n    return this;\n  }\n\n  /** @hidden @deprecated Use fit */\n  scaleTo(a, b?, c?): this {\n    return this.fit(a, b, c);\n  }\n\n  toString() {\n    return \"[\" + this._label + \"]\";\n  }\n\n  /** @hidden @deprecated Use label() */\n  id(): string;\n  /** @hidden @deprecated Use label() */\n  id(label: string): this;\n  /** @hidden @deprecated Use label() */\n  id(label?: string) {\n    if (typeof label === \"undefined\") {\n      return this._label;\n    }\n    this._label = label;\n    return this;\n  }\n\n  label(): string;\n  label(label: string): this;\n  label(label?: string) {\n    if (typeof label === \"undefined\") {\n      return this._label;\n    }\n    this._label = label;\n    return this;\n  }\n\n  attr(name: string, value: any): this;\n  attr(name: string): any;\n  attr(name: string, value?: any) {\n    if (typeof value === \"undefined\") {\n      return this._attrs !== null ? this._attrs[name] : undefined;\n    }\n    (this._attrs !== null ? this._attrs : (this._attrs = {}))[name] = value;\n    return this;\n  }\n\n  visible(visible: boolean): this;\n  visible(): boolean;\n  visible(visible?: boolean) {\n    if (typeof visible === \"undefined\") {\n      return this._visible;\n    }\n    this._visible = visible;\n    this._parent && (this._parent._ts_children = ++iid);\n    this._ts_pin = ++iid;\n    this.touch();\n    return this;\n  }\n\n  hide() {\n    this.visible(false);\n    return this;\n  }\n\n  show() {\n    this.visible(true);\n    return this;\n  }\n\n  parent() {\n    return this._parent;\n  }\n\n  next(visible?: boolean) {\n    let next = this._next;\n    while (next && visible && !next._visible) {\n      next = next._next;\n    }\n    return next;\n  }\n\n  prev(visible?: boolean) {\n    let prev = this._prev;\n    while (prev && visible && !prev._visible) {\n      prev = prev._prev;\n    }\n    return prev;\n  }\n\n  first(visible?: boolean) {\n    let next = this._first;\n    while (next && visible && !next._visible) {\n      next = next._next;\n    }\n    return next;\n  }\n\n  last(visible?: boolean) {\n    let prev = this._last;\n    while (prev && visible && !prev._visible) {\n      prev = prev._prev;\n    }\n    return prev;\n  }\n\n  visit<P>(visitor: ComponentVisitor<P>, payload?: P) {\n    const reverse = visitor.reverse;\n    const visible = visitor.visible;\n    if (visitor.start && visitor.start(this, payload)) {\n      return;\n    }\n    let child: Component;\n    let next = reverse ? this.last(visible) : this.first(visible);\n    while ((child = next)) {\n      next = reverse ? child.prev(visible) : child.next(visible);\n      if (child.visit(visitor, payload)) {\n        return true;\n      }\n    }\n    return visitor.end && visitor.end(this, payload);\n  }\n\n  append(...child: Component[]): this;\n  append(child: Component[]): this;\n  append(child: Component | Component[], more?: Component) {\n    if (Array.isArray(child)) {\n      for (let i = 0; i < child.length; i++) {\n        Component.append(this, child[i]);\n      }\n    } else if (typeof more !== \"undefined\") {\n      // deprecated\n      for (let i = 0; i < arguments.length; i++) {\n        Component.append(this, arguments[i]);\n      }\n    } else if (typeof child !== \"undefined\") Component.append(this, child);\n\n    return this;\n  }\n\n  prepend(...child: Component[]): this;\n  prepend(child: Component[]): this;\n  prepend(child: Component | Component[], more?: Component) {\n    if (Array.isArray(child)) {\n      for (let i = child.length - 1; i >= 0; i--) {\n        Component.prepend(this, child[i]);\n      }\n    } else if (typeof more !== \"undefined\") {\n      // deprecated\n      for (let i = arguments.length - 1; i >= 0; i--) {\n        Component.prepend(this, arguments[i]);\n      }\n    } else if (typeof child !== \"undefined\") Component.prepend(this, child);\n\n    return this;\n  }\n\n  appendTo(parent: Component) {\n    Component.append(parent, this);\n    return this;\n  }\n\n  prependTo(parent: Component) {\n    Component.prepend(parent, this);\n    return this;\n  }\n\n  insertNext(sibling: Component, more?: Component) {\n    if (Array.isArray(sibling)) {\n      for (let i = 0; i < sibling.length; i++) {\n        Component.insertAfter(sibling[i], this);\n      }\n    } else if (typeof more !== \"undefined\") {\n      // deprecated\n      for (let i = 0; i < arguments.length; i++) {\n        Component.insertAfter(arguments[i], this);\n      }\n    } else if (typeof sibling !== \"undefined\") {\n      Component.insertAfter(sibling, this);\n    }\n\n    return this;\n  }\n\n  insertPrev(sibling: Component, more?: Component) {\n    if (Array.isArray(sibling)) {\n      for (let i = sibling.length - 1; i >= 0; i--) {\n        Component.insertBefore(sibling[i], this);\n      }\n    } else if (typeof more !== \"undefined\") {\n      // deprecated\n      for (let i = arguments.length - 1; i >= 0; i--) {\n        Component.insertBefore(arguments[i], this);\n      }\n    } else if (typeof sibling !== \"undefined\") {\n      Component.insertBefore(sibling, this);\n    }\n\n    return this;\n  }\n\n  insertAfter(prev: Component) {\n    Component.insertAfter(this, prev);\n    return this;\n  }\n\n  insertBefore(next: Component) {\n    Component.insertBefore(this, next);\n    return this;\n  }\n\n  /** @internal */\n  static append(parent: Component, child: Component) {\n    assertType(child);\n    assertType(parent);\n\n    child.remove();\n\n    if (parent._last) {\n      parent._last._next = child;\n      child._prev = parent._last;\n    }\n\n    child._parent = parent;\n    parent._last = child;\n\n    if (!parent._first) {\n      parent._first = child;\n    }\n\n    child._parent._flag(child, true);\n\n    child._ts_parent = ++iid;\n    parent._ts_children = ++iid;\n    parent.touch();\n  }\n\n  /** @internal */\n  static prepend(parent: Component, child: Component) {\n    assertType(child);\n    assertType(parent);\n\n    child.remove();\n\n    if (parent._first) {\n      parent._first._prev = child;\n      child._next = parent._first;\n    }\n\n    child._parent = parent;\n    parent._first = child;\n\n    if (!parent._last) {\n      parent._last = child;\n    }\n\n    child._parent._flag(child, true);\n\n    child._ts_parent = ++iid;\n    parent._ts_children = ++iid;\n    parent.touch();\n  }\n\n  /** @internal */\n  static insertBefore(self: Component, next: Component) {\n    assertType(self);\n    assertType(next);\n\n    self.remove();\n\n    const parent = next._parent;\n    const prev = next._prev;\n\n    if (!parent) {\n      return;\n    }\n\n    next._prev = self;\n    // todo:\n    (prev && (prev._next = self)) || (parent && (parent._first = self));\n\n    self._parent = parent;\n    self._prev = prev;\n    self._next = next;\n\n    self._parent._flag(self, true);\n\n    self._ts_parent = ++iid;\n    self.touch();\n  }\n\n  /** @internal */\n  static insertAfter(self: Component, prev: Component) {\n    assertType(self);\n    assertType(prev);\n\n    self.remove();\n\n    const parent = prev._parent;\n    const next = prev._next;\n\n    if (!parent) {\n      return;\n    }\n\n    prev._next = self;\n    // todo:\n    (next && (next._prev = self)) || (parent && (parent._last = self));\n\n    self._parent = parent;\n    self._prev = prev;\n    self._next = next;\n\n    self._parent._flag(self, true);\n\n    self._ts_parent = ++iid;\n    self.touch();\n  }\n\n  remove(child?: Component, more?: any) {\n    if (typeof child !== \"undefined\") {\n      if (Array.isArray(child)) {\n        for (let i = 0; i < child.length; i++) {\n          assertType(child[i]).remove();\n        }\n      } else if (typeof more !== \"undefined\") {\n        for (let i = 0; i < arguments.length; i++) {\n          assertType(arguments[i]).remove();\n        }\n      } else {\n        assertType(child).remove();\n      }\n      return this;\n    }\n\n    if (this._prev) {\n      this._prev._next = this._next;\n    }\n    if (this._next) {\n      this._next._prev = this._prev;\n    }\n\n    if (this._parent) {\n      if (this._parent._first === this) {\n        this._parent._first = this._next;\n      }\n      if (this._parent._last === this) {\n        this._parent._last = this._prev;\n      }\n\n      this._parent._flag(this, false);\n\n      this._parent._ts_children = ++iid;\n      this._parent.touch();\n    }\n\n    this._prev = this._next = this._parent = null;\n    this._ts_parent = ++iid;\n    // this._parent.touch();\n    return this;\n  }\n\n  empty() {\n    let child: Component | null = null;\n    let next = this._first;\n    while ((child = next)) {\n      next = child._next;\n      child._prev = child._next = child._parent = null;\n\n      this._flag(child, false);\n    }\n\n    this._first = this._last = null;\n\n    this._ts_children = ++iid;\n    this.touch();\n    return this;\n  }\n\n  touch() {\n    this._ts_touch = ++iid;\n    this._parent && this._parent.touch();\n    return this;\n  }\n\n  /** @internal */\n  _flag(child: Component, value: boolean): Component;\n  /** @internal */\n  _flag(key: string): boolean;\n  /** @internal */\n  _flag(key: string, value: boolean): Component;\n  /** @internal Deep flag, used for optimizing event distribution. */\n  _flag(key: string | Component, value?: boolean) {\n    if (typeof value === \"undefined\") {\n      return (this._flags !== null && this._flags[key as string]) || 0;\n    }\n    if (typeof key === \"string\") {\n      if (value) {\n        this._flags = this._flags || {};\n        if (!this._flags[key] && this._parent) {\n          this._parent._flag(key, true);\n        }\n        this._flags[key] = (this._flags[key] || 0) + 1;\n      } else if (this._flags && this._flags[key] > 0) {\n        if (this._flags[key] == 1 && this._parent) {\n          this._parent._flag(key, false);\n        }\n        this._flags[key] = this._flags[key] - 1;\n      }\n    }\n    if (typeof key === \"object\") {\n      if (key._flags) {\n        for (const type in key._flags) {\n          if (key._flags[type] > 0) {\n            this._flag(type, value);\n          }\n        }\n      }\n    }\n    return this;\n  }\n\n  /** @internal */\n  hitTest(hit: Vec2Value) {\n    const width = this._pin._width;\n    const height = this._pin._height;\n    return hit.x >= 0 && hit.x <= width && hit.y >= 0 && hit.y <= height;\n  }\n\n  /** @hidden */\n  prerender() {\n    if (!this._visible) {\n      return;\n    }\n\n    this.prerenderTexture();\n\n    let child: Component;\n    let next = this._first;\n    while ((child = next)) {\n      next = child._next;\n      child.prerender();\n    }\n  }\n\n  /** @hidden */\n  prerenderTexture() {\n    // to be implemented by subclasses if needed\n  }\n\n  /** @hidden */\n  private renderedBefore = false;\n\n  /** @hidden */\n  render(context: CanvasRenderingContext2D) {\n    if (!this._visible) {\n      return;\n    }\n    stats.component++;\n\n    const m = this.matrix();\n    context.setTransform(m.a, m.b, m.c, m.d, m.e, m.f);\n\n    // move this elsewhere!\n    this._alpha = this._pin._alpha * (this._parent ? this._parent._alpha : 1);\n    const alpha = this._pin._textureAlpha * this._alpha;\n\n    if (context.globalAlpha != alpha) {\n      context.globalAlpha = alpha;\n    }\n\n    if (!this.renderedBefore) {\n      // todo: because getDevicePixelRatio is not accurate before first tick\n      this.prerenderTexture();\n    }\n    this.renderedBefore = true;\n\n    this.renderTexture(context);\n\n    if (context.globalAlpha != this._alpha) {\n      context.globalAlpha = this._alpha;\n    }\n\n    let child: Component;\n    let next = this._first;\n    while ((child = next)) {\n      next = child._next;\n      child.render(context);\n    }\n  }\n\n  /** @hidden */\n  renderTexture(context: CanvasRenderingContext2D) {\n    // to be implemented by subclasses if needed\n  }\n\n  /** @internal */\n  _tick(elapsed: number, now: number, last: number) {\n    if (!this._visible) {\n      return;\n    }\n\n    if (elapsed > this.MAX_ELAPSE) {\n      elapsed = this.MAX_ELAPSE;\n    }\n\n    let ticked = false;\n\n    if (this._tickBefore !== null) {\n      for (let i = 0; i < this._tickBefore.length; i++) {\n        stats.tick++;\n        const tickFn = this._tickBefore[i];\n        ticked = tickFn.call(this, elapsed, now, last) === true || ticked;\n      }\n    }\n\n    let child: Component | null;\n    let next = this._first;\n    while ((child = next)) {\n      next = child._next;\n      if (child._flag(\"_tick\")) {\n        ticked = child._tick(elapsed, now, last) === true ? true : ticked;\n      }\n    }\n\n    if (this._tickAfter !== null) {\n      for (let i = 0; i < this._tickAfter.length; i++) {\n        stats.tick++;\n        const tickFn = this._tickAfter[i];\n        ticked = tickFn.call(this, elapsed, now, last) === true || ticked;\n      }\n    }\n\n    return ticked;\n  }\n\n  tick(callback: ComponentTickListener<this>, before = false) {\n    if (typeof callback !== \"function\") {\n      return;\n    }\n    if (before) {\n      if (this._tickBefore === null) {\n        this._tickBefore = [];\n      }\n      this._tickBefore.push(callback);\n    } else {\n      if (this._tickAfter === null) {\n        this._tickAfter = [];\n      }\n      this._tickAfter.push(callback);\n    }\n    const hasTickListener = this._tickAfter?.length > 0 || this._tickBefore?.length > 0;\n    this._flag(\"_tick\", hasTickListener);\n  }\n\n  untick(callback: ComponentTickListener<this>) {\n    if (typeof callback !== \"function\") {\n      return;\n    }\n    let i;\n    if (this._tickBefore !== null && (i = this._tickBefore.indexOf(callback)) >= 0) {\n      this._tickBefore.splice(i, 1);\n    }\n    if (this._tickAfter !== null && (i = this._tickAfter.indexOf(callback)) >= 0) {\n      this._tickAfter.splice(i, 1);\n    }\n  }\n\n  timeout(callback: () => any, time: number) {\n    this.setTimeout(callback, time);\n  }\n\n  setTimeout(callback: () => any, time: number) {\n    function timer(t: number) {\n      if ((time -= t) < 0) {\n        this.untick(timer);\n        callback.call(this);\n      } else {\n        return true;\n      }\n    }\n    this.tick(timer);\n    return timer;\n  }\n\n  clearTimeout(timer: ComponentTickListener<this>) {\n    this.untick(timer);\n  }\n\n  on(types: string, listener: ComponentEventListener<this>): this;\n  /** @hidden @deprecated @internal */\n  on(types: string[], listener: ComponentEventListener<this>): this;\n  on(type: string | string[], listener: ComponentEventListener<this>) {\n    if (!type || !type.length || typeof listener !== \"function\") {\n      return this;\n    }\n    if (typeof type !== \"string\" && typeof type.join === \"function\") {\n      // deprecated arguments, type is array\n      for (let i = 0; i < type.length; i++) {\n        this.on(type[i], listener);\n      }\n    } else if (typeof type === \"string\" && type.indexOf(\" \") > -1) {\n      // deprecated arguments, type is spaced string\n      type = type.match(/\\S+/g);\n      for (let i = 0; i < type.length; i++) {\n        this._on(type[i], listener);\n      }\n    } else if (typeof type === \"string\") {\n      this._on(type, listener);\n    } else {\n      // invalid\n    }\n    return this;\n  }\n\n  /** @internal */\n  _on(type: string, listener: ComponentEventListener<this>) {\n    if (typeof type !== \"string\" && typeof listener !== \"function\") {\n      return;\n    }\n    this._listeners[type] = this._listeners[type] || [];\n    this._listeners[type].push(listener);\n    // todo: maybe recompute/set exact value?\n    this._flag(type, true);\n  }\n\n  off(types: string, listener: ComponentEventListener<this>): this;\n  /** @hidden @deprecated @internal */\n  off(types: string[], listener: ComponentEventListener<this>): this;\n  off(type: string | string[], listener: ComponentEventListener<this>) {\n    if (!type || !type.length || typeof listener !== \"function\") {\n      return this;\n    }\n    if (typeof type !== \"string\" && typeof type.join === \"function\") {\n      // deprecated arguments, type is array\n      for (let i = 0; i < type.length; i++) {\n        this.off(type[i], listener);\n      }\n    } else if (typeof type === \"string\" && type.indexOf(\" \") > -1) {\n      // deprecated arguments, type is spaced string\n      type = type.match(/\\S+/g);\n      for (let i = 0; i < type.length; i++) {\n        this._off(type[i], listener);\n      }\n    } else if (typeof type === \"string\") {\n      this._off(type, listener);\n    } else {\n      // invalid\n    }\n    return this;\n  }\n\n  /** @internal */\n  _off(type: string, listener: ComponentEventListener<this>) {\n    if (typeof type !== \"string\" && typeof listener !== \"function\") {\n      return;\n    }\n    const listeners = this._listeners[type];\n    if (!listeners || !listeners.length) {\n      return;\n    }\n    const index = listeners.indexOf(listener);\n    if (index >= 0) {\n      listeners.splice(index, 1);\n      // if (!listeners.length) {\n      //   delete this._listeners[type];\n      // }\n      // todo: maybe recompute/set exact value?\n      this._flag(type, false);\n    }\n  }\n\n  listeners(type: string) {\n    return this._listeners[type];\n  }\n\n  publish(name: string, args?: any) {\n    const listeners = this.listeners(name);\n    if (!listeners || !listeners.length) {\n      return 0;\n    }\n    for (let l = 0; l < listeners.length; l++) {\n      listeners[l].apply(this, args);\n    }\n    return listeners.length;\n  }\n\n  /** @hidden @deprecated @internal */\n  trigger(name: string, args?: any) {\n    this.publish(name, args);\n    return this;\n  }\n\n  size(w: number, h: number) {\n    // Pin shortcut, used by Transition and Component\n    this.pin(\"width\", w);\n    this.pin(\"height\", h);\n    return this;\n  }\n\n  width(w: number): this;\n  width(): number;\n  width(w?: number) {\n    // Pin shortcut, used by Transition and Component\n    if (typeof w === \"undefined\") {\n      return this.pin(\"width\");\n    }\n    this.pin(\"width\", w);\n    return this;\n  }\n\n  height(h: number): this;\n  height(): number;\n  height(h?: number) {\n    // Pin shortcut, used by Transition and Component\n    if (typeof h === \"undefined\") {\n      return this.pin(\"height\");\n    }\n    this.pin(\"height\", h);\n    return this;\n  }\n\n  offset(value: Vec2Value): this;\n  offset(x: number, y: number): this;\n  offset(a?: Vec2Value | number, b?: number) {\n    // Pin shortcut, used by Transition and Component\n    if (typeof a === \"object\") {\n      b = a.y;\n      a = a.x;\n    }\n    this.pin(\"offsetX\", a);\n    this.pin(\"offsetY\", b);\n    return this;\n  }\n\n  rotate(a: number) {\n    // Pin shortcut, used by Transition and Component\n    this.pin(\"rotation\", a);\n    return this;\n  }\n\n  skew(value: Vec2Value): this;\n  skew(x: number, y: number): this;\n  skew(a?: Vec2Value | number, b?: number) {\n    // Pin shortcut, used by Transition and Component\n    if (typeof a === \"object\") {\n      b = a.y;\n      a = a.x;\n    } else if (typeof b === \"undefined\") b = a;\n    this.pin(\"skewX\", a);\n    this.pin(\"skewY\", b);\n    return this;\n  }\n\n  scale(value: Vec2Value): this;\n  scale(x: number, y: number): this;\n  scale(s: number): this;\n  scale(a?: Vec2Value | number, b?: number) {\n    // Pin shortcut, used by Transition and Component\n    if (typeof a === \"object\") {\n      b = a.y;\n      a = a.x;\n    } else if (typeof b === \"undefined\") b = a;\n    this.pin(\"scaleX\", a);\n    this.pin(\"scaleY\", b);\n    return this;\n  }\n\n  alpha(a: number, ta?: number) {\n    // Pin shortcut, used by Transition and Component\n    this.pin(\"alpha\", a);\n    if (typeof ta !== \"undefined\") {\n      this.pin(\"textureAlpha\", ta);\n    }\n    return this;\n  }\n\n  tween(opts?: TransitionOptions): Transition;\n  tween(duration?: number, delay?: number, append?: boolean): Transition;\n  tween(a?: object | number, b?: number, c?: boolean) {\n    let options: TransitionOptions;\n    if (typeof a === \"object\" && a !== null) {\n      options = a;\n    } else {\n      options = {};\n      if (typeof a === \"number\") {\n        options.duration = a;\n        if (typeof b === \"number\") {\n          options.delay = b;\n          if (typeof c === \"boolean\") {\n            options.append = c;\n          }\n        } else if (typeof b === \"boolean\") {\n          options.append = b;\n        }\n      } else if (typeof a === \"boolean\") {\n        options.append = a;\n      }\n    }\n\n    if (!this._transitionTickInitied) {\n      this.tick(this._transitionTick, true);\n      this._transitionTickInitied = true;\n    }\n\n    this.touch();\n\n    // todo: what is the expected default behavior?\n    if (!options.append) {\n      this._transitions.length = 0;\n    }\n\n    const transition = new Transition(this, options);\n    this._transitions.push(transition);\n    return transition;\n  }\n\n  /** @internal */ _transitionTickInitied = false;\n  /** @internal */ _transitionTickLastTime = 0;\n  /** @internal */\n  _transitionTick = (elapsed: number, now: number, last: number) => {\n    if (!this._transitions.length) {\n      return false;\n    }\n\n    // ignore untracked tick\n    const ignore = this._transitionTickLastTime !== last;\n    this._transitionTickLastTime = now;\n    if (ignore) {\n      return true;\n    }\n\n    const head = this._transitions[0];\n\n    const ended = head.tick(this, elapsed, now, last);\n\n    // todo: move this logic to TransitionQueue\n    if (ended) {\n      if (head === this._transitions[0]) {\n        this._transitions.shift();\n      }\n      const next = head.finish();\n      if (next) {\n        this._transitions.unshift(next);\n      }\n    }\n\n    return true;\n  };\n\n  row(align: number) {\n    this.align(\"row\", align);\n    return this;\n  }\n\n  column(align: number) {\n    this.align(\"column\", align);\n    return this;\n  }\n\n  align(type: \"row\" | \"column\", align: number) {\n    this._padding = this._padding;\n    this._spacing = this._spacing;\n\n    this._layoutTicker && this.untick(this._layoutTicker);\n    this.tick(\n      (this._layoutTicker = () => {\n        if (this._mo_seq == this._ts_touch) {\n          return;\n        }\n        this._mo_seq = this._ts_touch;\n\n        const alignChildren = this._mo_seqAlign != this._ts_children;\n        this._mo_seqAlign = this._ts_children;\n\n        let width = 0;\n        let height = 0;\n\n        let child: Component;\n        let next = this.first(true);\n        let first = true;\n        while ((child = next)) {\n          next = child.next(true);\n\n          child.matrix(true);\n          const w = child.pin(\"boxWidth\");\n          const h = child.pin(\"boxHeight\");\n\n          if (type == \"column\") {\n            !first && (height += this._spacing);\n            child.pin(\"offsetY\") != height && child.pin(\"offsetY\", height);\n            width = Math.max(width, w);\n            height = height + h;\n            alignChildren && child.pin(\"alignX\", align);\n          } else if (type == \"row\") {\n            !first && (width += this._spacing);\n            child.pin(\"offsetX\") != width && child.pin(\"offsetX\", width);\n            width = width + w;\n            height = Math.max(height, h);\n            alignChildren && child.pin(\"alignY\", align);\n          }\n          first = false;\n        }\n        width += 2 * this._padding;\n        height += 2 * this._padding;\n        this.pin(\"width\") != width && this.pin(\"width\", width);\n        this.pin(\"height\") != height && this.pin(\"height\", height);\n      }),\n    );\n    return this;\n  }\n\n  /** @hidden @deprecated Use minimize() */\n  box() {\n    return this.minimize();\n  }\n\n  /** @hidden @deprecated Use minimize() */\n  layer() {\n    return this.maximize();\n  }\n\n  /**\n   * Set size to match largest child size.\n   */\n  minimize() {\n    this._padding = this._padding;\n\n    this._layoutTicker && this.untick(this._layoutTicker);\n    this.tick(\n      (this._layoutTicker = () => {\n        if (this._mo_box == this._ts_touch) {\n          return;\n        }\n        this._mo_box = this._ts_touch;\n\n        let width = 0;\n        let height = 0;\n        let child: Component;\n        let next = this.first(true);\n        while ((child = next)) {\n          next = child.next(true);\n          child.matrix(true);\n          const w = child.pin(\"boxWidth\");\n          const h = child.pin(\"boxHeight\");\n          width = Math.max(width, w);\n          height = Math.max(height, h);\n        }\n        width += 2 * this._padding;\n        height += 2 * this._padding;\n        this.pin(\"width\") != width && this.pin(\"width\", width);\n        this.pin(\"height\") != height && this.pin(\"height\", height);\n      }),\n    );\n    return this;\n  }\n\n  /**\n   * Set size to match parent size.\n   */\n  maximize() {\n    this._layoutTicker && this.untick(this._layoutTicker);\n    this.tick(\n      (this._layoutTicker = () => {\n        const parent = this.parent();\n        if (parent) {\n          const width = parent.pin(\"width\");\n          if (this.pin(\"width\") != width) {\n            this.pin(\"width\", width);\n          }\n          const height = parent.pin(\"height\");\n          if (this.pin(\"height\") != height) {\n            this.pin(\"height\", height);\n          }\n        }\n      }),\n      true,\n    );\n    return this;\n  }\n\n  // TODO: move padding to pin\n  /**\n   * Set cell spacing for layout.\n   */\n  padding(pad: number) {\n    this._padding = pad;\n    return this;\n  }\n\n  /**\n   * Set cell spacing for row and column layout.\n   */\n  spacing(space: number) {\n    this._spacing = space;\n    return this;\n  }\n}\n\n/** @hidden @deprecated Node is renamed to Component */\nexport { Component as Node };\n", "import {\n  PipeTexture,\n  texture,\n  Texture,\n  TexturePrerenderContext,\n  TextureSelectionInput,\n} from \"../texture\";\nimport { ResizableTexture } from \"../texture/resizable\";\n\nimport { Component } from \"./component\";\n\nexport function sprite(frame?: TextureSelectionInput) {\n  const sprite = new Sprite();\n  frame && sprite.texture(frame);\n  return sprite;\n}\n\nexport class Sprite extends Component {\n  /** @internal */ _texture: Texture | null = null;\n\n  /** @internal */ _image: Texture | null = null;\n  /** @internal */ _tiled: boolean = false;\n  /** @internal */ _stretched: boolean = false;\n\n  constructor() {\n    super();\n    this.label(\"Sprite\");\n  }\n\n  texture(frame: TextureSelectionInput) {\n    this._image = texture(frame).one();\n    if (this._image) {\n      this.pin(\"width\", this._image.getWidth());\n      this.pin(\"height\", this._image.getHeight());\n\n      // todo: could we chain textures in a way that doesn't require rebuilding the chain?\n      if (this._tiled) {\n        this._texture = new ResizableTexture(this._image, \"tile\");\n      } else if (this._stretched) {\n        this._texture = new ResizableTexture(this._image, \"stretch\");\n      } else {\n        this._texture = new PipeTexture(this._image);\n      }\n    } else {\n      this.pin(\"width\", 0);\n      this.pin(\"height\", 0);\n      this._texture = null;\n    }\n    return this;\n  }\n\n  /** @deprecated */\n  image(frame: TextureSelectionInput) {\n    return this.texture(frame);\n  }\n\n  tile(inner = false) {\n    this._tiled = true;\n    const texture = new ResizableTexture(this._image, \"tile\");\n    this._texture = texture;\n    return this;\n  }\n\n  stretch(inner = false) {\n    this._stretched = true;\n    const texture = new ResizableTexture(this._image, \"stretch\");\n    this._texture = texture;\n    return this;\n  }\n\n  /** @internal */\n  private prerenderContext = {} as TexturePrerenderContext;\n\n  /** @hidden */\n  prerenderTexture(): void {\n    if (!this._image) return;\n    const pixelRatio = this.getDevicePixelRatio();\n    this.prerenderContext.pixelRatio = pixelRatio;\n    const updated = this._image.prerender(this.prerenderContext);\n    if (updated === true) {\n      // should we let draw function decide to make this update?\n      const w = this._image.getWidth();\n      const h = this._image.getHeight();\n      this.size(w, h);\n    }\n  }\n\n  /** @hidden */\n  renderTexture(context: CanvasRenderingContext2D) {\n    if (!this._texture) return;\n\n    if (this._texture[\"_resizeMode\"]) {\n      this._texture.dw = this.pin(\"width\");\n      this._texture.dh = this.pin(\"height\");\n    }\n\n    this._texture.draw(context);\n  }\n}\n\n/** @hidden @deprecated */\nexport { sprite as image };\n/** @hidden @deprecated */\nexport { Sprite as Image };\n", "import { Sprite } from \"../core/sprite\";\n\nimport { ImageTexture } from \"./image\";\nimport { TexturePrerenderContext } from \"./texture\";\n\ntype CanvasTextureDrawer = (this: CanvasTexture) => void;\ntype CanvasTextureMemoizer = (this: CanvasTexture) => any;\n\n/** @hidden @deprecated */\ntype LegacyCanvasTextureDrawer = (this: CanvasTexture, context: CanvasRenderingContext2D) => void;\n/** @hidden @deprecated */\ntype LegacyCanvasSpriteMemoizer = () => any;\n\n/** @hidden @deprecated */\ntype LegacyCanvasSpriteDrawer = (ratio: number, texture: CanvasTexture, sprite: Sprite) => void;\n\n/**\n * A texture with off-screen canvas.\n */\nexport class CanvasTexture extends ImageTexture {\n  /** @internal */ _source: HTMLCanvasElement;\n  /** @internal */ _drawer?: CanvasTextureDrawer;\n  /** @internal */ _memoizer: CanvasTextureMemoizer;\n\n  /** @internal */ _lastPixelRatio = 0;\n  /** @internal */ _lastMemoKey: any;\n\n  constructor() {\n    super(document.createElement(\"canvas\"));\n  }\n\n  /**\n   * Set texture size to given width and height, and set canvas size to texture size multiply by pixelRatio.\n   */\n  setSize(destWidth: number, destHeight: number, pixelRatio = 1) {\n    this._source.width = destWidth * pixelRatio;\n    this._source.height = destHeight * pixelRatio;\n    this._pixelRatio = pixelRatio;\n  }\n\n  getContext(type = \"2d\", attributes?: any): CanvasRenderingContext2D {\n    return this._source.getContext(type, attributes) as CanvasRenderingContext2D;\n  }\n\n  /**\n   * @hidden @experimental\n   *\n   * This is the ratio of screen pixel to this canvas pixel.\n   */\n  getDevicePixelRatio() {\n    return this._lastPixelRatio;\n  }\n\n  // todo: remove in stable v1.0\n  /** @hidden @deprecated */\n  getOptimalPixelRatio() {\n    return this.getDevicePixelRatio();\n  }\n\n  setMemoizer(memoizer: CanvasTextureMemoizer) {\n    this._memoizer = memoizer;\n  }\n\n  setDrawer(drawer: CanvasTextureDrawer) {\n    this._drawer = drawer;\n  }\n\n  /** @internal */\n  prerender(context: TexturePrerenderContext): boolean {\n    const newPixelRatio = context.pixelRatio;\n    const lastPixelRatio = this._lastPixelRatio;\n\n    const pixelRationChange = lastPixelRatio / newPixelRatio;\n    const pixelRatioChanged =\n      lastPixelRatio === 0 || pixelRationChange > 1.25 || pixelRationChange < 0.8;\n\n    if (pixelRatioChanged) {\n      this._lastPixelRatio = newPixelRatio;\n    }\n\n    const newMemoKey = this._memoizer ? this._memoizer.call(this) : null;\n    const memoKeyChanged = this._lastMemoKey !== newMemoKey;\n\n    if (pixelRatioChanged || memoKeyChanged) {\n      this._lastMemoKey = newMemoKey;\n      this._lastPixelRatio = newPixelRatio;\n\n      if (typeof this._drawer === \"function\") {\n        this._drawer.call(this);\n      }\n      return true;\n    }\n  }\n\n  /** @hidden @deprecated */\n  size(width: number, height: number, pixelRatio: number) {\n    this.setSize(width, height, pixelRatio);\n    return this;\n  }\n\n  /** @hidden @deprecated */\n  context(type = \"2d\", attributes?: any) {\n    return this.getContext(type, attributes);\n  }\n\n  /** @hidden @deprecated */\n  canvas(legacyTextureDrawer: LegacyCanvasTextureDrawer) {\n    if (typeof legacyTextureDrawer === \"function\") {\n      legacyTextureDrawer.call(this, this.getContext());\n    } else if (typeof legacyTextureDrawer === \"undefined\") {\n      if (typeof this._drawer === \"function\") {\n        this._drawer.call(this);\n      }\n    }\n\n    return this;\n  }\n}\n\n/**\n * Create CanvasTexture (a texture with off-screen canvas).\n */\nexport function canvas(): CanvasTexture;\n\n/** @hidden @deprecated @internal */\nexport function canvas(drawer: LegacyCanvasTextureDrawer): CanvasTexture;\n\n/** @hidden @deprecated @internal */\nexport function canvas(type: string, drawer: LegacyCanvasTextureDrawer): CanvasTexture;\n\n/** @hidden @deprecated @internal */\nexport function canvas(\n  type: string,\n  attributes: Record<string, string>,\n  drawer: LegacyCanvasTextureDrawer,\n): CanvasTexture;\n\nexport function canvas(type?, attributes?, legacyTextureDrawer?): CanvasTexture {\n  if (typeof type === \"function\") {\n    const texture = new CanvasTexture();\n    legacyTextureDrawer = type;\n    texture.setDrawer(function () {\n      legacyTextureDrawer.call(texture, texture.getContext());\n    });\n    return texture;\n  } else if (typeof attributes === \"function\") {\n    const texture = new CanvasTexture();\n    legacyTextureDrawer = attributes;\n    texture.setDrawer(function () {\n      legacyTextureDrawer.call(texture, texture.getContext(type));\n    });\n    return texture;\n  } else if (typeof legacyTextureDrawer === \"function\") {\n    const texture = new CanvasTexture();\n    texture.setDrawer(function () {\n      legacyTextureDrawer.call(texture, texture.getContext(type, attributes));\n    });\n    return texture;\n  } else {\n    const texture = new CanvasTexture();\n    return texture;\n  }\n}\n\n/** @hidden @deprecated */\nexport function memoizeDraw(\n  legacySpriteDrawer: LegacyCanvasSpriteDrawer,\n  legacySpriteMemoizer: LegacyCanvasSpriteMemoizer = () => null,\n) {\n  const sprite = new Sprite();\n  const texture = new CanvasTexture();\n\n  sprite.texture(texture);\n\n  texture.setDrawer(function () {\n    legacySpriteDrawer(2.5 * texture._lastPixelRatio, texture, sprite);\n  });\n\n  texture.setMemoizer(legacySpriteMemoizer);\n\n  return sprite;\n}\n", "import { Vec2Value } from \"../common/matrix\";\n\nimport { Root, Viewport } from \"./root\";\nimport { Component } from \"./component\";\n\n// todo: capture mouse\n// todo: implement unmount\n\n// todo: replace this with single synthetic event names\nexport const POINTER_CLICK = \"click\";\nexport const POINTER_DOWN = \"touchstart mousedown\";\nexport const POINTER_MOVE = \"touchmove mousemove\";\nexport const POINTER_UP = \"touchend mouseup\";\nexport const POINTER_CANCEL = \"touchcancel mousecancel\";\n\n/** @hidden @deprecated */\nexport const POINTER_START = \"touchstart mousedown\";\n/** @hidden @deprecated */\nexport const POINTER_END = \"touchend mouseup\";\n\nclass EventPoint {\n  x: number;\n  y: number;\n\n  clone(obj?: Vec2Value) {\n    if (obj) {\n      obj.x = this.x;\n      obj.y = this.y;\n    } else {\n      obj = {\n        x: this.x,\n        y: this.y,\n      };\n    }\n    return obj;\n  }\n\n  toString() {\n    return (this.x | 0) + \"x\" + (this.y | 0);\n  }\n}\n\n// todo: make object readonly for users, to make it safe for reuse\nclass PointerSyntheticEvent {\n  x: number;\n  y: number;\n  readonly abs = new EventPoint();\n\n  raw: UIEvent;\n  type: string;\n  timeStamp: number;\n\n  clone(obj?: Vec2Value) {\n    if (obj) {\n      obj.x = this.x;\n      obj.y = this.y;\n    } else {\n      obj = {\n        x: this.x,\n        y: this.y,\n      };\n    }\n    return obj;\n  }\n\n  toString() {\n    return this.type + \": \" + (this.x | 0) + \"x\" + (this.y | 0);\n  }\n}\n\n/** @internal */\nclass VisitPayload {\n  type: string = \"\";\n  x: number = 0;\n  y: number = 0;\n  timeStamp: number = -1;\n  event: UIEvent = null;\n  root: Root = null;\n  collected: Component[] | null = null;\n  toString() {\n    return this.type + \": \" + (this.x | 0) + \"x\" + (this.y | 0);\n  }\n}\n\n// todo: define per pointer object? so that don't need to update root\n/** @internal */ const syntheticEvent = new PointerSyntheticEvent();\n\n/** @internal */ const PAYLOAD = new VisitPayload();\n\n/** @internal */\nexport class Pointer {\n  static DEBUG = false;\n  ratio = 1;\n\n  stage: Root;\n  elem: HTMLElement;\n\n  mount(stage: Root, elem: HTMLElement) {\n    this.stage = stage;\n    this.elem = elem;\n\n    this.ratio = stage.viewport().ratio || 1;\n    stage.on(\"viewport\", (viewport: Viewport) => {\n      this.ratio = viewport.ratio ?? this.ratio;\n    });\n\n    // `click` events are synthesized from start/end events on same components\n    // `mousecancel` events are synthesized on blur or mouseup outside element\n\n    elem.addEventListener(\"touchstart\", this.handleStart);\n    elem.addEventListener(\"touchend\", this.handleEnd);\n    elem.addEventListener(\"touchmove\", this.handleMove);\n    elem.addEventListener(\"touchcancel\", this.handleCancel);\n\n    elem.addEventListener(\"mousedown\", this.handleStart);\n    elem.addEventListener(\"mouseup\", this.handleEnd);\n    elem.addEventListener(\"mousemove\", this.handleMove);\n\n    document.addEventListener(\"mouseup\", this.handleCancel);\n    window.addEventListener(\"blur\", this.handleCancel);\n\n    return this;\n  }\n\n  unmount() {\n    const elem = this.elem;\n\n    elem.removeEventListener(\"touchstart\", this.handleStart);\n    elem.removeEventListener(\"touchend\", this.handleEnd);\n    elem.removeEventListener(\"touchmove\", this.handleMove);\n    elem.removeEventListener(\"touchcancel\", this.handleCancel);\n\n    elem.removeEventListener(\"mousedown\", this.handleStart);\n    elem.removeEventListener(\"mouseup\", this.handleEnd);\n    elem.removeEventListener(\"mousemove\", this.handleMove);\n\n    document.removeEventListener(\"mouseup\", this.handleCancel);\n    window.removeEventListener(\"blur\", this.handleCancel);\n\n    return this;\n  }\n\n  clickList: Component[] = [];\n  cancelList: Component[] = [];\n\n  handleStart = (event: TouchEvent | MouseEvent) => {\n    Pointer.DEBUG && console.debug && console.debug(\"pointer-start\", event.type);\n    event.preventDefault();\n    this.localPoint(event);\n    this.dispatchEvent(event.type, event);\n\n    this.findTargets(\"click\", this.clickList);\n    this.findTargets(\"mousecancel\", this.cancelList);\n  };\n\n  handleMove = (event: TouchEvent | MouseEvent) => {\n    event.preventDefault();\n    this.localPoint(event);\n    this.dispatchEvent(event.type, event);\n  };\n\n  handleEnd = (event: TouchEvent | MouseEvent) => {\n    event.preventDefault();\n    // up/end location is not available, last one is used instead\n    Pointer.DEBUG && console.debug && console.debug(\"pointer-end\", event.type);\n    this.dispatchEvent(event.type, event);\n\n    if (this.clickList.length) {\n      Pointer.DEBUG && console.debug && console.debug(\"pointer-click: \", event.type, this.clickList?.length);\n      this.dispatchEvent(\"click\", event, this.clickList);\n    }\n    this.cancelList.length = 0;\n  };\n\n  handleCancel = (event: TouchEvent | MouseEvent | FocusEvent) => {\n    if (this.cancelList.length) {\n      Pointer.DEBUG && console.debug && console.debug(\"pointer-cancel\", event.type, this.clickList?.length);\n      this.dispatchEvent(\"mousecancel\", event, this.cancelList);\n    }\n    this.clickList.length = 0;\n  };\n\n  /**\n   * Computer the location of the pointer event in the canvas coordination\n   */\n  localPoint(event: TouchEvent | MouseEvent) {\n    const elem = this.elem;\n    let x: number;\n    let y: number;\n    // pageX/Y if available?\n\n    if ((event as TouchEvent).touches?.length) {\n      x = (event as TouchEvent).touches[0].clientX;\n      y = (event as TouchEvent).touches[0].clientY;\n    } else {\n      x = (event as MouseEvent).clientX;\n      y = (event as MouseEvent).clientY;\n    }\n\n    const rect = elem.getBoundingClientRect();\n    x -= rect.left;\n    y -= rect.top;\n    x -= elem.clientLeft | 0;\n    y -= elem.clientTop | 0;\n\n    PAYLOAD.x = x * this.ratio;\n    PAYLOAD.y = y * this.ratio;\n  }\n\n  /**\n   * Find eligible target for and event type, used to keep trace components to dispatch click event\n   */\n  findTargets(type: string, result: Component[]) {\n    const payload = PAYLOAD;\n\n    payload.type = type;\n    payload.root = this.stage;\n    payload.event = null;\n    payload.collected = result;\n    payload.collected.length = 0;\n\n    this.stage.visit(\n      {\n        reverse: true,\n        visible: true,\n        start: this.visitStart,\n        end: this.visitEnd,\n      },\n      payload,\n    );\n  }\n\n  dispatchEvent(type: string, event: UIEvent, targets?: Component[]) {\n    const payload = PAYLOAD;\n\n    payload.type = type;\n    payload.root = this.stage;\n    payload.event = event;\n    payload.timeStamp = Date.now();\n    payload.collected = null;\n\n    if (type !== \"mousemove\" && type !== \"touchmove\") {\n      Pointer.DEBUG && console.debug && console.debug(\"pointer:dispatchEvent\", payload, targets?.length);\n    }\n\n    if (targets) {\n      while (targets.length) {\n        const component = targets.shift();\n        if (this.visitEnd(component, payload)) {\n          break;\n        }\n      }\n      targets.length = 0;\n    } else {\n      this.stage.visit(\n        {\n          reverse: true,\n          visible: true,\n          start: this.visitStart,\n          end: this.visitEnd,\n        },\n        payload,\n      );\n    }\n  }\n\n  visitStart = (component: Component, payload: VisitPayload) => {\n    return !component._flag(payload.type);\n  };\n\n  visitEnd = (component: Component, payload: VisitPayload) => {\n    // mouse: event/collect, type, root\n    syntheticEvent.raw = payload.event;\n    syntheticEvent.type = payload.type;\n    syntheticEvent.timeStamp = payload.timeStamp;\n    syntheticEvent.abs.x = payload.x;\n    syntheticEvent.abs.y = payload.y;\n\n    const listeners = component.listeners(payload.type);\n    if (!listeners) {\n      return;\n    }\n\n    component.matrix().inverse().map(payload, syntheticEvent);\n\n    // deep flags are used to decide to pass down event, and spy is not used for that\n    // we use spy to decide if an event should be delivered to elements that do not have hitTest\n    // todo: collect and pass hitTest result upward instead, probably use visit payload\n    const isEventTarget = component === payload.root || component.attr(\"spy\") || component.hitTest(syntheticEvent);\n    if (!isEventTarget) {\n      return;\n    }\n\n    if (payload.collected) {\n      payload.collected.push(component);\n    }\n\n    // todo: when this condition is false?\n    if (payload.event) {\n      // todo: use a function call to cancel processing events, like dom\n      let stop = false;\n      for (let l = 0; l < listeners.length; l++) {\n        stop = listeners[l].call(component, syntheticEvent) ? true : stop;\n      }\n      return stop;\n    }\n  };\n}\n\n/** @hidden @deprecated @internal */\nexport const Mouse = {\n  CLICK: \"click\",\n  START: \"touchstart mousedown\",\n  MOVE: \"touchmove mousemove\",\n  END: \"touchend mouseup\",\n  CANCEL: \"touchcancel mousecancel\",\n};\n", "import stats from \"../common/stats\";\nimport { Matrix } from \"../common/matrix\";\n\nimport { Component } from \"./component\";\nimport { Pointer } from \"./pointer\";\nimport { FitMode, isValidFitMode } from \"./pin\";\n\n/** @internal */ const ROOTS: Root[] = [];\n\nexport function pause() {\n  for (let i = ROOTS.length - 1; i >= 0; i--) {\n    ROOTS[i].pause();\n  }\n}\n\nexport function resume() {\n  for (let i = ROOTS.length - 1; i >= 0; i--) {\n    ROOTS[i].resume();\n  }\n}\n\nexport function mount(configs: RootConfig = {}) {\n  const root = new Root();\n  // todo: root.use(new Pointer());\n  root.mount(configs);\n  // todo: maybe just pass root? or do root.use(pointer)\n  root.pointer = new Pointer().mount(root, root.dom as HTMLElement);\n  return root;\n}\n\ntype RootConfig = {\n  canvas?: string | HTMLCanvasElement;\n};\n\n/**\n * Geometry of the rectangular that the application takes on the screen.\n */\nexport type Viewport = {\n  width: number;\n  height: number;\n  ratio: number;\n};\n\n/**\n * Geometry of a rectangular portion of the game that is projected on the screen.\n */\nexport type Viewbox = {\n  x?: number;\n  y?: number;\n  width: number;\n  height: number;\n  mode?: FitMode;\n};\n\nexport class Root extends Component {\n  canvas: HTMLCanvasElement | null = null;\n  dom: HTMLCanvasElement | null = null;\n  context: CanvasRenderingContext2D | null = null;\n\n  /** @internal */ pixelWidth = -1;\n  /** @internal */ pixelHeight = -1;\n  /** @internal */ pixelRatio = 1;\n  /** @internal */ drawingWidth = 0;\n  /** @internal */ drawingHeight = 0;\n\n  mounted = false;\n  paused = false;\n  sleep = false;\n\n  /** @internal */ devicePixelRatio: number;\n  /** @internal */ backingStoreRatio: number;\n\n  /** @internal */ pointer: Pointer;\n\n  /** @internal */ _viewport: Viewport;\n  /** @internal */ _viewbox: Viewbox;\n  /** @internal */ _camera: Matrix;\n\n  constructor() {\n    super();\n    this.label(\"Root\");\n  }\n\n  mount = (configs: RootConfig = {}) => {\n    if (typeof configs.canvas === \"string\") {\n      this.canvas = document.getElementById(configs.canvas) as HTMLCanvasElement;\n      if (!this.canvas) {\n        console.error(\"Canvas element not found: \", configs.canvas);\n      }\n    } else if (configs.canvas instanceof HTMLCanvasElement) {\n      this.canvas = configs.canvas;\n    } else if (configs.canvas) {\n      console.error(\"Unknown value for canvas:\", configs.canvas);\n    }\n\n    if (!this.canvas) {\n      this.canvas = (document.getElementById(\"cutjs\") ||\n        document.getElementById(\"stage\")) as HTMLCanvasElement;\n    }\n\n    if (!this.canvas) {\n      console.debug && console.debug(\"Creating canvas element...\");\n      this.canvas = document.createElement(\"canvas\");\n      Object.assign(this.canvas.style, {\n        position: \"absolute\",\n        display: \"block\",\n        top: \"0\",\n        left: \"0\",\n        bottom: \"0\",\n        right: \"0\",\n        width: \"100%\",\n        height: \"100%\",\n      });\n\n      const body = document.body;\n      body.insertBefore(this.canvas, body.firstChild);\n    }\n\n    this.dom = this.canvas;\n\n    this.context = this.canvas.getContext(\"2d\");\n\n    this.devicePixelRatio = window.devicePixelRatio || 1;\n    this.backingStoreRatio =\n      this.context[\"webkitBackingStorePixelRatio\"] ||\n      this.context[\"mozBackingStorePixelRatio\"] ||\n      this.context[\"msBackingStorePixelRatio\"] ||\n      this.context[\"oBackingStorePixelRatio\"] ||\n      this.context[\"backingStorePixelRatio\"] ||\n      1;\n\n    this.pixelRatio = this.devicePixelRatio / this.backingStoreRatio;\n\n    // resize();\n    // window.addEventListener('resize', resize, false);\n    // window.addEventListener('orientationchange', resize, false);\n\n    this.mounted = true;\n    ROOTS.push(this);\n    this.requestFrame();\n  };\n\n  /** @internal */ frameRequested = false;\n\n  /** @internal */\n  requestFrame = () => {\n    // one request at a time\n    if (!this.frameRequested) {\n      this.frameRequested = true;\n      requestAnimationFrame(this.onFrame);\n    }\n  };\n\n  /** @internal */ _lastFrameTime = 0;\n  /** @internal */ _mo_touch: number | null = null; // monitor touch\n\n  /** @internal */\n  onFrame = (now: number) => {\n    this.frameRequested = false;\n\n    if (!this.mounted || !this.canvas || !this.context) {\n      return;\n    }\n\n    this.requestFrame();\n\n    const newPixelWidth = this.canvas.clientWidth;\n    const newPixelHeight = this.canvas.clientHeight;\n\n    if (this.pixelWidth !== newPixelWidth || this.pixelHeight !== newPixelHeight) {\n      // viewport pixel size is not the same as last time\n      this.pixelWidth = newPixelWidth;\n      this.pixelHeight = newPixelHeight;\n\n      this.drawingWidth = newPixelWidth * this.pixelRatio;\n      this.drawingHeight = newPixelHeight * this.pixelRatio;\n\n      if (this.canvas.width !== this.drawingWidth || this.canvas.height !== this.drawingHeight) {\n        // canvas size doesn't math\n        this.canvas.width = this.drawingWidth;\n        this.canvas.height = this.drawingHeight;\n\n        console.debug && console.debug(\n            \"Resize: [\" +\n              this.drawingWidth +\n              \", \" +\n              this.drawingHeight +\n              \"] = \" +\n              this.pixelRatio +\n              \" x [\" +\n              this.pixelWidth +\n              \", \" +\n              this.pixelHeight +\n              \"]\",\n          );\n\n        this.viewport({\n          width: this.drawingWidth,\n          height: this.drawingHeight,\n          ratio: this.pixelRatio,\n        });\n      }\n    }\n\n    const last = this._lastFrameTime || now;\n    const elapsed = now - last;\n\n    if (!this.mounted || this.paused || this.sleep) {\n      return;\n    }\n\n    this._lastFrameTime = now;\n\n    this.prerender();\n\n    const tickRequest = this._tick(elapsed, now, last);\n    if (this._mo_touch != this._ts_touch) {\n      // something changed since last call\n      this._mo_touch = this._ts_touch;\n      this.sleep = false;\n\n      if (this.drawingWidth > 0 && this.drawingHeight > 0) {\n        this.context.setTransform(1, 0, 0, 1, 0, 0);\n        this.context.clearRect(0, 0, this.drawingWidth, this.drawingHeight);  \n        if (this.debugDrawAxis > 0) {\n          this.renderDebug(this.context);\n        }\n        this.render(this.context);\n      }\n    } else if (tickRequest) {\n      // nothing changed, but a component requested next tick\n      this.sleep = false;\n    } else {\n      // nothing changed, and no component requested next tick\n      this.sleep = true;\n    }\n\n    stats.fps = elapsed ? 1000 / elapsed : 0;\n  };\n\n  /** @hidden */\n  debugDrawAxis = 0;\n\n  private renderDebug(context: CanvasRenderingContext2D): void {\n    const size = typeof this.debugDrawAxis === \"number\" ? this.debugDrawAxis : 10;\n    const m = this.matrix();\n    context.setTransform(m.a, m.b, m.c, m.d, m.e, m.f);\n    const lineWidth = 3 / m.a;\n\n    context.beginPath();\n    context.moveTo(0, 0);\n    context.lineTo(0, 0.8 * size);\n    context.lineTo(-0.2 * size, 0.8 * size);\n    context.lineTo(0, size);\n    context.lineTo(+0.2 * size, 0.8 * size);\n    context.lineTo(0, 0.8 * size);\n    context.strokeStyle = 'rgba(93, 173, 226)';\n    context.lineJoin = \"round\";\n    context.lineCap = \"round\";\n    context.lineWidth = lineWidth;\n    context.stroke();\n\n    context.beginPath();\n    context.moveTo(0, 0);\n    context.lineTo(0.8 * size, 0);\n    context.lineTo(0.8 * size, -0.2 * size);\n    context.lineTo(size, 0);\n    context.lineTo(0.8 * size, +0.2 * size);\n    context.lineTo(0.8 * size, 0);\n    context.strokeStyle = 'rgba(236, 112, 99)';\n    context.lineJoin = \"round\";\n    context.lineCap = \"round\";\n    context.lineWidth = lineWidth;\n    context.stroke();\n  }\n\n  resume() {\n    if (this.sleep || this.paused) {\n      this.requestFrame();\n    }\n    this.paused = false;\n    this.sleep = false;\n    this.publish(\"resume\");\n    return this;\n  }\n\n  pause() {\n    if (!this.paused) {\n      this.publish(\"pause\");\n    }\n    this.paused = true;\n    return this;\n  }\n\n  /** @internal */\n  touch() {\n    if (this.sleep || this.paused) {\n      this.requestFrame();\n    }\n    this.sleep = false;\n    return super.touch();\n  }\n\n  unmount() {\n    this.mounted = false;\n    const index = ROOTS.indexOf(this);\n    if (index >= 0) {\n      ROOTS.splice(index, 1);\n    }\n\n    this.pointer?.unmount();\n    return this;\n  }\n\n  background(color: string) {\n    if (this.dom) {\n      this.dom.style.backgroundColor = color;\n    }\n    return this;\n  }\n\n  /**\n   * Set/Get viewport.\n   * This is used along with viewbox to determine the scale and position of the viewbox within the viewport.\n   * Viewport is the size of the container, for example size of the canvas element.\n   * Viewbox is provided by the user, and is the ideal size of the content.\n   */\n  viewport(): Viewport;\n  viewport(width: number, height: number, ratio?: number): this;\n  viewport(viewbox: Viewport): this;\n  viewport(width?: number | Viewport, height?: number, ratio?: number) {\n    if (typeof width === \"undefined\") {\n      // todo: return readonly object instead\n      return Object.assign({}, this._viewport);\n    }\n\n    if (typeof width === \"object\") {\n      const options = width;\n      width = options.width;\n      height = options.height;\n      ratio = options.ratio;\n    }\n\n    if (typeof width === \"number\" && typeof height === \"number\") {\n      this._viewport = {\n        width: width,\n        height: height,\n        ratio: typeof ratio === \"number\" ? ratio : 1,\n      };\n      this.viewbox();\n      const data = Object.assign({}, this._viewport);\n      this.visit({\n        start: function (component) {\n          if (!component._flag(\"viewport\")) {\n            return true;\n          }\n          component.publish(\"viewport\", [data]);\n        },\n      });\n    }\n\n    return this;\n  }\n\n  /**\n   * Set viewbox.\n   */\n  viewbox(viewbox: Viewbox): this;\n  viewbox(width?: number, height?: number, mode?: FitMode): this;\n  viewbox(width?: number | Viewbox, height?: number, mode?: FitMode): this {\n    // TODO: static/fixed viewbox\n    // TODO: use css object-fit values\n    if (typeof width === \"number\" && typeof height === \"number\") {\n      this._viewbox = {\n        width,\n        height,\n        mode,\n      };\n    } else if (typeof width === \"object\" && width !== null) {\n      this._viewbox = {\n        ...width,\n      };\n    }\n\n    this.rescale();\n\n    return this;\n  }\n\n  camera(matrix: Matrix) {\n    this._camera = matrix;\n    this.rescale();\n    return this;\n  }\n\n  /** @internal */\n  rescale() {\n    const viewbox = this._viewbox;\n    const viewport = this._viewport;\n    const camera = this._camera;\n    if (viewport && viewbox) {\n      const viewportWidth = viewport.width;\n      const viewportHeight = viewport.height;\n      const viewboxMode = isValidFitMode(viewbox.mode) ? viewbox.mode : \"in-pad\";\n      const viewboxWidth = viewbox.width;\n      const viewboxHeight = viewbox.height;\n\n      this.pin({\n        width: viewboxWidth,\n        height: viewboxHeight,\n      });\n      this.fit(viewportWidth, viewportHeight, viewboxMode);\n\n      const viewboxX = viewbox.x || 0;\n      const viewboxY = viewbox.y || 0;\n\n      const cameraZoomX = camera?.a || 1;\n      const cameraZoomY = camera?.d || 1;\n      const cameraX = camera?.e || 0;\n      const cameraY = camera?.f || 0;\n\n      const scaleX = this.pin(\"scaleX\");\n      const scaleY = this.pin(\"scaleY\");\n\n      this.pin(\"scaleX\", scaleX * cameraZoomX);\n      this.pin(\"scaleY\", scaleY * cameraZoomY);\n\n      this.pin(\"offsetX\", cameraX - viewboxX * scaleX * cameraZoomX);\n      this.pin(\"offsetY\", cameraY - viewboxY * scaleY * cameraZoomY);\n    } else if (viewport) {\n      this.pin({\n        width: viewport.width,\n        height: viewport.height,\n      });\n    }\n\n    return this;\n  }\n\n  /** @hidden */\n  flipX(x: boolean) {\n    this._pin._directionX = x ? -1 : 1;\n    return this;\n  }\n\n  /** @hidden */\n  flipY(y: boolean) {\n    this._pin._directionY = y ? -1 : 1;\n    return this;\n  }\n}\n", "import { math } from \"../common/math\";\nimport { Texture, TextureSelectionInputArray, texture } from \"../texture\";\n\nimport { Component } from \"./component\";\n\nexport function anim(frames: string | TextureSelectionInputArray, fps?: number) {\n  const anim = new Anim();\n  anim.frames(frames).gotoFrame(0);\n  fps && anim.fps(fps);\n  return anim;\n}\n\n// TODO: replace with atlas fps or texture time\n/** @internal */ const FPS = 15;\n\nexport class Anim extends Component {\n  /** @internal */ _texture: Texture | null = null;\n\n  /** @internal */ _frames: Texture[] = [];\n\n  /** @internal */ _fps: number;\n  /** @internal */ _ft: number;\n\n  /** @internal */ _time: number = -1;\n  /** @internal */ _repeat: number = 0;\n  /** @internal */ _index: number = 0;\n\n  /** @internal */ _callback: () => void;\n\n  constructor() {\n    super();\n    this.label(\"Anim\");\n\n    this._fps = FPS;\n    this._ft = 1000 / this._fps;\n\n    this.tick(this._animTick, false);\n  }\n\n  /** @hidden */\n  renderTexture(context: CanvasRenderingContext2D) {\n    if (!this._texture) return;\n\n    this._texture.draw(context);\n  }\n\n  /** @internal */\n  private _animTickLastTime = 0;\n  /** @internal */\n  private _animTick = (t: number, now: number, last: number) => {\n    if (this._time < 0 || this._frames.length <= 1) {\n      return;\n    }\n\n    // ignore old elapsed\n    const ignore = this._animTickLastTime != last;\n    this._animTickLastTime = now;\n    if (ignore) {\n      return true;\n    }\n\n    this._time += t;\n    if (this._time < this._ft) {\n      return true;\n    }\n    const n = (this._time / this._ft) | 0;\n    this._time -= n * this._ft;\n    this.moveFrame(n);\n    if (this._repeat > 0 && (this._repeat -= n) <= 0) {\n      this.stop();\n      this._callback && this._callback();\n      return false;\n    }\n    return true;\n  };\n\n  fps(fps?: number) {\n    if (typeof fps === \"undefined\") {\n      return this._fps;\n    }\n    this._fps = fps > 0 ? fps : FPS;\n    this._ft = 1000 / this._fps;\n    return this;\n  }\n\n  /** @deprecated Use frames */\n  setFrames(frames: string | TextureSelectionInputArray) {\n    return this.frames(frames);\n  }\n\n  frames(frames: string | TextureSelectionInputArray) {\n    this._index = 0;\n    this._frames = texture(frames).array();\n    this.touch();\n    return this;\n  }\n\n  length() {\n    return this._frames ? this._frames.length : 0;\n  }\n\n  gotoFrame(frame: number, resize = false) {\n    this._index = math.wrap(frame, this._frames.length) | 0;\n    resize = resize || !this._texture;\n    this._texture = this._frames[this._index];\n    if (resize) {\n      this.pin(\"width\", this._texture.getWidth());\n      this.pin(\"height\", this._texture.getHeight());\n    }\n    this.touch();\n    return this;\n  }\n\n  moveFrame(move: number) {\n    return this.gotoFrame(this._index + move);\n  }\n\n  repeat(repeat: number, callback?: () => void) {\n    this._repeat = repeat * this._frames.length - 1;\n    this._callback = callback;\n    this.play();\n    return this;\n  }\n\n  play(frame?: number) {\n    if (typeof frame !== \"undefined\") {\n      this.gotoFrame(frame);\n      this._time = 0;\n    } else if (this._time < 0) {\n      this._time = 0;\n    }\n\n    this.touch();\n    return this;\n  }\n\n  stop(frame?: number) {\n    this._time = -1;\n    if (typeof frame !== \"undefined\") {\n      this.gotoFrame(frame);\n    }\n    return this;\n  }\n}\n", "import { Texture, texture } from \"../texture\";\n\nimport { Component } from \"./component\";\n\nexport function monotype(chars: string | Record<string, Texture> | ((char: string) => Texture)) {\n  return new Monotype().frames(chars);\n}\n\nexport class Monotype extends Component {\n  /** @internal */ _textures: Texture[] = [];\n\n  /** @internal */ _font: (value: string) => Texture;\n  /** @internal */ _value: string | number | string[] | number[];\n\n  constructor() {\n    super();\n    this.label(\"Monotype\");\n  }\n\n  /** @hidden */\n  renderTexture(context: CanvasRenderingContext2D) {\n    if (!this._textures || !this._textures.length) return;\n\n    for (let i = 0, n = this._textures.length; i < n; i++) {\n      this._textures[i].draw(context);\n    }\n  }\n\n  /** @deprecated Use frames */\n  setFont(frames: string | Record<string, Texture> | ((char: string) => Texture)) {\n    return this.frames(frames);\n  }\n\n  frames(frames: string | Record<string, Texture> | ((char: string) => Texture)) {\n    this._textures = [];\n    if (typeof frames == \"string\") {\n      const selection = texture(frames);\n      this._font = function (value: string) {\n        return selection.one(value);\n      };\n    } else if (typeof frames === \"object\") {\n      this._font = function (value: string) {\n        return frames[value];\n      };\n    } else if (typeof frames === \"function\") {\n      this._font = frames;\n    }\n    return this;\n  }\n\n  /** @deprecated Use value */\n  setValue(value: string | number | string[] | number[]) {\n    return this.value(value);\n  }\n\n  value(value: string | number | string[] | number[]): this;\n  value(value: string | number | string[] | number[]) {\n    if (typeof value === \"undefined\") {\n      return this._value;\n    }\n    if (this._value === value) {\n      return this;\n    }\n    this._value = value;\n\n    if (value === null) {\n      value = \"\";\n    } else if (typeof value !== \"string\" && !Array.isArray(value)) {\n      value = value.toString();\n    }\n\n    this._spacing = this._spacing || 0;\n\n    let width = 0;\n    let height = 0;\n    for (let i = 0; i < value.length; i++) {\n      const v = value[i];\n      const texture = (this._textures[i] = this._font(typeof v === \"string\" ? v : v + \"\"));\n      width += i > 0 ? this._spacing : 0;\n      texture.setDestinationCoordinate(width, 0);\n      width = width + texture.getWidth();\n      height = Math.max(height, texture.getHeight());\n    }\n    this.pin(\"width\", width);\n    this.pin(\"height\", height);\n    this._textures.length = value.length;\n    return this;\n  }\n}\n\n/** @hidden @deprecated */\nexport { monotype as string };\n/** @hidden @deprecated */\nexport { Monotype as Str };\n"], "names": ["Matrix", "d", "b", "__assign", "Texture", "ImageTexture", "PipeTexture", "texture", "Atlas", "def", "TextureSelection", "atlas", "ResizableTexture", "iid", "<PERSON>n", "Easing", "Transition", "component", "Component", "self", "sprite", "Sprite", "CanvasTexture", "EventPoint", "PointerSyntheticEvent", "VisitPayload", "Pointer", "Root", "anim", "<PERSON><PERSON>", "Monotype"], "mappings": ";;;;;;;;;AAAiB,MAAM,cAAc,KAAK;AACzB,MAAM,YAAY,KAAK;AAGxB,WAAA,OAAO,KAAc,KAAY;AAC3C,QAAA,OAAO,QAAQ,aAAa;AACxB,YAAA;AACA,YAAA;AAAA,IAAA,WACG,OAAO,QAAQ,aAAa;AAC/B,YAAA;AACA,YAAA;AAAA,IAAA;AAER,WAAO,OAAO,MAAM,MAAM,YAAa,KAAI,MAAM,OAAO;AAAA,EAC1D;AAGgB,WAAA,KAAK,KAAa,KAAc,KAAY;AACtD,QAAA,OAAO,QAAQ,aAAa;AACxB,YAAA;AACA,YAAA;AAAA,IAAA,WACG,OAAO,QAAQ,aAAa;AAC/B,YAAA;AACA,YAAA;AAAA,IAAA;AAER,QAAI,MAAM,KAAK;AACN,aAAA,MAAM,QAAQ,MAAM;AACpB,aAAA,OAAO,MAAM,IAAI,MAAM;AAAA,IAAA,OACzB;AACE,aAAA,MAAM,QAAQ,MAAM;AACpB,aAAA,OAAO,OAAO,IAAI,MAAM;AAAA,IAAA;AAAA,EAEnC;AAGgB,WAAA,MAAM,KAAa,KAAa,KAAW;AACzD,QAAI,MAAM,KAAK;AACN,aAAA;AAAA,IAAA,WACE,MAAM,KAAK;AACb,aAAA;AAAA,IAAA,OACF;AACE,aAAA;AAAA,IAAA;AAAA,EAEX;AAGgB,WAAA,OAAO,GAAW,GAAS;AACzC,WAAO,UAAU,IAAI,IAAI,IAAI,CAAC;AAAA,EAChC;AAEa,MAAA,OAAO,OAAO,OAAO,IAAI;AAEtC,OAAK,SAAS;AACd,OAAK,OAAO;AACZ,OAAK,QAAQ;AACb,OAAK,SAAS;AAGd,OAAK,SAAS;AAEd,OAAK,QAAQ;AC7Cb,MAAA;AAAA;AAAA,IAAA,WAAA;AAkBE,eACEA,QAAA,GACA,GACA,GACA,GACA,GACA,GAAU;AAtBZ,aAAC,IAAG;AACJ,aAAC,IAAG;AACJ,aAAC,IAAG;AAEJ,aAAC,IAAG;AAEJ,aAAC,IAAG;AAEJ,aAAC,IAAG;AAgBE,YAAA,OAAO,MAAM,UAAU;AACzB,eAAK,MAAM,CAAC;AAAA,QAAA,OACP;AACL,eAAK,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,QAAA;AAAA,MAC7B;AAGFA,cAAA,UAAA,WAAA,WAAA;AACE,eACE,MACA,KAAK,IACL,OACA,KAAK,IACL,OACA,KAAK,IACL,OACA,KAAK,IACL,OACA,KAAK,IACL,OACA,KAAK,IACL;AAAA,MAEJ;AAEAA,cAAA,UAAA,QAAA,WAAA;AACE,eAAO,IAAIA,QAAO,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AAAA,MAClE;AAIAA,cAAA,UAAA,QAAA,SACE,GACA,GACA,GACA,GACA,GACA,GAAU;AAEV,aAAK,SAAS;AACV,YAAA,OAAO,MAAM,UAAU;AACzB,eAAK,IAAI,EAAE;AACX,eAAK,IAAI,EAAE;AACX,eAAK,IAAI,EAAE;AACX,eAAK,IAAI,EAAE;AACX,eAAK,IAAI,EAAE;AACX,eAAK,IAAI,EAAE;AAAA,QAAA,OACN;AACL,eAAK,IAAI,OAAO,MAAM,WAAW,IAAI;AACrC,eAAK,IAAI,OAAO,MAAM,WAAW,IAAI;AACrC,eAAK,IAAI,OAAO,MAAM,WAAW,IAAI;AACrC,eAAK,IAAI,OAAO,MAAM,WAAW,IAAI;AACrC,eAAK,IAAI,OAAO,MAAM,WAAW,IAAI;AACrC,eAAK,IAAI,OAAO,MAAM,WAAW,IAAI;AAAA,QAAA;AAEhC,eAAA;AAAA,MACT;AAEAA,cAAA,UAAA,WAAA,WAAA;AACE,aAAK,SAAS;AACd,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI;AACF,eAAA;AAAA,MACT;AAEAA,cAAM,UAAA,SAAN,SAAO,OAAa;AAClB,YAAI,CAAC,OAAO;AACH,iBAAA;AAAA,QAAA;AAGT,aAAK,SAAS;AAEd,YAAM,IAAI,QAAQ,KAAK,IAAI,KAAK,IAAI;AAEpC,YAAM,IAAI,QAAQ,KAAK,IAAI,KAAK,IAAI;AAEpC,YAAM,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK;AAChC,YAAM,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK;AAChC,YAAM,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK;AAChC,YAAM,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK;AAChC,YAAM,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK;AAChC,YAAM,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK;AAEhC,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI;AAEF,eAAA;AAAA,MACT;AAEAA,cAAA,UAAA,YAAA,SAAU,GAAW,GAAS;AACxB,YAAA,CAAC,KAAK,CAAC,GAAG;AACL,iBAAA;AAAA,QAAA;AAET,aAAK,SAAS;AACd,aAAK,KAAK;AACV,aAAK,KAAK;AACH,eAAA;AAAA,MACT;AAEAA,cAAA,UAAA,QAAA,SAAM,GAAW,GAAS;AACxB,YAAI,EAAE,IAAI,MAAM,EAAE,IAAI,IAAI;AACjB,iBAAA;AAAA,QAAA;AAET,aAAK,SAAS;AACd,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AACH,eAAA;AAAA,MACT;AAEAA,cAAA,UAAA,OAAA,SAAK,GAAW,GAAS;AACnB,YAAA,CAAC,KAAK,CAAC,GAAG;AACL,iBAAA;AAAA,QAAA;AAET,aAAK,SAAS;AAEd,YAAM,IAAI,KAAK,IAAI,KAAK,IAAI;AAC5B,YAAM,IAAI,KAAK,IAAI,KAAK,IAAI;AAC5B,YAAM,IAAI,KAAK,IAAI,KAAK,IAAI;AAC5B,YAAM,IAAI,KAAK,IAAI,KAAK,IAAI;AAC5B,YAAM,IAAI,KAAK,IAAI,KAAK,IAAI;AAC5B,YAAM,IAAI,KAAK,IAAI,KAAK,IAAI;AAE5B,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI;AACF,eAAA;AAAA,MACT;AAEAA,cAAM,UAAA,SAAN,SAAO,GAAc;AACnB,aAAK,SAAS;AAEd,YAAM,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;AACpC,YAAM,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;AACpC,YAAM,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;AACpC,YAAM,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;AAC9B,YAAA,IAAI,KAAK,IAAI,EAAE,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;AACpC,YAAA,IAAI,KAAK,IAAI,EAAE,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;AAE1C,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI;AAEF,eAAA;AAAA,MACT;AAEAA,cAAA,UAAA,UAAA,WAAA;AACE,YAAI,KAAK,QAAQ;AACf,eAAK,SAAS;AACV,cAAA,CAAC,KAAK,UAAU;AACb,iBAAA,WAAW,IAAIA;;AAItB,cAAM,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK;AACrC,eAAA,SAAS,IAAI,KAAK,IAAI;AAC3B,eAAK,SAAS,IAAI,CAAC,KAAK,IAAI;AAC5B,eAAK,SAAS,IAAI,CAAC,KAAK,IAAI;AACvB,eAAA,SAAS,IAAI,KAAK,IAAI;AACtB,eAAA,SAAS,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK;AACnD,eAAA,SAAS,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK;AAAA,QAAA;AAE1D,eAAO,KAAK;AAAA,MACd;AAEAA,cAAA,UAAA,MAAA,SAAI,GAAc,GAAa;AAC7B,YAAI,KAAK,EAAE,GAAG,GAAG,GAAG,EAAC;AACnB,UAAA,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK;AACvC,UAAA,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK;AAClC,eAAA;AAAA,MACT;AAEAA,cAAA,UAAA,OAAA,SAAK,GAAuB,GAAU;AAChC,YAAA,OAAO,MAAM,UAAU;AACzB,cAAI,EAAE;AACN,cAAI,EAAE;AAAA,QAAA;AAER,eAAO,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK;AAAA,MACxC;AAEAA,cAAA,UAAA,OAAA,SAAK,GAAuB,GAAU;AAChC,YAAA,OAAO,MAAM,UAAU;AACzB,cAAI,EAAE;AACN,cAAI,EAAE;AAAA,QAAA;AAER,eAAO,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK;AAAA,MACxC;AACDA,aAAAA;AAAAA,IAAA,EAAA;AAAA;AAAA,ECpPD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA,MAAI,gBAAgB,SAAS,GAAG,GAAG;AAC/B,oBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAA,eAAgB,SAAS,SAAUC,IAAGC,IAAG;AAAE,MAAAD,GAAE,YAAYC;AAAA,IAAE,KACzE,SAAUD,IAAGC,IAAG;AAAE,eAAS,KAAKA,GAAG,KAAIA,GAAE,eAAe,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA;AAC1E,WAAO,cAAc,GAAG,CAAC;AAAA,EAC7B;AAEO,WAAS,UAAU,GAAG,GAAG;AAC5B,kBAAc,GAAG,CAAC;AAClB,aAAS,KAAK;AAAE,WAAK,cAAc;AAAA,IAAI;AACvC,MAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAI;AAAA,EACvF;AAEO,MAAI,WAAW,WAAW;AAC7B,eAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,eAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,YAAI,UAAU,CAAC;AACf,iBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,MAC9E;AACD,aAAO;AAAA,IACV;AACD,WAAO,SAAS,MAAM,MAAM,SAAS;AAAA,EACzC;AA6BO,WAAS,UAAU,SAAS,YAAY,GAAG,WAAW;AACzD,aAAS,MAAM,OAAO;AAAE,aAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,gBAAQ,KAAK;AAAA,MAAE,CAAE;AAAA,IAAI;AAC5G,WAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,eAAS,UAAU,OAAO;AAAE,YAAI;AAAE,eAAK,UAAU,KAAK,KAAK,CAAC;AAAA,QAAE,SAAU,GAAG;AAAE,iBAAO,CAAC;AAAA;MAAM;AAC3F,eAAS,SAAS,OAAO;AAAE,YAAI;AAAE,eAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,QAAI,SAAQ,GAAG;AAAE,iBAAO,CAAC;AAAA;MAAM;AAC9F,eAAS,KAAK,QAAQ;AAAE,eAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,MAAI;AAC9G,YAAM,YAAY,UAAU,MAAM,SAAuB,CAAE,CAAA,GAAG,KAAI,CAAE;AAAA,IAC5E,CAAK;AAAA,EACL;AAEO,WAAS,YAAY,SAAS,MAAM;AACvC,QAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,UAAI,EAAE,CAAC,IAAI,EAAG,OAAM,EAAE,CAAC;AAAG,aAAO,EAAE,CAAC;AAAA,IAAI,GAAE,MAAM,CAAE,GAAE,KAAK,CAAA,EAAI,GAAE,GAAG,GAAG,GAAG;AAC/G,WAAO,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAG,GAAE,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,aAAO;AAAA,IAAO,IAAG;AACvJ,aAAS,KAAK,GAAG;AAAE,aAAO,SAAU,GAAG;AAAE,eAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,MAAI;AAAA,IAAG;AAClE,aAAS,KAAK,IAAI;AACd,UAAI,EAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,aAAO,EAAG,KAAI;AACV,YAAI,IAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,KAAM,QAAO;AAC3J,YAAI,IAAI,GAAG,EAAG,MAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,gBAAQ,GAAG,CAAC,GAAC;AAAA,UACT,KAAK;AAAA,UAAG,KAAK;AAAG,gBAAI;AAAI;AAAA,UACxB,KAAK;AAAG,cAAE;AAAS,mBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAK;AAAA,UACrD,KAAK;AAAG,cAAE;AAAS,gBAAI,GAAG,CAAC;AAAG,iBAAK,CAAC,CAAC;AAAG;AAAA,UACxC,KAAK;AAAG,iBAAK,EAAE,IAAI;AAAO,cAAE,KAAK,IAAG;AAAI;AAAA,UACxC;AACI,gBAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,kBAAI;AAAG;AAAA,YAAW;AAC5G,gBAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,gBAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,YAAQ;AACtF,gBAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,gBAAE,QAAQ,EAAE,CAAC;AAAG,kBAAI;AAAI;AAAA,YAAQ;AACrE,gBAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,gBAAE,QAAQ,EAAE,CAAC;AAAG,gBAAE,IAAI,KAAK,EAAE;AAAG;AAAA,YAAQ;AACnE,gBAAI,EAAE,CAAC,EAAG,GAAE,IAAI,IAAG;AACnB,cAAE,KAAK,IAAK;AAAE;AAAA,QACrB;AACD,aAAK,KAAK,KAAK,SAAS,CAAC;AAAA,MAC5B,SAAQ,GAAG;AAAE,aAAK,CAAC,GAAG,CAAC;AAAG,YAAI;AAAA,MAAE,UAAW;AAAE,YAAI,IAAI;AAAA,MAAI;AAC1D,UAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AAAG,aAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM;IAC7E;AAAA,EACL;ACtGA,MAAM,iBAAiB,OAAO,UAAU;AAGlC,WAAU,KAAK,OAAU;AACvB,QAAA,MAAM,eAAe,KAAK,KAAK;AACrC,WACE,QAAQ,uBACR,QAAQ,gCACR,QAAQ;AAAA,EAEZ;AAGM,WAAU,OAAO,OAAU;AAC/B,WAAO,eAAe,KAAK,KAAK,MAAM,qBAAqB,MAAM,gBAAgB;AAAA,EAEnF;AChBe,QAAA,QAAA;AAAA,IACb,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,WAAW;AAAA,IACX,MAAM;AAAA,IACN,KAAK;AAAA;ACLA,MAAM,MAAM,WAAA;AACjB,WAAO,KAAK,IAAA,EAAM,SAAS,EAAE,IAAI,KAAK,OAAA,EAAS,SAAS,EAAE,EAAE,MAAM,CAAC;AAAA,EACrE;ACYA,MAAA;AAAA;AAAA,IAAA,WAAA;AAAA,eAAAC,WAAA;AACsB,aAAA,MAAG,aAAa;AAErB,aAAA,KAAK;AACL,aAAA,KAAK;AAGL,aAAA,KAAK;AACL,aAAA,KAAK;AAAA,MAAA;AAYpBA,eAAA,UAAA,sBAAA,SAAoB,GAAW,GAAS;AACtC,aAAK,KAAK;AACV,aAAK,KAAK;AAAA,MACZ;AAGAA,eAAA,UAAA,qBAAA,SAAmB,GAAW,GAAS;AACrC,aAAK,KAAK;AACV,aAAK,KAAK;AAAA,MACZ;AAGAA,eAAA,UAAA,2BAAA,SAAyB,GAAW,GAAS;AAC3C,aAAK,KAAK;AACV,aAAK,KAAK;AAAA,MACZ;AAGAA,eAAA,UAAA,0BAAA,SAAwB,GAAW,GAAS;AAC1C,aAAK,KAAK;AACV,aAAK,KAAK;AAAA,MACZ;AAoCAA,eAAA,UAAA,OAAA,SACE,SACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IAAW;AAEP,YAAA,IAAY,IAAY,IAAY;AACpC,YAAA,IAAY,IAAY,IAAY;AAEpC,YAAA,UAAU,SAAS,GAAG;AAExB,eAAK,KAAK,KAAK;AACf,eAAK,KAAK,KAAK;AACf,eAAK,OAAE,QAAF,OAAE,SAAF,KAAM,KAAK;AAChB,eAAK,OAAE,QAAF,OAAE,SAAF,KAAM,KAAK;AAEhB,eAAK,KAAK,KAAK;AACf,eAAK,KAAK,KAAK;AACf,eAAK,OAAE,QAAF,OAAE,SAAF,KAAM,KAAK;AAChB,eAAK,OAAE,QAAF,OAAE,SAAF,KAAM,KAAK;AAAA,QAAA,WACR,UAAU,SAAS,GAAG;AAE9B,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AAEV,eAAK,KAAK,KAAK;AACf,eAAK,KAAK,KAAK;AACf,eAAK,OAAE,QAAF,OAAE,SAAF,KAAM,KAAK;AAChB,eAAK,OAAE,QAAF,OAAE,SAAF,KAAM,KAAK;AAAA,QAAA,OACX;AAEL,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AAEV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AAAA,QAAA;AAGP,aAAA,uBAAuB,SAAS,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACrE;AAcDA,aAAAA;AAAAA,IAAA,EAAA;AAAA;AChJD,MAAA;AAAA;AAAA,IAAA,SAAA,QAAA;AAAkC,gBAAOC,eAAA,MAAA;AAU3BA,eAAAA,cAAA,QAA6B,YAAmB;AAC1D,YAAA,QAAA,qBAAQ;AARO,cAAA,cAAc;AAKvB,cAAO,UAAG;AAIZ,YAAA,OAAO,WAAW,UAAU;AACzB,gBAAA,eAAe,QAAQ,UAAU;AAAA,QAAA;;;AAI1CA,oBAAA,UAAA,iBAAA,SAAe,OAA2B,YAAc;AAAd,YAAA,eAAA,QAAA;AAAc,uBAAA;AAAA,QAAA;AACtD,aAAK,UAAU;AACf,aAAK,cAAc;AAAA,MACrB;AAKAA,oBAAU,UAAA,aAAV,SAAW,SAAe;AACxB,aAAK,UAAU;AAAA,MACjB;AAEAA,oBAAA,UAAA,WAAA,WAAA;AACE,eAAO,KAAK,QAAQ,QAAQ,KAAK,eAAe,KAAK,UAAU,KAAK;AAAA,MACtE;AAEAA,oBAAA,UAAA,YAAA,WAAA;AACE,eAAO,KAAK,QAAQ,SAAS,KAAK,eAAe,KAAK,UAAU,KAAK;AAAA,MACvE;AAGAA,oBAAS,UAAA,YAAT,SAAU,SAAgC;AACjC,eAAA;AAAA,MACT;AAGAA,oBAAA,UAAA,yBAAA,SACE,SACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IAAU;AAEV,YAAM,QAAQ,KAAK;AACnB,YAAI,UAAU,QAAQ,OAAO,UAAU,UAAU;AAC/C;AAAA,QAAA;AAGG,aAAA,OAAE,QAAF,OAAE,SAAF,KAAM,KAAK,QAAQ,QAAQ,KAAK;AAChC,aAAA,OAAE,QAAF,OAAE,SAAF,KAAM,KAAK,QAAQ,SAAS,KAAK;AAEtC,aAAK,OAAA,QAAA,gBAAA,KAAM;AACX,aAAK,OAAA,QAAA,gBAAA,KAAM;AAEX,cAAM,KAAK;AACX,cAAM,KAAK;AAEL,YAAA,KAAK,KAAK,KAAK;AACf,YAAA,KAAK,KAAK,KAAK;AACf,YAAA,KAAK,KAAK,KAAK;AACf,YAAA,KAAK,KAAK,KAAK;AAEjB,YAAA;AACI,gBAAA;AAOE,kBAAA,UAAU,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,iBAChD,IAAI;AACP,cAAA,CAAC,KAAK,cAAc;AACd,oBAAA,IAAI,oBAAoB,KAAK;AACrC,oBAAQ,IAAI,EAAE;AACd,iBAAK,eAAe;AAAA,UAAA;AAAA,QACtB;AAAA,MAEJ;AACDA,aAAAA;AAAAA,IAAA,EA1FiC,OAAO;AAAA;ACVzC,MAAA;AAAA;AAAA,IAAA,SAAA,QAAA;AAAiC,gBAAOC,cAAA,MAAA;AAGtC,eAAAA,aAAY,QAAe;AACzB,YAAA,QAAA,qBAAQ;AACR,cAAK,UAAU;;;AAGjBA,mBAAgB,UAAA,mBAAhB,SAAiBC,UAAgB;AAC/B,aAAK,UAAUA;AAAA,MACjB;AAEAD,mBAAA,UAAA,WAAA,WAAA;;AACE,sBAAO,KAAA,KAAK,qCAAM,KAAK,QAAE,QAAA,OAAA,SAAA,KAAI,KAAK,QAAQ;MAC5C;AAEAA,mBAAA,UAAA,YAAA,WAAA;;AACE,sBAAO,KAAA,KAAK,qCAAM,KAAK,QAAE,QAAA,OAAA,SAAA,KAAI,KAAK,QAAQ;MAC5C;AAGAA,mBAAS,UAAA,YAAT,SAAU,SAAgC;AACjC,eAAA,KAAK,QAAQ,UAAU,OAAO;AAAA,MACvC;AAGAA,mBAAA,UAAA,yBAAA,SACE,SACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IAAU;AAEV,YAAMC,WAAU,KAAK;AACrB,YAAIA,aAAY,QAAQ,OAAOA,aAAY,UAAU;AACnD;AAAA,QAAA;AAGM,QAAAA,SAAA,KAAK,SAAS,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtD;AACDD,aAAAA;AAAAA,IAAA,EA5CgC,OAAO;AAAA;ACuDxC,MAAA;AAAA;AAAA,IAAA,SAAA,QAAA;AAA2B,gBAAYE,QAAA,MAAA;AASrC,eAAAA,OAAY,KAAyB;AAAzB,YAAA,QAAA,QAAA;AAAA,gBAAyB,CAAA;AAAA,QAAA;AACnC,YAAA,QAAA,qBAAQ;AA2CO,cAAA,oBAAG,SAACC,MAA2B;AAC9C,cAAM,MAAM,MAAK;AACjB,cAAM,MAAM,MAAK;AACjB,cAAM,OAAO,MAAK;AAElB,cAAI,CAACA,MAAK;AACD,mBAAA;AAAA,UAAA;AAGTA,iBAAM,OAAO,OAAO,CAAA,GAAIA,IAAG;AAEvB,cAAA,KAAK,GAAG,GAAG;AACbA,mBAAM,IAAIA,IAAG;AAAA,UAAA;AAGf,cAAI,OAAO,GAAG;AACZA,iBAAI,KAAK;AACTA,iBAAI,KAAK;AACTA,iBAAI,SAAS;AACbA,iBAAI,UAAU;AACdA,iBAAI,OAAO;AACXA,iBAAI,UAAU;AACdA,iBAAI,QAAQ;AACZA,iBAAI,SAAS;AAAA,UAAA;AAGf,cAAI,QAAQ,GAAG;AACbA,iBAAI,KAAK;AACTA,iBAAI,KAAK;AACTA,iBAAI,SAAS,IAAI;AACjBA,iBAAI,UAAU,IAAI;AAClBA,iBAAI,OAAO;AACXA,iBAAI,UAAU;AACdA,iBAAI,QAAQ;AACZA,iBAAI,SAAS;AAAA,UAAA;AAGT,cAAAF,WAAU,IAAI,YAAY,KAAI;AACpC,UAAAA,SAAQ,MAAME,KAAI;AAClB,UAAAF,SAAQ,SAASE,KAAI;AACrB,UAAAF,SAAQ,OAAOE,KAAI;AACnB,UAAAF,SAAQ,QAAQE,KAAI;AACpB,UAAAF,SAAQ,oBAAoBE,KAAI,GAAGA,KAAI,CAAC;AACxC,UAAAF,SAAQ,mBAAmBE,KAAI,OAAOA,KAAI,MAAM;AACzC,iBAAAF;AAAA,QACT;AAMoB,cAAA,uBAAG,SAAC,OAAa;AACnC,cAAM,WAAW,MAAK;AAEtB,cAAI,UAAU;AACR,gBAAA,KAAK,QAAQ,GAAG;AAClB,qBAAO,SAAS,KAAK;AAAA,YAAA,WACZ,OAAO,QAAQ,GAAG;AAC3B,qBAAO,SAAS,KAAK;AAAA,YAAA;AAAA,UACvB;AAAA,QAEJ;AAGM,cAAA,SAAG,SAAC,OAAc;AACtB,cAAI,CAAC,OAAO;AAEV,mBAAO,IAAI,iBAAiB,IAAI,YAAY,KAAI,CAAC;AAAA,UAAA;AAE7C,cAAA,oBAAoB,MAAK,qBAAqB,KAAK;AACzD,cAAI,mBAAmB;AACd,mBAAA,IAAI,iBAAiB,mBAAmB,KAAI;AAAA,UAAA;AAAA,QAEvD;AAlHE,cAAK,OAAO,IAAI;AAChB,cAAK,OAAO,IAAI,OAAO,IAAI,SAAS;AAC/B,cAAA,QAAQ,IAAI,QAAQ;AAEpB,cAAA,OAAO,IAAI,OAAO,IAAI;AAC3B,cAAK,YAAY,IAAI;AAErB,YAAI,OAAO,IAAI,UAAU,YAAY,OAAO,IAAI,KAAK,GAAG;AAClD,cAAA,SAAS,IAAI,OAAO;AACjB,kBAAA,YAAY,IAAI,MAAM;AAAA,UAAA,WAClB,SAAS,IAAI,OAAO;AACxB,kBAAA,YAAY,IAAI,MAAM;AAAA,UAAA;AAE7B,cAAI,OAAO,IAAI,MAAM,UAAU,UAAU;AAClC,kBAAA,cAAc,IAAI,MAAM;AAAA,UAAA;AAAA,QAC/B,OACK;AACD,cAAA,OAAO,IAAI,cAAc,UAAU;AACrC,kBAAK,YAAY,IAAI;AAAA,UACZ,WAAA,OAAO,IAAI,UAAU,UAAU;AACxC,kBAAK,YAAY,IAAI;AAAA,UAAA;AAEnB,cAAA,OAAO,IAAI,eAAe,UAAU;AACtC,kBAAK,cAAc,IAAI;AAAA,UAAA;AAAA,QACzB;AAGF,0BAAkB,GAAG;;;AAGjBC,aAAA,UAAA,OAAN,WAAA;;;;;;qBACM,KAAK,UAAS,QAAA,CAAA,GAAA,CAAA;AACF,uBAAA,CAAA,GAAM,eAAe,KAAK,SAAS,CAAC;AAAA;AAA5C,wBAAQ,GAAoC,KAAA;AAC7C,qBAAA,eAAe,OAAO,KAAK,WAAW;;;;;;;;;;MAE9C;AAgFFA,aAAAA;AAAAA,IAAA,EA/H0B,YAAY;AAAA;AAkIvC,WAAS,eAAe,KAAW;AACjC,YAAQ,SAAS,QAAQ,MAAM,oBAAoB,GAAG;AACtD,WAAO,IAAI,QAA0B,SAAU,SAAS,QAAM;AACtD,UAAA,MAAM,IAAI;AAChB,UAAI,SAAS,WAAA;AACX,gBAAQ,SAAS,QAAQ,MAAM,mBAAmB,GAAG;AACrD,gBAAQ,GAAG;AAAA,MACb;AACI,UAAA,UAAU,SAAU,OAAK;AACnB,gBAAA,MAAM,qBAAqB,GAAG;AACtC,eAAO,KAAK;AAAA,MACd;AACA,UAAI,MAAM;AAAA,IAAA,CACX;AAAA,EACH;AAGA,WAAS,kBAAkB,KAAoB;AAC7C,QAAI,YAAY;AAAK,cAAQ,KAAK,kDAAkD;AAGpF,QAAI,aAAa;AAAK,cAAQ,KAAK,mDAAmD;AAGtF,QAAI,aAAa;AAAK,cAAQ,KAAK,mDAAmD;AAGtF,QAAI,aAAa;AAAK,cAAQ,KAAK,mDAAmD;AAEtF,QAAI,WAAW;AAAK,cAAQ,KAAK,iDAAiD;AAElF,QAAI,eAAe;AAAK,cAAQ,KAAK,qDAAqD;AAE1F,QAAI,gBAAgB;AAAK,cAAQ,KAAK,sDAAsD;AAE5F,QAAI,OAAO,IAAI,UAAU,YAAY,SAAS,IAAI;AAChD,cAAQ,KAAK,qDAAqD;AAAA,EACtE;ACtMA,WAAS,wBAAwB,WAAc;AAC7C,WACE,OAAO,cAAc,YACrB,OAAO,SAAS,KAChB,aAAa,OAAO,UAAU,SAC9B,aAAa,OAAO,UAAU;AAAA,EAElC;AAOA,MAAA;AAAA;AAAA,IAAA,WAAA;AAGcE,eAAAA,kBAAA,WAAkCC,QAAa;AACzD,aAAK,YAAY;AACjB,aAAK,QAAQA;AAAAA,MAAA;AAOfD,wBAAA,UAAA,UAAA,SAAQ,WAAkC,UAAiB;AACzD,YAAI,CAAC,WAAW;AACP,iBAAA;AAAA,QACE,WAAA,MAAM,QAAQ,SAAS,GAAG;AACnC,iBAAO,KAAK,QAAQ,UAAU,CAAC,CAAC;AAAA,QAAA,WACvB,qBAAqB,SAAS;AAChC,iBAAA;AAAA,QAAA,WACE,wBAAwB,SAAS,GAAG;AACzC,cAAA,CAAC,KAAK,OAAO;AACR,mBAAA;AAAA,UAAA;AAEF,iBAAA,KAAK,MAAM,kBAAkB,SAAmC;AAAA,QAAA,WAEvE,OAAO,cAAc,YACrB,OAAO,SAAS,KAChB,OAAO,aAAa,aACpB;AACA,iBAAO,KAAK,QAAQ,UAAU,QAAQ,CAAC;AAAA,mBAC9B,OAAO,cAAc,cAAc,KAAK,SAAS,GAAG;AAC7D,iBAAO,KAAK,QAAQ,UAAU,QAAQ,CAAC;AAAA,QAAA,WAC9B,OAAO,cAAc,UAAU;AACpC,cAAA,CAAC,KAAK,OAAO;AACR,mBAAA;AAAA,UAAA;AAET,iBAAO,KAAK,QAAQ,KAAK,MAAM,qBAAqB,SAAS,CAAC;AAAA,QAAA;AAAA,MAElE;AAEAA,wBAAG,UAAA,MAAH,SAAI,UAAiB;AACnB,eAAO,KAAK,QAAQ,KAAK,WAAW,QAAQ;AAAA,MAC9C;AAEAA,wBAAK,UAAA,QAAL,SAAM,KAAe;AACnB,YAAM,QAAQ,MAAM,QAAQ,GAAG,IAAI,MAAM;AACzC,YAAI,MAAM,QAAQ,KAAK,SAAS,GAAG;AACjC,mBAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,kBAAM,CAAC,IAAI,KAAK,QAAQ,KAAK,UAAU,CAAC,CAAC;AAAA,UAAA;AAAA,QAC3C,OACK;AACL,gBAAM,CAAC,IAAI,KAAK,QAAQ,KAAK,SAAS;AAAA,QAAA;AAEjC,eAAA;AAAA,MACT;AACDA,aAAAA;AAAAA,IAAA,EAAA;AAAA;AAGD,MAAM,aAAa;AAAA,GAAI,SAAA,QAAA;AAAe,cAAO,SAAA,MAAA;AAqB3C,aAAA,UAAA;AACE,UAAA,QAAA,qBAAQ;AACH,YAAA,mBAAmB,GAAG,CAAC;;;AAtB9B,YAAA,UAAA,WAAA,WAAA;AACS,aAAA;AAAA,IACT;AACA,YAAA,UAAA,YAAA,WAAA;AACS,aAAA;AAAA,IACT;AACS,YAAA,UAAA,YAAT,SAAU,SAAgC;AACjC,aAAA;AAAA,IACT;AACsB,YAAA,UAAA,yBAAtB,SACE,SACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA;IACO;AAKT,YAAA,UAAA,sBAAA,SAAoB,GAAQ;IAAe;AAC3C,YAAA,UAAA,qBAAA,SAAmB,GAAQ;IAAe;AAC1C,YAAA,UAAA,2BAAA,SAAyB,GAAW;IAAkB;AACtD,YAAA,UAAA,0BAAA,SAAwB,GAAW;IAAkB;AACjD,YAAA,UAAA,OAAJ;IAAc;AACf,WAAA;AAAA,EAAD,EA9BsC,OAAO;AAiC7C,MAAM,eAAe,IAAI,iBAAiB,UAAU;AAGpD,MAAM,qBAA4C,CAAA;AAGlD,MAAM,cAAuB,CAAA;AAKvB,WAAgB,MAAM,KAA4B;;;;;;AAGtD,gBAAI,eAAe,OAAO;AACxBC,uBAAQ;AAAA,YAAA,OACH;AACLA,uBAAQ,IAAI,MAAM,GAAG;AAAA,YAAA;AAGvB,gBAAIA,OAAM,MAAM;AACKA,iCAAAA,OAAM,IAAI,IAAIA;AAAAA,YAAA;AAEnC,wBAAY,KAAKA,MAAK;AAEtB,mBAAA,CAAA,GAAMA,OAAM,MAAM;AAAA;AAAlB,eAAA,KAAA;AAEA,mBAAA,CAAA,GAAOA,MAAK;AAAA,QAAA;AAAA;;EACb;AAOK,WAAU,QAAQ,OAAqC;AACvD,QAAA,aAAa,OAAO,OAAO;AACtB,aAAA,IAAI,iBAAiB,KAAK;AAAA,IAAA;AAGnC,QAAI,SAA8C;AAG5C,QAAA,aAAa,MAAM,QAAQ,GAAG;AACpC,QAAI,aAAa,KAAK,MAAM,SAAS,aAAa,GAAG;AACnD,UAAM,UAAQ,mBAAmB,MAAM,MAAM,GAAG,UAAU,CAAC;AAC3D,eAAS,WAAS,QAAM,OAAO,MAAM,MAAM,aAAa,CAAC,CAAC;AAAA,IAAA;AAG5D,QAAI,CAAC,QAAQ;AAEL,UAAA,UAAQ,mBAAmB,KAAK;AAC7B,eAAA,WAAS,QAAM;;AAG1B,QAAI,CAAC,QAAQ;AAEX,eAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,iBAAS,YAAY,CAAC,EAAE,OAAO,KAAK;AACpC,YAAI,QAAQ;AACV;AAAA,QAAA;AAAA,MACF;AAAA,IACF;AAGF,QAAI,CAAC,QAAQ;AACH,cAAA,MAAM,wBAAwB,KAAK;AAClC,eAAA;AAAA,IAAA;AAGJ,WAAA;AAAA,EACT;ACtMA,MAAA;AAAA;AAAA,IAAA,SAAA,QAAA;AAAsC,gBAAOC,mBAAA,MAAA;AAM/BA,eAAAA,kBAAA,QAAiB,MAA0B;AACrD,YAAA,QAAA,qBAAQ;AACR,cAAK,UAAU;AACf,cAAK,cAAc;;;AAGrBA,wBAAA,UAAA,WAAA,WAAA;;AAES,gBAAA,KAAA,KAAK,QAAE,QAAA,OAAA,SAAA,KAAI,KAAK,QAAQ,SAAQ;AAAA,MACzC;AAEAA,wBAAA,UAAA,YAAA,WAAA;;AAES,gBAAA,KAAA,KAAK,QAAE,QAAA,OAAA,SAAA,KAAI,KAAK,QAAQ,UAAS;AAAA,MAC1C;AAGAA,wBAAS,UAAA,YAAT,SAAU,SAAgC;AACjC,eAAA;AAAA,MACT;AAEAA,wBAAA,UAAA,yBAAA,SACE,SACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IAAU;AAEV,YAAML,WAAU,KAAK;AACrB,YAAIA,aAAY,QAAQ,OAAOA,aAAY,UAAU;AACnD;AAAA,QAAA;AAGF,YAAI,WAAW;AACf,YAAI,YAAY;AAEhB,YAAM,OAAO,OAAO,SAASA,SAAQ,IAAI,IAAIA,SAAQ,OAAO;AAC5D,YAAM,QAAQ,OAAO,SAASA,SAAQ,KAAK,IAAIA,SAAQ,QAAQ;AAC/D,YAAM,MAAM,OAAO,SAASA,SAAQ,GAAG,IAAIA,SAAQ,MAAM;AACzD,YAAM,SAAS,OAAO,SAASA,SAAQ,MAAM,IAAIA,SAAQ,SAAS;AAElE,YAAM,QAAQA,SAAQ,aAAa,OAAO;AAC1C,YAAM,SAASA,SAAQ,cAAc,MAAM;AAEvC,YAAA,CAAC,KAAK,YAAY;AACpB,qBAAW,KAAK,IAAI,WAAW,OAAO,OAAO,CAAC;AAC9C,sBAAY,KAAK,IAAI,YAAY,MAAM,QAAQ,CAAC;AAAA,QAAA;AAI9C,YAAA,MAAM,KAAK,OAAO,GAAG;AACf,UAAAA,SAAA,KAAK,SAAS,GAAG,GAAG,MAAM,KAAK,GAAG,GAAG,MAAM,GAAG;AAAA,QAAA;AAEpD,YAAA,SAAS,KAAK,OAAO,GAAG;AAClB,UAAAA,SAAA,KAAK,SAAS,GAAG,SAAS,KAAK,MAAM,QAAQ,GAAG,YAAY,KAAK,MAAM,MAAM;AAAA,QAAA;AAEnF,YAAA,MAAM,KAAK,QAAQ,GAAG;AAChB,UAAAA,SAAA,KAAK,SAAS,QAAQ,MAAM,GAAG,OAAO,KAAK,WAAW,MAAM,GAAG,OAAO,GAAG;AAAA,QAAA;AAE/E,YAAA,SAAS,KAAK,QAAQ,GAAG;AAC3B,UAAAA,SAAQ,KACN,SACA,QAAQ,MACR,SAAS,KACT,OACA,QACA,WAAW,MACX,YAAY,KACZ,OACA,MAAM;AAAA,QAAA;AAIN,YAAA,KAAK,gBAAgB,WAAW;AAElC,cAAI,MAAM,GAAG;AACH,YAAAA,SAAA,KAAK,SAAS,MAAM,GAAG,OAAO,KAAK,MAAM,GAAG,UAAU,GAAG;AAAA,UAAA;AAEnE,cAAI,SAAS,GAAG;AACN,YAAAA,SAAA,KACN,SACA,MACA,SAAS,KACT,OACA,QACA,MACA,YAAY,KACZ,UACA,MAAM;AAAA,UAAA;AAGV,cAAI,OAAO,GAAG;AACJ,YAAAA,SAAA,KAAK,SAAS,GAAG,KAAK,MAAM,QAAQ,GAAG,KAAK,MAAM,SAAS;AAAA,UAAA;AAErE,cAAI,QAAQ,GAAG;AACL,YAAAA,SAAA,KACN,SACA,QAAQ,MACR,KACA,OACA,QACA,WAAW,MACX,KACA,OACA,SAAS;AAAA,UAAA;AAIL,UAAAA,SAAA,KAAK,SAAS,MAAM,KAAK,OAAO,QAAQ,MAAM,KAAK,UAAU,SAAS;AAAA,QAAA,WACrE,KAAK,gBAAgB,QAAQ;AAEtC,cAAI,IAAI;AACR,cAAI,IAAI;AACR,cAAI;AACJ,iBAAO,IAAI,GAAG;AACR,gBAAA,KAAK,IAAI,OAAO,CAAC;AAChB,iBAAA;AACL,gBAAI,IAAI;AACR,gBAAI,IAAI;AACR,gBAAI;AACJ,mBAAO,IAAI,GAAG;AACR,kBAAA,KAAK,IAAI,QAAQ,CAAC;AACjB,mBAAA;AACG,cAAAA,SAAA,KAAK,SAAS,MAAM,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACjD,kBAAI,KAAK,GAAG;AACV,oBAAI,MAAM;AACA,kBAAAA,SAAA,KAAK,SAAS,GAAG,KAAK,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC;AAAA,gBAAA;AAEtD,oBAAI,OAAO;AACD,kBAAAA,SAAA,KAAK,SAAS,QAAQ,MAAM,KAAK,OAAO,GAAG,IAAI,GAAG,GAAG,OAAO,CAAC;AAAA,gBAAA;AAAA,cACvE;AAEG,mBAAA;AAAA,YAAA;AAEP,gBAAI,KAAK;AACC,cAAAA,SAAA,KAAK,SAAS,MAAM,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG;AAAA,YAAA;AAErD,gBAAI,QAAQ;AACF,cAAAA,SAAA,KAAK,SAAS,MAAM,SAAS,KAAK,GAAG,QAAQ,GAAG,GAAG,GAAG,MAAM;AAAA,YAAA;AAEjE,iBAAA;AAAA,UAAA;AAAA,QACP;AAAA,MAEJ;AACDK,aAAAA;AAAAA,IAAA,EA1JqC,OAAO;AAAA;WCH7B,sBAAmB;AAEjC,WAAO,OAAO,WAAW,cAAc,OAAO,oBAAoB,IAAI;AAAA,EACxE;ACkBM,WAAU,eAAe,OAAa;AAC1C,WACE,UACC,UAAU,WACT,UAAU,aACV,UAAU,UACV,UAAU,QACV,UAAU,YACV,UAAU,SACV,UAAU;AAAA,EAEhB;AAEiB,MAAIC,QAAM;AA8B3B,MAAA;AAAA;AAAA,IAAA,WAAA;AA+DE,eAAAC,KAAY,OAAgB;AA9DR,aAAA,MAAG,SAAS;AA0Df,aAAA,cAAc;AACd,aAAA,cAAc;AAI7B,aAAK,SAAS;AACd,aAAK,UAAU;AAGV,aAAA,kBAAkB,IAAI;AAGtB,aAAA,kBAAkB,IAAI;AAE3B,aAAK,MAAK;AAAA,MAAA;AAGZA,WAAA,UAAA,QAAA,WAAA;AACE,aAAK,gBAAgB;AACrB,aAAK,SAAS;AAEd,aAAK,SAAS;AACd,aAAK,UAAU;AAEf,aAAK,UAAU;AACf,aAAK,UAAU;AACf,aAAK,SAAS;AACd,aAAK,SAAS;AACd,aAAK,YAAY;AAGjB,aAAK,WAAW;AAEhB,aAAK,UAAU;AACf,aAAK,UAAU;AAGf,aAAK,WAAW;AAChB,aAAK,WAAW;AAChB,aAAK,WAAW;AAGhB,aAAK,WAAW;AAChB,aAAK,UAAU;AACf,aAAK,UAAU;AAGf,aAAK,WAAW;AAChB,aAAK,WAAW;AAEhB,aAAK,QAAQ;AACb,aAAK,QAAQ;AACb,aAAK,YAAY,KAAK;AACtB,aAAK,aAAa,KAAK;AAGvB,aAAK,gBAAgB,EAAED;AACvB,aAAK,gBAAgB,EAAEA;AACvB,aAAK,aAAa,EAAEA;AAAAA,MACtB;AAGAC,WAAA,UAAA,UAAA,WAAA;AACE,aAAK,UAAU,KAAK,OAAO,WAAW,KAAK,OAAO,QAAQ;AAG1D,YAAI,KAAK,YAAY,KAAK,cAAc,KAAK,eAAe;AAC1D,eAAK,aAAa,KAAK;AACvB,eAAK,gBAAgB,EAAED;AAAAA,QAAA;AAGrB,YAAA,KAAK,YAAY,KAAK,WAAW,KAAK,aAAa,KAAK,QAAQ,eAAe;AAC5E,eAAA,YAAY,KAAK,QAAQ;AAC9B,eAAK,gBAAgB,EAAEA;AAAAA,QAAA;AAGlB,eAAA;AAAA,MACT;AAEAC,WAAA,UAAA,WAAA,WAAA;AACS,eAAA,KAAK,SAAS,QAAQ,KAAK,UAAU,KAAK,QAAQ,SAAS,QAAQ;AAAA,MAC5E;AAGAA,WAAA,UAAA,iBAAA,WAAA;AACE,aAAK,QAAO;AACZ,YAAM,KAAK,KAAK,IACd,KAAK,eACL,KAAK,eACL,KAAK,UAAU,KAAK,QAAQ,aAAa,CAAC;AAExC,YAAA,KAAK,WAAW,IAAI;AACtB,iBAAO,KAAK;AAAA,QAAA;AAEd,aAAK,UAAU;AAEf,YAAM,MAAM,KAAK;AACb,YAAA,MAAM,KAAK,gBAAgB;AAE/B,aAAK,WAAW,IAAI,OAAO,KAAK,QAAQ,eAAe;AAEvD,aAAK,aAAa,EAAED;AAEb,eAAA;AAAA,MACT;AAEAC,WAAA,UAAA,iBAAA,WAAA;AACE,aAAK,QAAO;AACZ,YAAM,KAAK,KAAK,IACd,KAAK,eACL,KAAK,eACL,KAAK,UAAU,KAAK,QAAQ,gBAAgB,CAAC;AAE3C,YAAA,KAAK,WAAW,IAAI;AACtB,iBAAO,KAAK;AAAA,QAAA;AAEd,aAAK,UAAU;AAEf,YAAM,MAAM,KAAK;AAEjB,YAAI,SAAQ;AACZ,YAAI,KAAK,UAAU;AACb,cAAA,UAAU,CAAC,KAAK,UAAU,KAAK,QAAQ,CAAC,KAAK,UAAU,KAAK,OAAO;AAAA,QAAA;AAErE,YAAA,MAAM,KAAK,UAAU,KAAK,aAAa,KAAK,UAAU,KAAK,WAAW;AAC1E,YAAI,KAAK,KAAK,QAAQ,KAAK,MAAM;AAC7B,YAAA,OAAO,KAAK,SAAS;AACzB,YAAI,KAAK,UAAU;AACb,cAAA,UAAU,KAAK,UAAU,KAAK,QAAQ,KAAK,UAAU,KAAK,OAAO;AAAA,QAAA;AAIvE,YAAI,KAAK,UAAU;AAEjB,eAAK,QAAQ;AACb,eAAK,QAAQ;AACb,eAAK,YAAY,KAAK;AACtB,eAAK,aAAa,KAAK;AAAA,QAAA,OAClB;AAEL,cAAI;AACJ,cAAI;AACC,cAAA,IAAI,IAAI,KAAK,IAAI,IAAI,KAAO,IAAI,IAAI,KAAK,IAAI,IAAI,GAAI;AACpD,gBAAA;AACJ,gBAAI,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK;AAAA,UAAA,OAClC;AACD,gBAAA,IAAI,IAAI,KAAK;AACb,gBAAA,IAAI,IAAI,KAAK;AAAA,UAAA;AAEnB,cAAI,IAAI,GAAG;AACT,iBAAK,QAAQ;AACb,iBAAK,YAAY,IAAI;AAAA,UAAA,OAChB;AACL,iBAAK,QAAQ;AACb,iBAAK,YAAY,IAAI;AAAA,UAAA;AAElB,cAAA,IAAI,IAAI,KAAK,IAAI,IAAI,KAAO,IAAI,IAAI,KAAK,IAAI,IAAI,GAAI;AACpD,gBAAA;AACJ,gBAAI,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK;AAAA,UAAA,OAClC;AACD,gBAAA,IAAI,IAAI,KAAK;AACb,gBAAA,IAAI,IAAI,KAAK;AAAA,UAAA;AAEnB,cAAI,IAAI,GAAG;AACT,iBAAK,QAAQ;AACb,iBAAK,aAAa,IAAI;AAAA,UAAA,OACjB;AACL,iBAAK,QAAQ;AACb,iBAAK,aAAa,IAAI;AAAA,UAAA;AAAA,QACxB;AAGF,aAAK,KAAK,KAAK;AACf,aAAK,KAAK,KAAK;AAEf,aAAK,MAAM,KAAK,QAAQ,KAAK,WAAW,KAAK,YAAY,KAAK;AAC9D,aAAK,MAAM,KAAK,QAAQ,KAAK,WAAW,KAAK,aAAa,KAAK;AAE3D,YAAA,KAAK,YAAY,KAAK,SAAS;AACjC,eAAK,QAAQ;AACb,eAAK,MAAM,KAAK,UAAU,KAAK,QAAQ;AACvC,eAAK,MAAM,KAAK,UAAU,KAAK,QAAQ;AAAA,QAAA;AAGzC,YAAI,UAAU,KAAK,IAAI,KAAK,EAAE;AAE9B,eAAO,KAAK;AAAA,MACd;AAGAA,WAAG,UAAA,MAAH,SAAI,KAAW;AACb,YAAI,OAAO,QAAQ,GAAG,MAAM,YAAY;AAC/B,iBAAA,QAAQ,GAAG,EAAE,IAAI;AAAA,QAAA;AAAA,MAE5B;AAIAA,WAAA,UAAA,MAAA,SAAI,GAAG,GAAE;AACH,YAAA,OAAO,MAAM,UAAU;AACzB,cAAI,OAAO,QAAQ,CAAC,MAAM,cAAc,OAAO,MAAM,aAAa;AACxD,oBAAA,CAAC,EAAE,MAAM,CAAC;AAAA,UAAA;AAAA,QACpB,WACS,OAAO,MAAM,UAAU;AAChC,eAAK,KAAK,GAAG;AACP,gBAAA,OAAO,QAAQ,CAAC,MAAM,cAAc,OAAO,EAAE,CAAC,MAAM,aAAa;AACnE,sBAAQ,CAAC,EAAE,MAAM,EAAE,CAAC,GAAG,CAAC;AAAA,YAAA;AAAA,UAC1B;AAAA,QACF;AAEF,YAAI,KAAK,QAAQ;AACV,eAAA,OAAO,UAAU,EAAED;AACxB,eAAK,OAAO;;AAEP,eAAA;AAAA,MACT;AAIAC,WAAA,UAAA,MAAA,SAAI,OAAsB,QAAuB,MAAc;AAC7D,aAAK,gBAAgB,EAAED;AACvB,YAAI,SAAS,WAAW;AACf,iBAAA;AAAA,QAAA;AAET,YAAI,SAAS,SAAS;AACb,iBAAA;AAAA,QAAA;AAEL,YAAA,OAAO,UAAU,UAAU;AACxB,eAAA,UAAU,QAAQ,KAAK;AAC5B,eAAK,SAAS,KAAK;AAAA,QAAA;AAEjB,YAAA,OAAO,WAAW,UAAU;AACzB,eAAA,UAAU,SAAS,KAAK;AAC7B,eAAK,UAAU,KAAK;AAAA,QAAA;AAElB,YAAA,OAAO,UAAU,YAAY,OAAO,WAAW,YAAY,OAAO,SAAS,UAAU;AACvF,cAAI,SAAS,OAAQ;AAAA,mBACV,SAAS,SAAS,SAAS,YAAY;AAC3C,iBAAA,UAAU,KAAK,UAAU,KAAK,IAAI,KAAK,SAAS,KAAK,OAAO;AAAA,UACxD,WAAA,SAAS,QAAQ,SAAS,UAAU;AACxC,iBAAA,UAAU,KAAK,UAAU,KAAK,IAAI,KAAK,SAAS,KAAK,OAAO;AAAA,UAAA;AAE/D,cAAA,SAAS,cAAc,SAAS,UAAU;AACvC,iBAAA,SAAS,QAAQ,KAAK;AACtB,iBAAA,UAAU,SAAS,KAAK;AAAA,UAAA;AAAA,QAC/B;AAAA,MAEJ;AACDC,aAAAA;AAAAA,IAAA,EAAA;AAAA;AAEgB,MAAM,UAAU;AAAA,IAC/B,OAAO,SAAU,KAAQ;AACvB,aAAO,IAAI;AAAA,IACb;AAAA,IAEA,cAAc,SAAU,KAAQ;AAC9B,aAAO,IAAI;AAAA,IACb;AAAA,IAEA,OAAO,SAAU,KAAQ;AACvB,aAAO,IAAI;AAAA,IACb;AAAA,IAEA,QAAQ,SAAU,KAAQ;AACxB,aAAO,IAAI;AAAA,IACb;AAAA,IAEA,UAAU,SAAU,KAAQ;AAC1B,aAAO,IAAI;AAAA,IACb;AAAA,IAEA,WAAW,SAAU,KAAQ;AAC3B,aAAO,IAAI;AAAA,IACb;AAAA;AAAA;AAAA,IAKA,QAAQ,SAAU,KAAQ;AACxB,aAAO,IAAI;AAAA,IACb;AAAA,IAEA,QAAQ,SAAU,KAAQ;AACxB,aAAO,IAAI;AAAA,IACb;AAAA;AAAA;AAAA,IAKA,OAAO,SAAU,KAAQ;AACvB,aAAO,IAAI;AAAA,IACb;AAAA,IAEA,OAAO,SAAU,KAAQ;AACvB,aAAO,IAAI;AAAA,IACb;AAAA,IAEA,UAAU,SAAU,KAAQ;AAC1B,aAAO,IAAI;AAAA,IACb;AAAA;AAAA;AAAA,IAKA,QAAQ,SAAU,KAAQ;AACxB,aAAO,IAAI;AAAA,IACb;AAAA,IAEA,QAAQ,SAAU,KAAQ;AACxB,aAAO,IAAI;AAAA,IACb;AAAA;AAAA;AAAA,IAKA,SAAS,SAAU,KAAQ;AACzB,aAAO,IAAI;AAAA,IACb;AAAA,IAEA,SAAS,SAAU,KAAQ;AACzB,aAAO,IAAI;AAAA,IACb;AAAA;AAAA;AAAA,IAKA,QAAQ,SAAU,KAAQ;AACxB,aAAO,IAAI;AAAA,IACb;AAAA,IAEA,QAAQ,SAAU,KAAQ;AACxB,aAAO,IAAI;AAAA,IACb;AAAA;AAAA;AAAA,IAKA,SAAS,SAAU,KAAQ;AACzB,aAAO,IAAI;AAAA,IACb;AAAA,IAEA,SAAS,SAAU,KAAQ;AACzB,aAAO,IAAI;AAAA,IAAA;AAAA;AAgBE,MAAM,UAAU;AAAA,IAC/B,OAAO,SAAU,KAAU,OAAa;AACtC,UAAI,SAAS;AAAA,IACf;AAAA,IAEA,cAAc,SAAU,KAAU,OAAa;AAC7C,UAAI,gBAAgB;AAAA,IACtB;AAAA,IAEA,OAAO,SAAU,KAAU,OAAa;AACtC,UAAI,kBAAkB;AACtB,UAAI,SAAS;AACb,UAAI,gBAAgB,EAAED;AAAAA,IACxB;AAAA,IAEA,QAAQ,SAAU,KAAU,OAAa;AACvC,UAAI,mBAAmB;AACvB,UAAI,UAAU;AACd,UAAI,gBAAgB,EAAEA;AAAAA,IACxB;AAAA,IAEA,OAAO,SAAU,KAAU,OAAa;AACtC,UAAI,UAAU;AACd,UAAI,UAAU;AACd,UAAI,gBAAgB,EAAEA;AAAAA,IACxB;AAAA,IAEA,QAAQ,SAAU,KAAU,OAAa;AACvC,UAAI,UAAU;AACd,UAAI,gBAAgB,EAAEA;AAAAA,IACxB;AAAA,IAEA,QAAQ,SAAU,KAAU,OAAa;AACvC,UAAI,UAAU;AACd,UAAI,gBAAgB,EAAEA;AAAAA,IACxB;AAAA,IAEA,MAAM,SAAU,KAAU,OAAa;AACrC,UAAI,SAAS;AACb,UAAI,SAAS;AACb,UAAI,gBAAgB,EAAEA;AAAAA,IACxB;AAAA,IAEA,OAAO,SAAU,KAAU,OAAa;AACtC,UAAI,SAAS;AACb,UAAI,gBAAgB,EAAEA;AAAAA,IACxB;AAAA,IAEA,OAAO,SAAU,KAAU,OAAa;AACtC,UAAI,SAAS;AACb,UAAI,gBAAgB,EAAEA;AAAAA,IACxB;AAAA,IAEA,UAAU,SAAU,KAAU,OAAa;AACzC,UAAI,YAAY;AAChB,UAAI,gBAAgB,EAAEA;AAAAA,IACxB;AAAA,IAEA,OAAO,SAAU,KAAU,OAAa;AACtC,UAAI,UAAU;AACd,UAAI,UAAU;AACd,UAAI,WAAW;AACf,UAAI,gBAAgB,EAAEA;AAAAA,IACxB;AAAA,IAEA,QAAQ,SAAU,KAAU,OAAa;AACvC,UAAI,UAAU;AACd,UAAI,WAAW;AACf,UAAI,gBAAgB,EAAEA;AAAAA,IACxB;AAAA,IAEA,QAAQ,SAAU,KAAU,OAAa;AACvC,UAAI,UAAU;AACd,UAAI,WAAW;AACf,UAAI,gBAAgB,EAAEA;AAAAA,IACxB;AAAA,IAEA,QAAQ,SAAU,KAAU,OAAa;AACvC,UAAI,WAAW;AACf,UAAI,WAAW;AACf,UAAI,gBAAgB,EAAEA;AAAAA,IACxB;AAAA,IAEA,SAAS,SAAU,KAAU,OAAa;AACxC,UAAI,WAAW;AACf,UAAI,gBAAgB,EAAEA;AAAAA,IACxB;AAAA,IAEA,SAAS,SAAU,KAAU,OAAa;AACxC,UAAI,WAAW;AACf,UAAI,gBAAgB,EAAEA;AAAAA,IACxB;AAAA,IAEA,OAAO,SAAU,KAAU,OAAa;AACjC,WAAA,OAAO,KAAK,KAAK;AACjB,WAAA,OAAO,KAAK,KAAK;AAAA,IACxB;AAAA,IAEA,QAAQ,SAAU,KAAU,OAAa;AACvC,UAAI,UAAU;AACd,UAAI,WAAW;AACf,UAAI,gBAAgB,EAAEA;AAEjB,WAAA,QAAQ,KAAK,KAAK;AAAA,IACzB;AAAA,IAEA,QAAQ,SAAU,KAAU,OAAa;AACvC,UAAI,UAAU;AACd,UAAI,WAAW;AACf,UAAI,gBAAgB,EAAEA;AAEjB,WAAA,QAAQ,KAAK,KAAK;AAAA,IACzB;AAAA,IAEA,QAAQ,SAAU,KAAU,OAAa;AAClC,WAAA,QAAQ,KAAK,KAAK;AAClB,WAAA,QAAQ,KAAK,KAAK;AAAA,IACzB;AAAA,IAEA,SAAS,SAAU,KAAU,OAAa;AACxC,UAAI,WAAW;AACf,UAAI,WAAW;AACf,UAAI,gBAAgB,EAAEA;AAAAA,IACxB;AAAA,IAEA,SAAS,SAAU,KAAU,OAAa;AACxC,UAAI,WAAW;AACf,UAAI,WAAW;AACf,UAAI,gBAAgB,EAAEA;AAAAA,IACxB;AAAA,IAEA,YAAY,SAAU,KAAU,OAAgB,KAAiB;AAC/D,UAAI,KAAK;AACP,YAAI,SAAS,MAAM;AACT,kBAAA;AAAA,QAAA,WACC,SAAS,OAAO;AACjB,kBAAA;AAAA,QAAA;AAEV,YAAI,IAAI,IAAI,aAAa,IAAI,cAAc,KAAK;AAAA,MAAA;AAAA,IAEpD;AAAA,IAEA,aAAa,SAAU,KAAU,OAAe,KAAiB;AAC/D,UAAI,CAAC,OAAO,CAAC,IAAI,YAAY;AACvB,YAAA,IAAI,OAAO,IAAI;AAAA,MAAA;AAAA,IAEvB;AAAA,IAEA,cAAc,SAAU,KAAU,OAAe,KAAiB;AAChE,UAAI,CAAC,OAAO,CAAC,IAAI,YAAY;AACvB,YAAA,IAAI,MAAM,KAAK;AAAA,MAAA;AAAA,IAEvB;AAAA,IAEA,WAAW,SAAU,KAAU,OAAgB,KAAgB;AAC7D,UAAI,KAAK;AACP,YAAI,IAAI,IAAI,YAAY,IAAI,aAAa,KAAK;AAAA,MAAA;AAAA,IAElD;AAAA,IAEA,YAAY,SAAU,KAAU,OAAe,KAAgB;AAC7D,UAAI,CAAC,OAAO,CAAC,IAAI,WAAW;AACtB,YAAA,IAAI,OAAO,IAAI;AAAA,MAAA;AAAA,IAEvB;AAAA,IAEA,aAAa,SAAU,KAAU,OAAe,KAAgB;AAC9D,UAAI,CAAC,OAAO,CAAC,IAAI,WAAW;AACtB,YAAA,IAAI,MAAM,KAAK;AAAA,MAAA;AAAA,IAEvB;AAAA,IAEA,QAAQ,SAAU,KAAU,OAAa;AAClC,WAAA,OAAO,KAAK,MAAM,CAAC;AACxB,WAAK,MAAM,KAAK,MAAM,IAAI,MAAM,CAAC;AACjC,WAAK,MAAM,KAAK,MAAM,IAAI,MAAM,CAAC;AAC5B,WAAA,OAAO,KAAK,MAAM,CAAC;AACnB,WAAA,QAAQ,KAAK,MAAM,CAAC;AACpB,WAAA,QAAQ,KAAK,MAAM,CAAC;AACpB,WAAA,SAAS,KAAK,CAAC;AAAA,IAAA;AAAA;ACppBxB,WAAS,SAAS,GAAM;AACf,WAAA;AAAA,EACT;AAmCiB,MAAM,eAA+C;AACrD,MAAM,eAA2C;AAGjD,MAAM,eAA2C;AAMlE,MAAA;AAAA;AAAA,IAAA,WAAA;AAAA,eAAAE,UAAA;AAAA,MAAA;AACSA,cAAA,MAAP,SACE,OACA,UAAyB;AAEzB,mBAAW,YAAY;AACnB,YAAA,OAAO,UAAU,YAAY;AACxB,iBAAA;AAAA,QAAA;AAEL,YAAA,OAAO,UAAU,UAAU;AACtB,iBAAA;AAAA,QAAA;AAEL,YAAA,SAAS,aAAa,KAAK;AAC/B,YAAI,QAAQ;AACH,iBAAA;AAAA,QAAA;AAEH,YAAA,SAAS,gDAAgD,KAAK,KAAK;AACzE,YAAI,CAAC,UAAU,CAAC,OAAO,QAAQ;AACtB,iBAAA;AAAA,QAAA;AAGH,YAAA,WAAW,OAAO,CAAC;AACnB,YAAA,SAAS,aAAa,QAAQ;AAE9B,YAAA,WAAW,OAAO,CAAC;AACnB,YAAA,SAAS,aAAa,QAAQ;AAE9B,YAAA,SAAS,OAAO,CAAC;AAEvB,YAAI,CAAC,QAAQ;AACF,mBAAA;AAAA,QAAA,WACA,QAAQ,UAAU,OAAO,OAAO,OAAO,YAAY;AAC5D,mBAAS,OAAO;AAAA,QAAA,WACP,QAAQ,UAAU,OAAO,OAAO,OAAO,YAAY;AACtD,cAAA,OAAO,SAAS,OAAO,QAAQ,OAAO,EAAE,EAAE,MAAM,GAAG,IAAI;AAC7D,mBAAS,OAAO,GAAG,MAAM,OAAO,IAAI,IAAI;AAAA,QAAA,OACnC;AACI,mBAAA;AAAA,QAAA;AAGX,YAAI,QAAQ;AACV,mBAAS,OAAO,MAAM;AAAA,QAAA;AAGxB,qBAAa,KAAK,IAAI;AAEf,eAAA;AAAA,MACT;AACDA,aAAAA;AAAAA,IAAA,EAAA;AAAA;AAGD,WAAS,QAAQ,MAAc,IAAc;AAC3C,iBAAa,IAAI,IAAI;AAAA,EACvB;AAGA,WAAS,UAAU,MAAgB;AACjC,QAAM,QAAQ,KAAK,KAAK,MAAM,KAAK;AACnC,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAC/B,UAAA,MAAM,MAAM,CAAC;AACnB,UAAI,KAAK;AACP,qBAAa,GAAG,IAAI;AAAA,MAAA;AAAA,IACtB;AAAA,EAEJ;AAEA,UAAQ,MAAM,SAAU,GAAiB;AAChC,WAAA;AAAA,EACT,CAAC;AAED,UAAQ,OAAO,SAAU,GAAiB;AACxC,WAAO,SAAU,GAAS;AACjB,aAAA,IAAI,EAAE,IAAI,CAAC;AAAA,IACpB;AAAA,EACF,CAAC;AAED,UAAQ,UAAU,SAAU,GAAiB;AAC3C,WAAO,SAAU,GAAS;AACxB,aAAO,IAAI,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE,KAAK,IAAI,EAAE,IAAI;AAAA,IACvD;AAAA,EACF,CAAC;AAED,UAAQ,UAAU,SAAU,GAAiB;AAC3C,WAAO,SAAU,GAAS;AACxB,aAAO,IAAI,MAAM,IAAI,EAAE,KAAK,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI;AAAA,IACvD;AAAA,EACF,CAAC;AAED,YAAU;AAAA,IACR,MAAM;AAAA,IACN,IAAI,SAAU,GAAS;AACd,aAAA;AAAA,IAAA;AAAA,EAEV,CAAA;AAED,YAAU;AAAA,IACR,MAAM;AAAA,IACN,IAAI,SAAU,GAAS;AACrB,aAAO,IAAI;AAAA,IAAA;AAAA,EAEd,CAAA;AAED,YAAU;AAAA,IACR,MAAM;AAAA,IACN,IAAI,SAAU,GAAS;AACrB,aAAO,IAAI,IAAI;AAAA,IAAA;AAAA,EAElB,CAAA;AAED,YAAU;AAAA,IACR,MAAM;AAAA,IACN,IAAI,SAAU,GAAS;AACd,aAAA,IAAI,IAAI,IAAI;AAAA,IAAA;AAAA,EAEtB,CAAA;AAED,YAAU;AAAA,IACR,MAAM;AAAA,IACN,IAAI,SAAU,GAAS;AACd,aAAA,IAAI,IAAI,IAAI,IAAI;AAAA,IAAA;AAAA,EAE1B,CAAA;AAED,YAAU;AAAA,IACR,MAAM;AAAA,IACN,IAAI,SAAU,GAAS;AACrB,aAAO,IAAI,KAAK,IAAK,IAAI,KAAK,KAAM,CAAC;AAAA,IAAA;AAAA,EAExC,CAAA;AAED,YAAU;AAAA,IACR,MAAM;AAAA,IACN,IAAI,SAAU,GAAS;AACd,aAAA,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,EAAE;AAAA,IAAA;AAAA,EAE/C,CAAA;AAED,YAAU;AAAA,IACR,MAAM;AAAA,IACN,IAAI,SAAU,GAAS;AACrB,aAAO,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC;AAAA,IAAA;AAAA,EAEjC,CAAA;AAED,YAAU;AAAA,IACR,MAAM;AAAA,IACN,IAAI,SAAU,GAAS;AACrB,aAAO,IAAI,IAAI,OACX,SAAS,IAAI,IACb,IAAI,IAAI,OACN,UAAU,KAAK,MAAM,QAAQ,IAAI,OACjC,IAAI,MAAM,OACR,UAAU,KAAK,OAAO,QAAQ,IAAI,SAClC,UAAU,KAAK,QAAQ,QAAQ,IAAI;AAAA,IAAA;AAAA,EAE9C,CAAA;AAED,YAAU;AAAA,IACR,MAAM;AAAA,IACN,IAAI,SAAU,GAAC;AACb,aAAO,SAAU,GAAS;AACjB,eAAA,KAAK,IAAI,GAAG,CAAC;AAAA,MACtB;AAAA,IAAA;AAAA,EAEH,CAAA;AAED,YAAU;AAAA,IACR,MAAM;AAAA,IACN,IAAI,SAAU,GAAG,GAAC;AAChB,UAAI,KAAK;AACT,UAAI,KAAK;AACH,UAAA,IAAK,KAAK,IAAI,KAAK,MAAO,KAAK,KAAK,IAAI,CAAC;AAC/C,aAAO,SAAU,GAAS;AACxB,eAAO,IAAI,IAAI,KAAK,IAAI,GAAG,MAAM,CAAC,IAAI,KAAK,KAAM,IAAI,MAAM,IAAI,KAAK,MAAO,CAAC;AAAA,MAC9E;AAAA,IAAA;AAAA,EAEH,CAAA;AAED,YAAU;AAAA,IACR,MAAM;AAAA,IACN,IAAI,SAAU,GAAC;AACT,UAAA,OAAO,MAAM,cAAc,IAAI;AACnC,aAAO,SAAU,GAAS;AACxB,eAAO,IAAI,MAAM,IAAI,KAAK,IAAI;AAAA,MAChC;AAAA,IAAA;AAAA,EAEH,CAAA;AC3ND,MAAA;AAAA;AAAA,IAAA,WAAA;AAqBcC,eAAAA,YAAA,OAAkB,SAA+B;AAA/B,YAAA,YAAA,QAAA;AAAA,oBAA+B,CAAA;AAAA,QAAA;AApBzC,aAAA,MAAG,gBAAgB;AAKtB,aAAA,UAAmC,CAAA;AAgBlD,aAAK,OAAO,CAAA;AACP,aAAA,YAAY,QAAQ,YAAY;AAChC,aAAA,SAAS,QAAQ,SAAS;AAE/B,aAAK,SAAS;AACd,aAAK,QAAQ;AAAA,MAAA;AAIfA,kBAAI,UAAA,OAAJ,SAAKC,YAAsB,SAAiB,KAAa,MAAY;AACnE,aAAK,SAAS;AAEV,YAAA,KAAK,QAAQ,KAAK,QAAQ;AAC5B;AAAA,QAAA;AAGI,YAAA,OAAO,KAAK,QAAQ,KAAK;AAE3B,YAAA,CAAC,KAAK,QAAQ;AAChB,eAAK,SAAS,CAAA;AACH,mBAAA,OAAO,KAAK,MAAM;AAC3B,iBAAK,OAAO,GAAG,IAAI,KAAK,OAAO,IAAI,GAAG;AAAA,UAAA;AAAA,QACxC;AAGF,YAAI,IAAI,KAAK,IAAI,OAAO,KAAK,WAAW,CAAC;AACzC,YAAM,QAAQ,KAAK;AAEf,YAAA,OAAO,KAAK,WAAW,YAAY;AACjC,cAAA,KAAK,QAAQ,CAAC;AAAA,QAAA;AAGpB,YAAM,IAAI,IAAI;AAEH,iBAAA,OAAO,KAAK,MAAM;AAC3B,eAAK,OAAO,IAAI,KAAK,KAAK,OAAO,GAAG,IAAI,IAAI,KAAK,KAAK,GAAG,IAAI,CAAC;AAAA,QAAA;AAGzD,eAAA;AAAA,MACT;AAGAD,kBAAA,UAAA,SAAA,WAAA;AAAA,YASC,QAAA;AARM,aAAA,QAAQ,QAAQ,SAAC,UAA+B;AAC/C,cAAA;AACO,qBAAA,KAAK,MAAK,MAAM;AAAA,mBAClB,GAAG;AACV,oBAAQ,MAAM,CAAC;AAAA,UAAA;AAAA,QACjB,CACD;AACD,eAAO,KAAK;AAAA,MACd;AAIAA,kBAAA,UAAA,QAAA,SAAM,GAAqB,GAAU;AAC/B,YAAA;AACJ,YAAI,OAAO,MAAM,YAAY,MAAM,MAAM;AAC7B,oBAAA;AAAA,QAAA,OACL;AACL,oBAAU;AACN,cAAA,OAAO,MAAM,UAAU;AACzB,oBAAQ,WAAW;AACf,gBAAA,OAAO,MAAM,UAAU;AACzB,sBAAQ,QAAQ;AAAA,YAAA;AAAA,UAClB;AAAA,QACF;AAGF,eAAQ,KAAK,QAAQ,IAAIA,YAAW,KAAK,QAAQ,OAAO;AAAA,MAC1D;AAEAA,kBAAQ,UAAA,WAAR,SAAS,UAAgB;AACvB,aAAK,YAAY;AACV,eAAA;AAAA,MACT;AAEAA,kBAAK,UAAA,QAAL,SAAM,OAAa;AACjB,aAAK,SAAS;AACP,eAAA;AAAA,MACT;AAEAA,kBAAI,UAAA,OAAJ,SAAK,QAA2C;AACzC,aAAA,UAAU,OAAO,IAAI,MAAM;AACzB,eAAA;AAAA,MACT;AAEAA,kBAAI,UAAA,OAAJ,SAAK,IAAyB;AACvB,aAAA,QAAQ,KAAK,EAAE;AACb,eAAA;AAAA,MACT;AAEAA,kBAAA,UAAA,OAAA,WAAA;AACO,aAAA,QAAQ,KAAK,WAAA;AAChB,eAAK,KAAI;AAAA,QAAA,CACV;AACD,aAAK,QAAQ;AACN,eAAA;AAAA,MACT;AAEAA,kBAAA,UAAA,SAAA,WAAA;AACO,aAAA,QAAQ,KAAK,WAAA;AAChB,eAAK,OAAM;AAAA,QAAA,CACZ;AACD,aAAK,UAAU;AACR,eAAA;AAAA,MACT;AAKAA,kBAAA,UAAA,MAAA,SAAI,GAAI,GAAE;AACJ,YAAA,OAAO,MAAM,UAAU;AACzB,mBAAW,QAAQ,GAAG;AACpB,oBAAQ,KAAK,QAAQ,KAAK,MAAM,MAAM,EAAE,IAAI,CAAC;AAAA,UAAA;AAAA,QAC/C,WACS,OAAO,MAAM,aAAa;AACnC,kBAAQ,KAAK,QAAQ,KAAK,MAAM,GAAG,CAAC;AAAA,QAAA;AAE/B,eAAA;AAAA,MACT;AAKAA,kBAAI,UAAA,OAAJ,SAAK,IAAyB;AAC5B,aAAK,KAAK,EAAE;AACL,eAAA;AAAA,MACT;AAKAA,kBAAK,UAAA,QAAL,SAAM,SAAgB;AACb,eAAA;AAAA,MACT;AAEAA,kBAAA,UAAA,OAAA,SAAK,GAAW,GAAS;AAElB,aAAA,IAAI,SAAS,CAAC;AACd,aAAA,IAAI,UAAU,CAAC;AACb,eAAA;AAAA,MACT;AAIAA,kBAAK,UAAA,QAAL,SAAM,GAAU;AAEV,YAAA,OAAO,MAAM,aAAa;AACrB,iBAAA,KAAK,IAAI,OAAO;AAAA,QAAA;AAEpB,aAAA,IAAI,SAAS,CAAC;AACZ,eAAA;AAAA,MACT;AAIAA,kBAAM,UAAA,SAAN,SAAO,GAAU;AAEX,YAAA,OAAO,MAAM,aAAa;AACrB,iBAAA,KAAK,IAAI,QAAQ;AAAA,QAAA;AAErB,aAAA,IAAI,UAAU,CAAC;AACb,eAAA;AAAA,MACT;AAIAA,kBAAA,UAAA,SAAA,SAAO,GAAuB,GAAU;AAElC,YAAA,OAAO,MAAM,UAAU;AACzB,cAAI,EAAE;AACN,cAAI,EAAE;AAAA,QAAA;AAEH,aAAA,IAAI,WAAW,CAAC;AAChB,aAAA,IAAI,WAAW,CAAC;AACd,eAAA;AAAA,MACT;AAEAA,kBAAM,UAAA,SAAN,SAAO,GAAS;AAET,aAAA,IAAI,YAAY,CAAC;AACf,eAAA;AAAA,MACT;AAIAA,kBAAA,UAAA,OAAA,SAAK,GAAuB,GAAU;AAEhC,YAAA,OAAO,MAAM,UAAU;AACzB,cAAI,EAAE;AACN,cAAI,EAAE;AAAA,QAAA,WACG,OAAO,MAAM,aAAa;AAC/B,cAAA;AAAA,QAAA;AAED,aAAA,IAAI,SAAS,CAAC;AACd,aAAA,IAAI,SAAS,CAAC;AACZ,eAAA;AAAA,MACT;AAKAA,kBAAA,UAAA,QAAA,SAAM,GAAuB,GAAU;AAEjC,YAAA,OAAO,MAAM,UAAU;AACzB,cAAI,EAAE;AACN,cAAI,EAAE;AAAA,QAAA,WACG,OAAO,MAAM,aAAa;AAC/B,cAAA;AAAA,QAAA;AAED,aAAA,IAAI,UAAU,CAAC;AACf,aAAA,IAAI,UAAU,CAAC;AACb,eAAA;AAAA,MACT;AAEAA,kBAAA,UAAA,QAAA,SAAM,GAAW,IAAW;AAErB,aAAA,IAAI,SAAS,CAAC;AACf,YAAA,OAAO,OAAO,aAAa;AACxB,eAAA,IAAI,gBAAgB,EAAE;AAAA,QAAA;AAEtB,eAAA;AAAA,MACT;AACDA,aAAAA;AAAAA,IAAA,EAAA;AAAA;AAGD,WAAS,QAAQC,YAAsB,KAAa,KAAa,OAAa;AAC5E,QAAI,OAAOA,WAAU,IAAI,GAAG,MAAM,UAAU;AAC1C,UAAI,GAAG,IAAI;AAAA,IACF,WAAA,OAAOA,WAAU,IAAI,MAAM,GAAG,MAAM,YAAY,OAAOA,WAAU,IAAI,MAAM,GAAG,MAAM,UAAU;AACnG,UAAA,MAAM,GAAG,IAAI;AACb,UAAA,MAAM,GAAG,IAAI;AAAA,IAAA;AAAA,EAErB;ACrQA,MAAI,MAAM;AACV,QAAM,SAAS;AAGf,WAAS,WAAc,KAAM;AACvB,QAAA,OAAO,eAAe,WAAW;AAC5B,aAAA;AAAA,IAAA;AAET,UAAM,wBAAwB;AAAA,EAChC;WAmBgB,SAAM;AACpB,WAAO;EACT;WAGgB,QAAK;AACnB,WAAO;EACT;WAGgB,MAAG;AACjB,WAAO;EACT;WAMgB,SAAM;AACpB,WAAO;EACT;WAEgB,YAAS;AACvB,WAAO,IAAI,UAAS;AAAA,EACtB;AAEM,WAAU,IAAI,OAAa;AAC/B,WAAO,IAAI,UAAW,EAAC,IAAI,KAAK,EAAE,MAAM,KAAK;AAAA,EAC/C;AAEM,WAAU,OAAO,OAAa;AAClC,WAAO,IAAI,UAAW,EAAC,OAAO,KAAK,EAAE,MAAM,QAAQ;AAAA,EACrD;WAEgB,WAAQ;AACtB,WAAO,IAAI,UAAS,EAAG,SAAU,EAAC,MAAM,UAAU;AAAA,EACpD;WAEgB,WAAQ;AACtB,WAAO,IAAI,UAAS,EAAG,SAAU,EAAC,MAAM,UAAU;AAAA,EACpD;AASA,MAAA;AAAA;AAAA,IAAA,WAAA;AA6CE,eAAAC,aAAA;AAAA,YAKC,QAAA;AAjDmB,aAAA,MAAG,eAAe;AAErB,aAAA,SAAS;AAET,aAAA,UAA4B;AAC5B,aAAA,QAA0B;AAC1B,aAAA,QAA0B;AAE1B,aAAA,SAA2B;AAC3B,aAAA,QAA0B;AAE1B,aAAA,WAAW;AAGX,aAAA,SAAiB;AAEjB,aAAA,WAAmB;AACnB,aAAA,WAAmB;oBAEZ,IAAI,IAAI,IAAI;AAQnB,aAAA,aAAkE,CAAA;AAClE,aAAA,SAA8B,CAAA;AAC9B,aAAA,SAA8B,CAAA;AAC9B,aAAA,eAA6B,CAAA;AAE7B,aAAA,cAA4C,CAAA;AAC5C,aAAA,aAA2C,CAAA;AAK5D,aAAU,aAAG;AAygBL,aAAc,iBAAG;AA8WR,aAAA,yBAAyB;AACzB,aAAA,0BAA0B;AAE3C,aAAA,kBAAkB,SAAC,SAAiB,KAAa,MAAY;AACvD,cAAA,CAAC,MAAK,aAAa,QAAQ;AACtB,mBAAA;AAAA,UAAA;AAIH,cAAA,SAAS,MAAK,4BAA4B;AAChD,gBAAK,0BAA0B;AAC/B,cAAI,QAAQ;AACH,mBAAA;AAAA,UAAA;AAGH,cAAA,OAAO,MAAK,aAAa,CAAC;AAEhC,cAAM,QAAQ,KAAK,KAAK,OAAM,SAAS,KAAK,IAAI;AAGhD,cAAI,OAAO;AACT,gBAAI,SAAS,MAAK,aAAa,CAAC,GAAG;AACjC,oBAAK,aAAa;;AAEd,gBAAA,OAAO,KAAK;AAClB,gBAAI,MAAM;AACH,oBAAA,aAAa,QAAQ,IAAI;AAAA,YAAA;AAAA,UAChC;AAGK,iBAAA;AAAA,QACT;AA/4BQ,cAAA;AACN,YAAI,gBAAgBA,YAAW;AACxB,eAAA,MAAM,KAAK,YAAY,IAAI;AAAA,QAAA;AAAA,MAClC;AAGFA,iBAAM,UAAA,SAAN,SAAO,UAAgB;AAAhB,YAAA,aAAA,QAAA;AAAgB,qBAAA;AAAA,QAAA;AACrB,YAAI,aAAa,MAAM;AACd,iBAAA,KAAK,KAAK;;AAEZ,eAAA,KAAK,KAAK;MACnB;AAGAA,iBAAA,UAAA,gBAAA,WAAA;;AAEQ,YAAA,KAAI,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,OAAM;AAC9B,YAAM,aAAa,CAAC,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,KAAK,IAAI,EAAE,CAAC,CAAC,IAAI;AAC9D,eAAA;AAAA,MACT;AAGAA,iBAAA,UAAA,sBAAA,WAAA;;AAEQ,YAAA,gBAAe,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,OAAM;AACzC,YAAM,aAAa,CAAC,eAChB,IACA,KAAK,IAAI,KAAK,IAAI,aAAa,CAAC,GAAG,KAAK,IAAI,aAAa,CAAC,CAAC;AACxD,eAAA;AAAA,MACT;AAGAA,iBAAA,UAAA,uBAAA,WAAA;AACS,eAAA,KAAK,wBAAwB;MACtC;AAMAA,iBAAA,UAAA,MAAA,SAAI,GAAqB,GAAO;AAC1B,YAAA,OAAO,MAAM,UAAU;AACpB,eAAA,KAAK,IAAI,CAAC;AACR,iBAAA;AAAA,QAAA,WACE,OAAO,MAAM,UAAU;AAC5B,cAAA,OAAO,MAAM,aAAa;AACrB,mBAAA,KAAK,KAAK,IAAI,CAAC;AAAA,UAAA,OACjB;AACA,iBAAA,KAAK,IAAI,GAAG,CAAC;AACX,mBAAA;AAAA,UAAA;AAAA,QACT,WACS,OAAO,MAAM,aAAa;AACnC,iBAAO,KAAK;AAAA,QAAA;AAAA,MAEhB;AAIAA,iBAAA,UAAA,MAAA,SAAI,GAAG,GAAI,GAAE;AACP,YAAA,OAAO,MAAM,UAAU;AACrB,cAAA;AACJ,cAAI,EAAE;AACN,cAAI,EAAE;AAAA,QAAA;AAER,aAAK,KAAK,IAAI,GAAG,GAAG,CAAC;AACd,eAAA;AAAA,MACT;AAGAA,iBAAA,UAAA,UAAA,SAAQ,GAAG,GAAI,GAAE;AACf,eAAO,KAAK,IAAI,GAAG,GAAG,CAAC;AAAA,MACzB;AAEAA,iBAAA,UAAA,WAAA,WAAA;AACS,eAAA,MAAM,KAAK,SAAS;AAAA,MAC7B;AAOAA,iBAAE,UAAA,KAAF,SAAG,OAAc;AACX,YAAA,OAAO,UAAU,aAAa;AAChC,iBAAO,KAAK;AAAA,QAAA;AAEd,aAAK,SAAS;AACP,eAAA;AAAA,MACT;AAIAA,iBAAK,UAAA,QAAL,SAAM,OAAc;AACd,YAAA,OAAO,UAAU,aAAa;AAChC,iBAAO,KAAK;AAAA,QAAA;AAEd,aAAK,SAAS;AACP,eAAA;AAAA,MACT;AAIAA,iBAAA,UAAA,OAAA,SAAK,MAAc,OAAW;AACxB,YAAA,OAAO,UAAU,aAAa;AAChC,iBAAO,KAAK,WAAW,OAAO,KAAK,OAAO,IAAI,IAAI;AAAA,QAAA;AAEnD,SAAA,KAAK,WAAW,OAAO,KAAK,SAAU,KAAK,SAAS,CAAA,GAAK,IAAI,IAAI;AAC3D,eAAA;AAAA,MACT;AAIAA,iBAAO,UAAA,UAAP,SAAQ,SAAiB;AACnB,YAAA,OAAO,YAAY,aAAa;AAClC,iBAAO,KAAK;AAAA,QAAA;AAEd,aAAK,WAAW;AAChB,aAAK,YAAY,KAAK,QAAQ,eAAe,EAAE;AAC/C,aAAK,UAAU,EAAE;AACjB,aAAK,MAAK;AACH,eAAA;AAAA,MACT;AAEAA,iBAAA,UAAA,OAAA,WAAA;AACE,aAAK,QAAQ,KAAK;AACX,eAAA;AAAA,MACT;AAEAA,iBAAA,UAAA,OAAA,WAAA;AACE,aAAK,QAAQ,IAAI;AACV,eAAA;AAAA,MACT;AAEAA,iBAAA,UAAA,SAAA,WAAA;AACE,eAAO,KAAK;AAAA,MACd;AAEAA,iBAAI,UAAA,OAAJ,SAAK,SAAiB;AACpB,YAAI,OAAO,KAAK;AAChB,eAAO,QAAQ,WAAW,CAAC,KAAK,UAAU;AACxC,iBAAO,KAAK;AAAA,QAAA;AAEP,eAAA;AAAA,MACT;AAEAA,iBAAI,UAAA,OAAJ,SAAK,SAAiB;AACpB,YAAI,OAAO,KAAK;AAChB,eAAO,QAAQ,WAAW,CAAC,KAAK,UAAU;AACxC,iBAAO,KAAK;AAAA,QAAA;AAEP,eAAA;AAAA,MACT;AAEAA,iBAAK,UAAA,QAAL,SAAM,SAAiB;AACrB,YAAI,OAAO,KAAK;AAChB,eAAO,QAAQ,WAAW,CAAC,KAAK,UAAU;AACxC,iBAAO,KAAK;AAAA,QAAA;AAEP,eAAA;AAAA,MACT;AAEAA,iBAAI,UAAA,OAAJ,SAAK,SAAiB;AACpB,YAAI,OAAO,KAAK;AAChB,eAAO,QAAQ,WAAW,CAAC,KAAK,UAAU;AACxC,iBAAO,KAAK;AAAA,QAAA;AAEP,eAAA;AAAA,MACT;AAEAA,iBAAA,UAAA,QAAA,SAAS,SAA8B,SAAW;AAChD,YAAM,UAAU,QAAQ;AACxB,YAAM,UAAU,QAAQ;AACxB,YAAI,QAAQ,SAAS,QAAQ,MAAM,MAAM,OAAO,GAAG;AACjD;AAAA,QAAA;AAEE,YAAA;AACA,YAAA,OAAO,UAAU,KAAK,KAAK,OAAO,IAAI,KAAK,MAAM,OAAO;AAC5D,eAAQ,QAAQ,MAAO;AACrB,iBAAO,UAAU,MAAM,KAAK,OAAO,IAAI,MAAM,KAAK,OAAO;AACzD,cAAI,MAAM,MAAM,SAAS,OAAO,GAAG;AAC1B,mBAAA;AAAA,UAAA;AAAA,QACT;AAEF,eAAO,QAAQ,OAAO,QAAQ,IAAI,MAAM,OAAO;AAAA,MACjD;AAIAA,iBAAA,UAAA,SAAA,SAAO,OAAgC,MAAgB;AACjD,YAAA,MAAM,QAAQ,KAAK,GAAG;AACxB,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrCA,uBAAU,OAAO,MAAM,MAAM,CAAC,CAAC;AAAA,UAAA;AAAA,QACjC,WACS,OAAO,SAAS,aAAa;AAEtC,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzCA,uBAAU,OAAO,MAAM,UAAU,CAAC,CAAC;AAAA,UAAA;AAAA,QACrC,WACS,OAAO,UAAU;AAAaA,qBAAU,OAAO,MAAM,KAAK;AAE9D,eAAA;AAAA,MACT;AAIAA,iBAAA,UAAA,UAAA,SAAQ,OAAgC,MAAgB;AAClD,YAAA,MAAM,QAAQ,KAAK,GAAG;AACxB,mBAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1CA,uBAAU,QAAQ,MAAM,MAAM,CAAC,CAAC;AAAA,UAAA;AAAA,QAClC,WACS,OAAO,SAAS,aAAa;AAEtC,mBAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC9CA,uBAAU,QAAQ,MAAM,UAAU,CAAC,CAAC;AAAA,UAAA;AAAA,QACtC,WACS,OAAO,UAAU;AAAaA,qBAAU,QAAQ,MAAM,KAAK;AAE/D,eAAA;AAAA,MACT;AAEAA,iBAAQ,UAAA,WAAR,SAAS,QAAiB;AACxBA,mBAAU,OAAO,QAAQ,IAAI;AACtB,eAAA;AAAA,MACT;AAEAA,iBAAS,UAAA,YAAT,SAAU,QAAiB;AACzBA,mBAAU,QAAQ,QAAQ,IAAI;AACvB,eAAA;AAAA,MACT;AAEAA,iBAAA,UAAA,aAAA,SAAW,SAAoB,MAAgB;AACzC,YAAA,MAAM,QAAQ,OAAO,GAAG;AAC1B,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvCA,uBAAU,YAAY,QAAQ,CAAC,GAAG,IAAI;AAAA,UAAA;AAAA,QACxC,WACS,OAAO,SAAS,aAAa;AAEtC,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzCA,uBAAU,YAAY,UAAU,CAAC,GAAG,IAAI;AAAA,UAAA;AAAA,QAC1C,WACS,OAAO,YAAY,aAAa;AACzCA,qBAAU,YAAY,SAAS,IAAI;AAAA,QAAA;AAG9B,eAAA;AAAA,MACT;AAEAA,iBAAA,UAAA,aAAA,SAAW,SAAoB,MAAgB;AACzC,YAAA,MAAM,QAAQ,OAAO,GAAG;AAC1B,mBAAS,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AAC5CA,uBAAU,aAAa,QAAQ,CAAC,GAAG,IAAI;AAAA,UAAA;AAAA,QACzC,WACS,OAAO,SAAS,aAAa;AAEtC,mBAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC9CA,uBAAU,aAAa,UAAU,CAAC,GAAG,IAAI;AAAA,UAAA;AAAA,QAC3C,WACS,OAAO,YAAY,aAAa;AACzCA,qBAAU,aAAa,SAAS,IAAI;AAAA,QAAA;AAG/B,eAAA;AAAA,MACT;AAEAA,iBAAW,UAAA,cAAX,SAAY,MAAe;AACzBA,mBAAU,YAAY,MAAM,IAAI;AACzB,eAAA;AAAA,MACT;AAEAA,iBAAY,UAAA,eAAZ,SAAa,MAAe;AAC1BA,mBAAU,aAAa,MAAM,IAAI;AAC1B,eAAA;AAAA,MACT;AAGOA,iBAAA,SAAP,SAAc,QAAmB,OAAgB;AAC/C,mBAAW,KAAK;AAChB,mBAAW,MAAM;AAEjB,cAAM,OAAM;AAEZ,YAAI,OAAO,OAAO;AAChB,iBAAO,MAAM,QAAQ;AACrB,gBAAM,QAAQ,OAAO;AAAA,QAAA;AAGvB,cAAM,UAAU;AAChB,eAAO,QAAQ;AAEX,YAAA,CAAC,OAAO,QAAQ;AAClB,iBAAO,SAAS;AAAA,QAAA;AAGZ,cAAA,QAAQ,MAAM,OAAO,IAAI;AAE/B,cAAM,aAAa,EAAE;AACrB,eAAO,eAAe,EAAE;AACxB,eAAO,MAAK;AAAA,MACd;AAGOA,iBAAA,UAAP,SAAe,QAAmB,OAAgB;AAChD,mBAAW,KAAK;AAChB,mBAAW,MAAM;AAEjB,cAAM,OAAM;AAEZ,YAAI,OAAO,QAAQ;AACjB,iBAAO,OAAO,QAAQ;AACtB,gBAAM,QAAQ,OAAO;AAAA,QAAA;AAGvB,cAAM,UAAU;AAChB,eAAO,SAAS;AAEZ,YAAA,CAAC,OAAO,OAAO;AACjB,iBAAO,QAAQ;AAAA,QAAA;AAGX,cAAA,QAAQ,MAAM,OAAO,IAAI;AAE/B,cAAM,aAAa,EAAE;AACrB,eAAO,eAAe,EAAE;AACxB,eAAO,MAAK;AAAA,MACd;AAGOA,iBAAA,eAAP,SAAoBC,OAAiB,MAAe;AAClD,mBAAWA,KAAI;AACf,mBAAW,IAAI;AAEf,QAAAA,MAAK,OAAM;AAEX,YAAM,SAAS,KAAK;AACpB,YAAM,OAAO,KAAK;AAElB,YAAI,CAAC,QAAQ;AACX;AAAA,QAAA;AAGF,aAAK,QAAQA;AAEZ,iBAAS,KAAK,QAAQA,UAAW,WAAW,OAAO,SAASA;AAE7D,QAAAA,MAAK,UAAU;AACf,QAAAA,MAAK,QAAQ;AACb,QAAAA,MAAK,QAAQ;AAER,QAAAA,MAAA,QAAQ,MAAMA,OAAM,IAAI;AAE7B,QAAAA,MAAK,aAAa,EAAE;AACpB,QAAAA,MAAK,MAAK;AAAA,MACZ;AAGOD,iBAAA,cAAP,SAAmBC,OAAiB,MAAe;AACjD,mBAAWA,KAAI;AACf,mBAAW,IAAI;AAEf,QAAAA,MAAK,OAAM;AAEX,YAAM,SAAS,KAAK;AACpB,YAAM,OAAO,KAAK;AAElB,YAAI,CAAC,QAAQ;AACX;AAAA,QAAA;AAGF,aAAK,QAAQA;AAEZ,iBAAS,KAAK,QAAQA,UAAW,WAAW,OAAO,QAAQA;AAE5D,QAAAA,MAAK,UAAU;AACf,QAAAA,MAAK,QAAQ;AACb,QAAAA,MAAK,QAAQ;AAER,QAAAA,MAAA,QAAQ,MAAMA,OAAM,IAAI;AAE7B,QAAAA,MAAK,aAAa,EAAE;AACpB,QAAAA,MAAK,MAAK;AAAA,MACZ;AAEAD,iBAAA,UAAA,SAAA,SAAO,OAAmB,MAAU;AAC9B,YAAA,OAAO,UAAU,aAAa;AAC5B,cAAA,MAAM,QAAQ,KAAK,GAAG;AACxB,qBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,yBAAW,MAAM,CAAC,CAAC,EAAE,OAAM;AAAA,YAAA;AAAA,UAC7B,WACS,OAAO,SAAS,aAAa;AACtC,qBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,yBAAW,UAAU,CAAC,CAAC,EAAE,OAAM;AAAA,YAAA;AAAA,UACjC,OACK;AACM,uBAAA,KAAK,EAAE;;AAEb,iBAAA;AAAA,QAAA;AAGT,YAAI,KAAK,OAAO;AACT,eAAA,MAAM,QAAQ,KAAK;AAAA,QAAA;AAE1B,YAAI,KAAK,OAAO;AACT,eAAA,MAAM,QAAQ,KAAK;AAAA,QAAA;AAG1B,YAAI,KAAK,SAAS;AACZ,cAAA,KAAK,QAAQ,WAAW,MAAM;AAC3B,iBAAA,QAAQ,SAAS,KAAK;AAAA,UAAA;AAEzB,cAAA,KAAK,QAAQ,UAAU,MAAM;AAC1B,iBAAA,QAAQ,QAAQ,KAAK;AAAA,UAAA;AAGvB,eAAA,QAAQ,MAAM,MAAM,KAAK;AAEzB,eAAA,QAAQ,eAAe,EAAE;AAC9B,eAAK,QAAQ;;AAGf,aAAK,QAAQ,KAAK,QAAQ,KAAK,UAAU;AACzC,aAAK,aAAa,EAAE;AAEb,eAAA;AAAA,MACT;AAEAA,iBAAA,UAAA,QAAA,WAAA;AACE,YAAI,QAA0B;AAC9B,YAAI,OAAO,KAAK;AAChB,eAAQ,QAAQ,MAAO;AACrB,iBAAO,MAAM;AACb,gBAAM,QAAQ,MAAM,QAAQ,MAAM,UAAU;AAEvC,eAAA,MAAM,OAAO,KAAK;AAAA,QAAA;AAGpB,aAAA,SAAS,KAAK,QAAQ;AAE3B,aAAK,eAAe,EAAE;AACtB,aAAK,MAAK;AACH,eAAA;AAAA,MACT;AAEAA,iBAAA,UAAA,QAAA,WAAA;AACE,aAAK,YAAY,EAAE;AACd,aAAA,WAAW,KAAK,QAAQ,MAAK;AAC3B,eAAA;AAAA,MACT;AASAA,iBAAA,UAAA,QAAA,SAAM,KAAyB,OAAe;AACxC,YAAA,OAAO,UAAU,aAAa;AAChC,iBAAQ,KAAK,WAAW,QAAQ,KAAK,OAAO,GAAa,KAAM;AAAA,QAAA;AAE7D,YAAA,OAAO,QAAQ,UAAU;AAC3B,cAAI,OAAO;AACJ,iBAAA,SAAS,KAAK,UAAU;AAC7B,gBAAI,CAAC,KAAK,OAAO,GAAG,KAAK,KAAK,SAAS;AAChC,mBAAA,QAAQ,MAAM,KAAK,IAAI;AAAA,YAAA;AAE9B,iBAAK,OAAO,GAAG,KAAK,KAAK,OAAO,GAAG,KAAK,KAAK;AAAA,UAAA,WACpC,KAAK,UAAU,KAAK,OAAO,GAAG,IAAI,GAAG;AAC9C,gBAAI,KAAK,OAAO,GAAG,KAAK,KAAK,KAAK,SAAS;AACpC,mBAAA,QAAQ,MAAM,KAAK,KAAK;AAAA,YAAA;AAE/B,iBAAK,OAAO,GAAG,IAAI,KAAK,OAAO,GAAG,IAAI;AAAA,UAAA;AAAA,QACxC;AAEE,YAAA,OAAO,QAAQ,UAAU;AAC3B,cAAI,IAAI,QAAQ;AACH,qBAAA,QAAQ,IAAI,QAAQ;AAC7B,kBAAI,IAAI,OAAO,IAAI,IAAI,GAAG;AACnB,qBAAA,MAAM,MAAM,KAAK;AAAA,cAAA;AAAA,YACxB;AAAA,UACF;AAAA,QACF;AAEK,eAAA;AAAA,MACT;AAGAA,iBAAO,UAAA,UAAP,SAAQ,KAAc;AACd,YAAA,QAAQ,KAAK,KAAK;AAClB,YAAA,SAAS,KAAK,KAAK;AAClB,eAAA,IAAI,KAAK,KAAK,IAAI,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,MAChE;AAGAA,iBAAA,UAAA,YAAA,WAAA;AACM,YAAA,CAAC,KAAK,UAAU;AAClB;AAAA,QAAA;AAGF,aAAK,iBAAgB;AAEjB,YAAA;AACJ,YAAI,OAAO,KAAK;AAChB,eAAQ,QAAQ,MAAO;AACrB,iBAAO,MAAM;AACb,gBAAM,UAAS;AAAA,QAAA;AAAA,MAEnB;AAGAA,iBAAA,UAAA,mBAAA,WAAA;AAAA,MAEA;AAMAA,iBAAM,UAAA,SAAN,SAAO,SAAiC;AAClC,YAAA,CAAC,KAAK,UAAU;AAClB;AAAA,QAAA;AAEI,cAAA;AAEA,YAAA,IAAI,KAAK;AACf,gBAAQ,aAAa,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAG5C,aAAA,SAAS,KAAK,KAAK,UAAU,KAAK,UAAU,KAAK,QAAQ,SAAS;AACvE,YAAM,QAAQ,KAAK,KAAK,gBAAgB,KAAK;AAEzC,YAAA,QAAQ,eAAe,OAAO;AAChC,kBAAQ,cAAc;AAAA,QAAA;AAGpB,YAAA,CAAC,KAAK,gBAAgB;AAExB,eAAK,iBAAgB;AAAA,QAAA;AAEvB,aAAK,iBAAiB;AAEtB,aAAK,cAAc,OAAO;AAEtB,YAAA,QAAQ,eAAe,KAAK,QAAQ;AACtC,kBAAQ,cAAc,KAAK;AAAA,QAAA;AAGzB,YAAA;AACJ,YAAI,OAAO,KAAK;AAChB,eAAQ,QAAQ,MAAO;AACrB,iBAAO,MAAM;AACb,gBAAM,OAAO,OAAO;AAAA,QAAA;AAAA,MAExB;AAGAA,iBAAa,UAAA,gBAAb,SAAc,SAAiC;AAAA,MAE/C;AAGAA,iBAAA,UAAA,QAAA,SAAM,SAAiB,KAAa,MAAY;AAC1C,YAAA,CAAC,KAAK,UAAU;AAClB;AAAA,QAAA;AAGE,YAAA,UAAU,KAAK,YAAY;AAC7B,oBAAU,KAAK;AAAA,QAAA;AAGjB,YAAI,SAAS;AAET,YAAA,KAAK,gBAAgB,MAAM;AAC7B,mBAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAC1C,kBAAA;AACA,gBAAA,SAAS,KAAK,YAAY,CAAC;AACjC,qBAAS,OAAO,KAAK,MAAM,SAAS,KAAK,IAAI,MAAM,QAAQ;AAAA,UAAA;AAAA,QAC7D;AAGE,YAAA;AACJ,YAAI,OAAO,KAAK;AAChB,eAAQ,QAAQ,MAAO;AACrB,iBAAO,MAAM;AACT,cAAA,MAAM,MAAM,OAAO,GAAG;AACxB,qBAAS,MAAM,MAAM,SAAS,KAAK,IAAI,MAAM,OAAO,OAAO;AAAA,UAAA;AAAA,QAC7D;AAGE,YAAA,KAAK,eAAe,MAAM;AAC5B,mBAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AACzC,kBAAA;AACA,gBAAA,SAAS,KAAK,WAAW,CAAC;AAChC,qBAAS,OAAO,KAAK,MAAM,SAAS,KAAK,IAAI,MAAM,QAAQ;AAAA,UAAA;AAAA,QAC7D;AAGK,eAAA;AAAA,MACT;AAEAA,iBAAA,UAAA,OAAA,SAAK,UAAuC,QAAc;;AAAd,YAAA,WAAA,QAAA;AAAc,mBAAA;AAAA,QAAA;AACpD,YAAA,OAAO,aAAa,YAAY;AAClC;AAAA,QAAA;AAEF,YAAI,QAAQ;AACN,cAAA,KAAK,gBAAgB,MAAM;AAC7B,iBAAK,cAAc,CAAA;AAAA,UAAA;AAEhB,eAAA,YAAY,KAAK,QAAQ;AAAA,QAAA,OACzB;AACD,cAAA,KAAK,eAAe,MAAM;AAC5B,iBAAK,aAAa,CAAA;AAAA,UAAA;AAEf,eAAA,WAAW,KAAK,QAAQ;AAAA,QAAA;AAE/B,YAAM,oBAAkB,KAAA,KAAK,oDAAY,UAAS,YAAK,KAAK,iBAAa,QAAA,OAAA,SAAA,SAAA,GAAA,UAAS;AAC7E,aAAA,MAAM,SAAS,eAAe;AAAA,MACrC;AAEAA,iBAAM,UAAA,SAAN,SAAO,UAAqC;AACtC,YAAA,OAAO,aAAa,YAAY;AAClC;AAAA,QAAA;AAEE,YAAA;AACA,YAAA,KAAK,gBAAgB,SAAS,IAAI,KAAK,YAAY,QAAQ,QAAQ,MAAM,GAAG;AACzE,eAAA,YAAY,OAAO,GAAG,CAAC;AAAA,QAAA;AAE1B,YAAA,KAAK,eAAe,SAAS,IAAI,KAAK,WAAW,QAAQ,QAAQ,MAAM,GAAG;AACvE,eAAA,WAAW,OAAO,GAAG,CAAC;AAAA,QAAA;AAAA,MAE/B;AAEAA,iBAAA,UAAA,UAAA,SAAQ,UAAqB,MAAY;AAClC,aAAA,WAAW,UAAU,IAAI;AAAA,MAChC;AAEAA,iBAAA,UAAA,aAAA,SAAW,UAAqB,MAAY;AAC1C,iBAAS,MAAM,GAAS;AACjB,eAAA,QAAQ,KAAK,GAAG;AACnB,iBAAK,OAAO,KAAK;AACjB,qBAAS,KAAK,IAAI;AAAA,UAAA,OACb;AACE,mBAAA;AAAA,UAAA;AAAA,QACT;AAEF,aAAK,KAAK,KAAK;AACR,eAAA;AAAA,MACT;AAEAA,iBAAY,UAAA,eAAZ,SAAa,OAAkC;AAC7C,aAAK,OAAO,KAAK;AAAA,MACnB;AAKAA,iBAAA,UAAA,KAAA,SAAG,MAAyB,UAAsC;AAChE,YAAI,CAAC,QAAQ,CAAC,KAAK,UAAU,OAAO,aAAa,YAAY;AACpD,iBAAA;AAAA,QAAA;AAET,YAAI,OAAO,SAAS,YAAY,OAAO,KAAK,SAAS,YAAY;AAE/D,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,iBAAK,GAAG,KAAK,CAAC,GAAG,QAAQ;AAAA,UAAA;AAAA,QAC3B,WACS,OAAO,SAAS,YAAY,KAAK,QAAQ,GAAG,IAAI,IAAI;AAEtD,iBAAA,KAAK,MAAM,MAAM;AACxB,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,iBAAK,IAAI,KAAK,CAAC,GAAG,QAAQ;AAAA,UAAA;AAAA,QAC5B,WACS,OAAO,SAAS,UAAU;AAC9B,eAAA,IAAI,MAAM,QAAQ;AAAA,QAAA,MAClB;AAGA,eAAA;AAAA,MACT;AAGAA,iBAAA,UAAA,MAAA,SAAI,MAAc,UAAsC;AACtD,YAAI,OAAO,SAAS,YAAY,OAAO,aAAa,YAAY;AAC9D;AAAA,QAAA;AAEF,aAAK,WAAW,IAAI,IAAI,KAAK,WAAW,IAAI,KAAK;AACjD,aAAK,WAAW,IAAI,EAAE,KAAK,QAAQ;AAE9B,aAAA,MAAM,MAAM,IAAI;AAAA,MACvB;AAKAA,iBAAA,UAAA,MAAA,SAAI,MAAyB,UAAsC;AACjE,YAAI,CAAC,QAAQ,CAAC,KAAK,UAAU,OAAO,aAAa,YAAY;AACpD,iBAAA;AAAA,QAAA;AAET,YAAI,OAAO,SAAS,YAAY,OAAO,KAAK,SAAS,YAAY;AAE/D,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,iBAAK,IAAI,KAAK,CAAC,GAAG,QAAQ;AAAA,UAAA;AAAA,QAC5B,WACS,OAAO,SAAS,YAAY,KAAK,QAAQ,GAAG,IAAI,IAAI;AAEtD,iBAAA,KAAK,MAAM,MAAM;AACxB,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,iBAAK,KAAK,KAAK,CAAC,GAAG,QAAQ;AAAA,UAAA;AAAA,QAC7B,WACS,OAAO,SAAS,UAAU;AAC9B,eAAA,KAAK,MAAM,QAAQ;AAAA,QAAA,MACnB;AAGA,eAAA;AAAA,MACT;AAGAA,iBAAA,UAAA,OAAA,SAAK,MAAc,UAAsC;AACvD,YAAI,OAAO,SAAS,YAAY,OAAO,aAAa,YAAY;AAC9D;AAAA,QAAA;AAEI,YAAA,YAAY,KAAK,WAAW,IAAI;AACtC,YAAI,CAAC,aAAa,CAAC,UAAU,QAAQ;AACnC;AAAA,QAAA;AAEI,YAAA,QAAQ,UAAU,QAAQ,QAAQ;AACxC,YAAI,SAAS,GAAG;AACJ,oBAAA,OAAO,OAAO,CAAC;AAKpB,eAAA,MAAM,MAAM,KAAK;AAAA,QAAA;AAAA,MAE1B;AAEAA,iBAAS,UAAA,YAAT,SAAU,MAAY;AACb,eAAA,KAAK,WAAW,IAAI;AAAA,MAC7B;AAEAA,iBAAA,UAAA,UAAA,SAAQ,MAAc,MAAU;AACxB,YAAA,YAAY,KAAK,UAAU,IAAI;AACrC,YAAI,CAAC,aAAa,CAAC,UAAU,QAAQ;AAC5B,iBAAA;AAAA,QAAA;AAET,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,oBAAU,CAAC,EAAE,MAAM,MAAM,IAAI;AAAA,QAAA;AAE/B,eAAO,UAAU;AAAA,MACnB;AAGAA,iBAAA,UAAA,UAAA,SAAQ,MAAc,MAAU;AACzB,aAAA,QAAQ,MAAM,IAAI;AAChB,eAAA;AAAA,MACT;AAEAA,iBAAA,UAAA,OAAA,SAAK,GAAW,GAAS;AAElB,aAAA,IAAI,SAAS,CAAC;AACd,aAAA,IAAI,UAAU,CAAC;AACb,eAAA;AAAA,MACT;AAIAA,iBAAK,UAAA,QAAL,SAAM,GAAU;AAEV,YAAA,OAAO,MAAM,aAAa;AACrB,iBAAA,KAAK,IAAI,OAAO;AAAA,QAAA;AAEpB,aAAA,IAAI,SAAS,CAAC;AACZ,eAAA;AAAA,MACT;AAIAA,iBAAM,UAAA,SAAN,SAAO,GAAU;AAEX,YAAA,OAAO,MAAM,aAAa;AACrB,iBAAA,KAAK,IAAI,QAAQ;AAAA,QAAA;AAErB,aAAA,IAAI,UAAU,CAAC;AACb,eAAA;AAAA,MACT;AAIAA,iBAAA,UAAA,SAAA,SAAO,GAAwB,GAAU;AAEnC,YAAA,OAAO,MAAM,UAAU;AACzB,cAAI,EAAE;AACN,cAAI,EAAE;AAAA,QAAA;AAEH,aAAA,IAAI,WAAW,CAAC;AAChB,aAAA,IAAI,WAAW,CAAC;AACd,eAAA;AAAA,MACT;AAEAA,iBAAM,UAAA,SAAN,SAAO,GAAS;AAET,aAAA,IAAI,YAAY,CAAC;AACf,eAAA;AAAA,MACT;AAIAA,iBAAA,UAAA,OAAA,SAAK,GAAwB,GAAU;AAEjC,YAAA,OAAO,MAAM,UAAU;AACzB,cAAI,EAAE;AACN,cAAI,EAAE;AAAA,QAAA,WACG,OAAO,MAAM;AAAiB,cAAA;AACpC,aAAA,IAAI,SAAS,CAAC;AACd,aAAA,IAAI,SAAS,CAAC;AACZ,eAAA;AAAA,MACT;AAKAA,iBAAA,UAAA,QAAA,SAAM,GAAwB,GAAU;AAElC,YAAA,OAAO,MAAM,UAAU;AACzB,cAAI,EAAE;AACN,cAAI,EAAE;AAAA,QAAA,WACG,OAAO,MAAM;AAAiB,cAAA;AACpC,aAAA,IAAI,UAAU,CAAC;AACf,aAAA,IAAI,UAAU,CAAC;AACb,eAAA;AAAA,MACT;AAEAA,iBAAA,UAAA,QAAA,SAAM,GAAW,IAAW;AAErB,aAAA,IAAI,SAAS,CAAC;AACf,YAAA,OAAO,OAAO,aAAa;AACxB,eAAA,IAAI,gBAAgB,EAAE;AAAA,QAAA;AAEtB,eAAA;AAAA,MACT;AAIAA,iBAAA,UAAA,QAAA,SAAM,GAAqB,GAAY,GAAW;AAC5C,YAAA;AACJ,YAAI,OAAO,MAAM,YAAY,MAAM,MAAM;AAC7B,oBAAA;AAAA,QAAA,OACL;AACL,oBAAU;AACN,cAAA,OAAO,MAAM,UAAU;AACzB,oBAAQ,WAAW;AACf,gBAAA,OAAO,MAAM,UAAU;AACzB,sBAAQ,QAAQ;AACZ,kBAAA,OAAO,MAAM,WAAW;AAC1B,wBAAQ,SAAS;AAAA,cAAA;AAAA,YACnB,WACS,OAAO,MAAM,WAAW;AACjC,sBAAQ,SAAS;AAAA,YAAA;AAAA,UACnB,WACS,OAAO,MAAM,WAAW;AACjC,oBAAQ,SAAS;AAAA,UAAA;AAAA,QACnB;AAGE,YAAA,CAAC,KAAK,wBAAwB;AAC3B,eAAA,KAAK,KAAK,iBAAiB,IAAI;AACpC,eAAK,yBAAyB;AAAA,QAAA;AAGhC,aAAK,MAAK;AAGN,YAAA,CAAC,QAAQ,QAAQ;AACnB,eAAK,aAAa,SAAS;AAAA,QAAA;AAG7B,YAAM,aAAa,IAAI,WAAW,MAAM,OAAO;AAC1C,aAAA,aAAa,KAAK,UAAU;AAC1B,eAAA;AAAA,MACT;AAmCAA,iBAAG,UAAA,MAAH,SAAI,OAAa;AACV,aAAA,MAAM,OAAO,KAAK;AAChB,eAAA;AAAA,MACT;AAEAA,iBAAM,UAAA,SAAN,SAAO,OAAa;AACb,aAAA,MAAM,UAAU,KAAK;AACnB,eAAA;AAAA,MACT;AAEAA,iBAAA,UAAA,QAAA,SAAM,MAAwB,OAAa;AAA3C,YAkDC,QAAA;AAjDC,aAAK,WAAW,KAAK;AACrB,aAAK,WAAW,KAAK;AAErB,aAAK,iBAAiB,KAAK,OAAO,KAAK,aAAa;AAC/C,aAAA,KACF,KAAK,gBAAgB,WAAA;AAChB,cAAA,MAAK,WAAW,MAAK,WAAW;AAClC;AAAA,UAAA;AAEF,gBAAK,UAAU,MAAK;AAEd,cAAA,gBAAgB,MAAK,gBAAgB,MAAK;AAChD,gBAAK,eAAe,MAAK;AAEzB,cAAI,QAAQ;AACZ,cAAI,SAAS;AAET,cAAA;AACA,cAAA,OAAO,MAAK,MAAM,IAAI;AAC1B,cAAI,QAAQ;AACZ,iBAAQ,QAAQ,MAAO;AACd,mBAAA,MAAM,KAAK,IAAI;AAEtB,kBAAM,OAAO,IAAI;AACX,gBAAA,IAAI,MAAM,IAAI,UAAU;AACxB,gBAAA,IAAI,MAAM,IAAI,WAAW;AAE/B,gBAAI,QAAQ,UAAU;AACnB,eAAA,UAAU,UAAU,MAAK;AAC1B,oBAAM,IAAI,SAAS,KAAK,UAAU,MAAM,IAAI,WAAW,MAAM;AACrD,sBAAA,KAAK,IAAI,OAAO,CAAC;AACzB,uBAAS,SAAS;AACD,+BAAA,MAAM,IAAI,UAAU,KAAK;AAAA,YAAA,WACjC,QAAQ,OAAO;AACvB,eAAA,UAAU,SAAS,MAAK;AACzB,oBAAM,IAAI,SAAS,KAAK,SAAS,MAAM,IAAI,WAAW,KAAK;AAC3D,sBAAQ,QAAQ;AACP,uBAAA,KAAK,IAAI,QAAQ,CAAC;AACV,+BAAA,MAAM,IAAI,UAAU,KAAK;AAAA,YAAA;AAEpC,oBAAA;AAAA,UAAA;AAEV,mBAAS,IAAI,MAAK;AAClB,oBAAU,IAAI,MAAK;AACnB,gBAAK,IAAI,OAAO,KAAK,SAAS,MAAK,IAAI,SAAS,KAAK;AACrD,gBAAK,IAAI,QAAQ,KAAK,UAAU,MAAK,IAAI,UAAU,MAAM;AAAA,QAAA,CACzD;AAEG,eAAA;AAAA,MACT;AAGAA,iBAAA,UAAA,MAAA,WAAA;AACE,eAAO,KAAK,SAAQ;AAAA,MACtB;AAGAA,iBAAA,UAAA,QAAA,WAAA;AACE,eAAO,KAAK,SAAQ;AAAA,MACtB;AAKAA,iBAAA,UAAA,WAAA,WAAA;AAAA,YA8BC,QAAA;AA7BC,aAAK,WAAW,KAAK;AAErB,aAAK,iBAAiB,KAAK,OAAO,KAAK,aAAa;AAC/C,aAAA,KACF,KAAK,gBAAgB,WAAA;AAChB,cAAA,MAAK,WAAW,MAAK,WAAW;AAClC;AAAA,UAAA;AAEF,gBAAK,UAAU,MAAK;AAEpB,cAAI,QAAQ;AACZ,cAAI,SAAS;AACT,cAAA;AACA,cAAA,OAAO,MAAK,MAAM,IAAI;AAC1B,iBAAQ,QAAQ,MAAO;AACd,mBAAA,MAAM,KAAK,IAAI;AACtB,kBAAM,OAAO,IAAI;AACX,gBAAA,IAAI,MAAM,IAAI,UAAU;AACxB,gBAAA,IAAI,MAAM,IAAI,WAAW;AACvB,oBAAA,KAAK,IAAI,OAAO,CAAC;AAChB,qBAAA,KAAK,IAAI,QAAQ,CAAC;AAAA,UAAA;AAE7B,mBAAS,IAAI,MAAK;AAClB,oBAAU,IAAI,MAAK;AACnB,gBAAK,IAAI,OAAO,KAAK,SAAS,MAAK,IAAI,SAAS,KAAK;AACrD,gBAAK,IAAI,QAAQ,KAAK,UAAU,MAAK,IAAI,UAAU,MAAM;AAAA,QAAA,CACzD;AAEG,eAAA;AAAA,MACT;AAKAA,iBAAA,UAAA,WAAA,WAAA;AAAA,YAmBC,QAAA;AAlBC,aAAK,iBAAiB,KAAK,OAAO,KAAK,aAAa;AAC/C,aAAA,KACF,KAAK,gBAAgB,WAAA;AACd,cAAA,SAAS,MAAK;AACpB,cAAI,QAAQ;AACJ,gBAAA,QAAQ,OAAO,IAAI,OAAO;AAChC,gBAAI,MAAK,IAAI,OAAO,KAAK,OAAO;AACzB,oBAAA,IAAI,SAAS,KAAK;AAAA,YAAA;AAEnB,gBAAA,SAAS,OAAO,IAAI,QAAQ;AAClC,gBAAI,MAAK,IAAI,QAAQ,KAAK,QAAQ;AAC3B,oBAAA,IAAI,UAAU,MAAM;AAAA,YAAA;AAAA,UAC3B;AAAA,WAGJ,IAAI;AAEC,eAAA;AAAA,MACT;AAMAA,iBAAO,UAAA,UAAP,SAAQ,KAAW;AACjB,aAAK,WAAW;AACT,eAAA;AAAA,MACT;AAKAA,iBAAO,UAAA,UAAP,SAAQ,OAAa;AACnB,aAAK,WAAW;AACT,eAAA;AAAA,MACT;AACDA,aAAAA;AAAAA,IAAA,EAAA;AAAA;AC9pCK,WAAU,OAAO,OAA6B;AAC5CE,QAAAA,UAAS,IAAI;AACVA,aAAAA,QAAO,QAAQ,KAAK;AACtBA,WAAAA;AAAAA,EACT;AAEA,MAAA;AAAA;AAAA,IAAA,SAAA,QAAA;AAA4B,gBAASC,SAAA,MAAA;AAOnC,eAAAA,UAAA;AACE,YAAA,QAAA,qBAAQ;AAPO,cAAA,WAA2B;AAE3B,cAAA,SAAyB;AACzB,cAAA,SAAkB;AAClB,cAAA,aAAsB;AAiD/B,cAAgB,mBAAG;AA7CzB,cAAK,MAAM,QAAQ;;;AAGrBA,cAAO,UAAA,UAAP,SAAQ,OAA4B;AAClC,aAAK,SAAS,QAAQ,KAAK,EAAE,IAAG;AAChC,YAAI,KAAK,QAAQ;AACf,eAAK,IAAI,SAAS,KAAK,OAAO,UAAU;AACxC,eAAK,IAAI,UAAU,KAAK,OAAO,WAAW;AAG1C,cAAI,KAAK,QAAQ;AACf,iBAAK,WAAW,IAAI,iBAAiB,KAAK,QAAQ,MAAM;AAAA,UAAA,WAC/C,KAAK,YAAY;AAC1B,iBAAK,WAAW,IAAI,iBAAiB,KAAK,QAAQ,SAAS;AAAA,UAAA,OACtD;AACL,iBAAK,WAAW,IAAI,YAAY,KAAK,MAAM;AAAA,UAAA;AAAA,QAC7C,OACK;AACA,eAAA,IAAI,SAAS,CAAC;AACd,eAAA,IAAI,UAAU,CAAC;AACpB,eAAK,WAAW;AAAA,QAAA;AAEX,eAAA;AAAA,MACT;AAGAA,cAAK,UAAA,QAAL,SAAM,OAA4B;AACzB,eAAA,KAAK,QAAQ,KAAK;AAAA,MAC3B;AAEAA,cAAI,UAAA,OAAJ,SAAK,OAAa;AAChB,aAAK,SAAS;AACd,YAAMd,WAAU,IAAI,iBAAiB,KAAK,QAAQ,MAAM;AACxD,aAAK,WAAWA;AACT,eAAA;AAAA,MACT;AAEAc,cAAO,UAAA,UAAP,SAAQ,OAAa;AACnB,aAAK,aAAa;AAClB,YAAMd,WAAU,IAAI,iBAAiB,KAAK,QAAQ,SAAS;AAC3D,aAAK,WAAWA;AACT,eAAA;AAAA,MACT;AAMAc,cAAA,UAAA,mBAAA,WAAA;AACE,YAAI,CAAC,KAAK;AAAQ;AACZ,YAAA,aAAa,KAAK;AACxB,aAAK,iBAAiB,aAAa;AACnC,YAAM,UAAU,KAAK,OAAO,UAAU,KAAK,gBAAgB;AAC3D,YAAI,YAAY,MAAM;AAEd,cAAA,IAAI,KAAK,OAAO;AAChB,cAAA,IAAI,KAAK,OAAO;AACjB,eAAA,KAAK,GAAG,CAAC;AAAA,QAAA;AAAA,MAElB;AAGAA,cAAa,UAAA,gBAAb,SAAc,SAAiC;AAC7C,YAAI,CAAC,KAAK;AAAU;AAEhB,YAAA,KAAK,SAAS,aAAa,GAAG;AAChC,eAAK,SAAS,KAAK,KAAK,IAAI,OAAO;AACnC,eAAK,SAAS,KAAK,KAAK,IAAI,QAAQ;AAAA,QAAA;AAGjC,aAAA,SAAS,KAAK,OAAO;AAAA,MAC5B;AACDA,aAAAA;AAAAA,IAAA,EAjF2B,SAAS;AAAA;ACErC,MAAA;AAAA;AAAA,IAAA,SAAA,QAAA;AAAmC,gBAAYC,gBAAA,MAAA;AAQ7C,eAAAA,iBAAA;AACE,YAAA,QAAA,kBAAM,SAAS,cAAc,QAAQ,CAAC,KAAE;AAJzB,cAAA,kBAAkB;;;AAUnCA,qBAAA,UAAA,UAAA,SAAQ,WAAmB,YAAoB,YAAc;AAAd,YAAA,eAAA,QAAA;AAAc,uBAAA;AAAA,QAAA;AACtD,aAAA,QAAQ,QAAQ,YAAY;AAC5B,aAAA,QAAQ,SAAS,aAAa;AACnC,aAAK,cAAc;AAAA,MACrB;AAEAA,qBAAA,UAAA,aAAA,SAAW,MAAa,YAAgB;AAA7B,YAAA,SAAA,QAAA;AAAW,iBAAA;AAAA,QAAA;AACpB,eAAO,KAAK,QAAQ,WAAW,MAAM,UAAU;AAAA,MACjD;AAOAA,qBAAA,UAAA,sBAAA,WAAA;AACE,eAAO,KAAK;AAAA,MACd;AAIAA,qBAAA,UAAA,uBAAA,WAAA;AACE,eAAO,KAAK,oBAAmB;AAAA,MACjC;AAEAA,qBAAW,UAAA,cAAX,SAAY,UAA+B;AACzC,aAAK,YAAY;AAAA,MACnB;AAEAA,qBAAS,UAAA,YAAT,SAAU,QAA2B;AACnC,aAAK,UAAU;AAAA,MACjB;AAGAA,qBAAS,UAAA,YAAT,SAAU,SAAgC;AACxC,YAAM,gBAAgB,QAAQ;AAC9B,YAAM,iBAAiB,KAAK;AAE5B,YAAM,oBAAoB,iBAAiB;AAC3C,YAAM,oBACJ,mBAAmB,KAAK,oBAAoB,QAAQ,oBAAoB;AAE1E,YAAI,mBAAmB;AACrB,eAAK,kBAAkB;AAAA,QAAA;AAGzB,YAAM,aAAa,KAAK,YAAY,KAAK,UAAU,KAAK,IAAI,IAAI;AAC1D,YAAA,iBAAiB,KAAK,iBAAiB;AAE7C,YAAI,qBAAqB,gBAAgB;AACvC,eAAK,eAAe;AACpB,eAAK,kBAAkB;AAEnB,cAAA,OAAO,KAAK,YAAY,YAAY;AACjC,iBAAA,QAAQ,KAAK,IAAI;AAAA,UAAA;AAEjB,iBAAA;AAAA,QAAA;AAAA,MAEX;AAGAA,qBAAA,UAAA,OAAA,SAAK,OAAe,QAAgB,YAAkB;AAC/C,aAAA,QAAQ,OAAO,QAAQ,UAAU;AAC/B,eAAA;AAAA,MACT;AAGAA,qBAAA,UAAA,UAAA,SAAQ,MAAa,YAAgB;AAA7B,YAAA,SAAA,QAAA;AAAW,iBAAA;AAAA,QAAA;AACV,eAAA,KAAK,WAAW,MAAM,UAAU;AAAA,MACzC;AAGAA,qBAAM,UAAA,SAAN,SAAO,qBAA8C;AAC/C,YAAA,OAAO,wBAAwB,YAAY;AAC7C,8BAAoB,KAAK,MAAM,KAAK,WAAA,CAAY;AAAA,QAAA,WACvC,OAAO,wBAAwB,aAAa;AACjD,cAAA,OAAO,KAAK,YAAY,YAAY;AACjC,iBAAA,QAAQ,KAAK,IAAI;AAAA,UAAA;AAAA,QACxB;AAGK,eAAA;AAAA,MACT;AACDA,aAAAA;AAAAA,IAAA,EAlGkC,YAAY;AAAA;AAsH/B,WAAA,OAAO,MAAO,YAAa,qBAAoB;AACzD,QAAA,OAAO,SAAS,YAAY;AACxB,UAAA,YAAU,IAAI;AACE,4BAAA;AACtB,gBAAQ,UAAU,WAAA;AAChB,4BAAoB,KAAK,WAAS,UAAQ,WAAA,CAAY;AAAA,MAAA,CACvD;AACM,aAAA;AAAA,IAAA,WACE,OAAO,eAAe,YAAY;AACrC,UAAA,YAAU,IAAI;AACE,4BAAA;AACtB,gBAAQ,UAAU,WAAA;AAChB,4BAAoB,KAAK,WAAS,UAAQ,WAAW,IAAI,CAAC;AAAA,MAAA,CAC3D;AACM,aAAA;AAAA,IAAA,WACE,OAAO,wBAAwB,YAAY;AAC9C,UAAA,YAAU,IAAI;AACpB,gBAAQ,UAAU,WAAA;AAChB,4BAAoB,KAAK,WAAS,UAAQ,WAAW,MAAM,UAAU,CAAC;AAAA,MAAA,CACvE;AACM,aAAA;AAAA,IAAA,OACF;AACC,UAAAf,WAAU,IAAI;AACb,aAAAA;AAAA,IAAA;AAAA,EAEX;AAGgB,WAAA,YACd,oBACA,sBAA6D;AAA7D,QAAA,yBAAA,QAAA;AAAA,6BAAA,WAAA;AAAyD,eAAA;AAAA,MAAA;AAAA,IAAA;AAEnD,QAAAa,UAAS,IAAI;AACb,QAAAb,WAAU,IAAI;AAEpB,IAAAa,QAAO,QAAQb,QAAO;AAEtB,IAAAA,SAAQ,UAAU,WAAA;AAChB,yBAAmB,MAAMA,SAAQ,iBAAiBA,UAASa,OAAM;AAAA,IAAA,CAClE;AAED,IAAAb,SAAQ,YAAY,oBAAoB;AAEjC,WAAAa;AAAA,EACT;AC5KO,MAAM,gBAAgB;AACtB,MAAM,eAAe;AACrB,MAAM,eAAe;AACrB,MAAM,aAAa;AACnB,MAAM,iBAAiB;AAGvB,MAAM,gBAAgB;AAEtB,MAAM,cAAc;AAE3B,MAAA;AAAA;AAAA,IAAA,WAAA;AAAA,eAAAG,cAAA;AAAA,MAAA;AAIEA,kBAAK,UAAA,QAAL,SAAM,KAAe;AACnB,YAAI,KAAK;AACP,cAAI,IAAI,KAAK;AACb,cAAI,IAAI,KAAK;AAAA,QAAA,OACR;AACC,gBAAA;AAAA,YACJ,GAAG,KAAK;AAAA,YACR,GAAG,KAAK;AAAA;;AAGL,eAAA;AAAA,MACT;AAEAA,kBAAA,UAAA,WAAA,WAAA;AACE,gBAAQ,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI;AAAA,MACxC;AACDA,aAAAA;AAAAA,IAAA,EAAA;AAAA;AAGD,MAAA;AAAA;AAAA,IAAA,WAAA;AAAA,eAAAC,yBAAA;AAGW,aAAA,MAAM,IAAI,WAAU;AAAA,MAAA;AAM7BA,6BAAK,UAAA,QAAL,SAAM,KAAe;AACnB,YAAI,KAAK;AACP,cAAI,IAAI,KAAK;AACb,cAAI,IAAI,KAAK;AAAA,QAAA,OACR;AACC,gBAAA;AAAA,YACJ,GAAG,KAAK;AAAA,YACR,GAAG,KAAK;AAAA;;AAGL,eAAA;AAAA,MACT;AAEAA,6BAAA,UAAA,WAAA,WAAA;AACS,eAAA,KAAK,OAAO,QAAQ,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI;AAAA,MAC3D;AACDA,aAAAA;AAAAA,IAAA,EAAA;AAAA;AAGD,MAAA;AAAA;AAAA,IAAA,WAAA;AAAA,eAAAC,gBAAA;AACE,aAAI,OAAW;AACf,aAAC,IAAW;AACZ,aAAC,IAAW;AACZ,aAAS,YAAW;AACpB,aAAK,QAAY;AACjB,aAAI,OAAS;AACb,aAAS,YAAuB;AAAA,MAAA;AAChCA,oBAAA,UAAA,WAAA,WAAA;AACS,eAAA,KAAK,OAAO,QAAQ,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI;AAAA,MAC3D;AACDA,aAAAA;AAAAA,IAAA,EAAA;AAAA;AAGgB,MAAM,iBAAiB,IAAI;AAE3B,MAAM,UAAU,IAAI;AAGrC,MAAA;AAAA;AAAA,IAAA,WAAA;AAAA,eAAAC,WAAA;AAAA,YAyNC,QAAA;AAvNC,aAAK,QAAG;AAkDR,aAAS,YAAgB;AACzB,aAAU,aAAgB;AAEf,aAAA,cAAG,SAAC,OAA8B;AAC3CA,mBAAQ,SAAS,QAAQ,SAAS,QAAQ,MAAM,iBAAiB,MAAM,IAAI;AAC3E,gBAAM,eAAc;AACpB,gBAAK,WAAW,KAAK;AAChB,gBAAA,cAAc,MAAM,MAAM,KAAK;AAE/B,gBAAA,YAAY,SAAS,MAAK,SAAS;AACnC,gBAAA,YAAY,eAAe,MAAK,UAAU;AAAA,QACjD;AAEU,aAAA,aAAG,SAAC,OAA8B;AAC1C,gBAAM,eAAc;AACpB,gBAAK,WAAW,KAAK;AAChB,gBAAA,cAAc,MAAM,MAAM,KAAK;AAAA,QACtC;AAES,aAAA,YAAG,SAAC,OAA8B;;AACzC,gBAAM,eAAc;AAEpBA,mBAAQ,SAAS,QAAQ,SAAS,QAAQ,MAAM,eAAe,MAAM,IAAI;AACpE,gBAAA,cAAc,MAAM,MAAM,KAAK;AAEhC,cAAA,MAAK,UAAU,QAAQ;AACzBA,qBAAQ,SAAS,QAAQ,SAAS,QAAQ,MAAM,mBAAmB,MAAM,OAAM,KAAA,MAAK,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,MAAM;AACrG,kBAAK,cAAc,SAAS,OAAO,MAAK,SAAS;AAAA,UAAA;AAEnD,gBAAK,WAAW,SAAS;AAAA,QAC3B;AAEY,aAAA,eAAG,SAAC,OAA2C;;AACrD,cAAA,MAAK,WAAW,QAAQ;AAC1BA,qBAAQ,SAAS,QAAQ,SAAS,QAAQ,MAAM,kBAAkB,MAAM,OAAM,KAAA,MAAK,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,MAAM;AACpG,kBAAK,cAAc,eAAe,OAAO,MAAK,UAAU;AAAA,UAAA;AAE1D,gBAAK,UAAU,SAAS;AAAA,QAC1B;AAsFA,aAAA,aAAa,SAACT,YAAsB,SAAqB;AACvD,iBAAO,CAACA,WAAU,MAAM,QAAQ,IAAI;AAAA,QACtC;AAEA,aAAA,WAAW,SAACA,YAAsB,SAAqB;AAErD,yBAAe,MAAM,QAAQ;AAC7B,yBAAe,OAAO,QAAQ;AAC9B,yBAAe,YAAY,QAAQ;AACpB,yBAAA,IAAI,IAAI,QAAQ;AAChB,yBAAA,IAAI,IAAI,QAAQ;AAE/B,cAAM,YAAYA,WAAU,UAAU,QAAQ,IAAI;AAClD,cAAI,CAAC,WAAW;AACd;AAAA,UAAA;AAGF,UAAAA,WAAU,SAAS,QAAU,EAAA,IAAI,SAAS,cAAc;AAKlD,cAAA,gBAAgBA,eAAc,QAAQ,QAAQA,WAAU,KAAK,KAAK,KAAKA,WAAU,QAAQ,cAAc;AAC7G,cAAI,CAAC,eAAe;AAClB;AAAA,UAAA;AAGF,cAAI,QAAQ,WAAW;AACb,oBAAA,UAAU,KAAKA,UAAS;AAAA,UAAA;AAIlC,cAAI,QAAQ,OAAO;AAEjB,gBAAI,SAAO;AACX,qBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,uBAAO,UAAU,CAAC,EAAE,KAAKA,YAAW,cAAc,IAAI,OAAO;AAAA,YAAA;AAExD,mBAAA;AAAA,UAAA;AAAA,QAEX;AAAA,MAAA;AAjNAS,eAAA,UAAA,QAAA,SAAM,OAAa,MAAiB;AAApC,YAyBC,QAAA;AAxBC,aAAK,QAAQ;AACb,aAAK,OAAO;AAEZ,aAAK,QAAQ,MAAM,SAAQ,EAAG,SAAS;AACjC,cAAA,GAAG,YAAY,SAAC,UAAkB;;AACjC,gBAAA,SAAQ,KAAA,SAAS,WAAS,QAAA,OAAA,SAAA,KAAA,MAAK;AAAA,QAAA,CACrC;AAKI,aAAA,iBAAiB,cAAc,KAAK,WAAW;AAC/C,aAAA,iBAAiB,YAAY,KAAK,SAAS;AAC3C,aAAA,iBAAiB,aAAa,KAAK,UAAU;AAC7C,aAAA,iBAAiB,eAAe,KAAK,YAAY;AAEjD,aAAA,iBAAiB,aAAa,KAAK,WAAW;AAC9C,aAAA,iBAAiB,WAAW,KAAK,SAAS;AAC1C,aAAA,iBAAiB,aAAa,KAAK,UAAU;AAEzC,iBAAA,iBAAiB,WAAW,KAAK,YAAY;AAC/C,eAAA,iBAAiB,QAAQ,KAAK,YAAY;AAE1C,eAAA;AAAA,MACT;AAEAA,eAAA,UAAA,UAAA,WAAA;AACE,YAAM,OAAO,KAAK;AAEb,aAAA,oBAAoB,cAAc,KAAK,WAAW;AAClD,aAAA,oBAAoB,YAAY,KAAK,SAAS;AAC9C,aAAA,oBAAoB,aAAa,KAAK,UAAU;AAChD,aAAA,oBAAoB,eAAe,KAAK,YAAY;AAEpD,aAAA,oBAAoB,aAAa,KAAK,WAAW;AACjD,aAAA,oBAAoB,WAAW,KAAK,SAAS;AAC7C,aAAA,oBAAoB,aAAa,KAAK,UAAU;AAE5C,iBAAA,oBAAoB,WAAW,KAAK,YAAY;AAClD,eAAA,oBAAoB,QAAQ,KAAK,YAAY;AAE7C,eAAA;AAAA,MACT;AA6CAA,eAAU,UAAA,aAAV,SAAW,OAA8B;;AACvC,YAAM,OAAO,KAAK;AACd,YAAA;AACA,YAAA;kBAGC,MAAqB,aAAS,QAAA,OAAA,SAAA,SAAA,GAAA,QAAQ;AACpC,cAAA,MAAqB,QAAQ,CAAC,EAAE;AAChC,cAAA,MAAqB,QAAQ,CAAC,EAAE;AAAA,QAAA,OAChC;AACL,cAAK,MAAqB;AAC1B,cAAK,MAAqB;AAAA,QAAA;AAGtB,YAAA,OAAO,KAAK;AAClB,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK,aAAa;AACvB,aAAK,KAAK,YAAY;AAEd,gBAAA,IAAI,IAAI,KAAK;AACb,gBAAA,IAAI,IAAI,KAAK;AAAA,MACvB;AAKAA,eAAA,UAAA,cAAA,SAAY,MAAc,QAAmB;AAC3C,YAAM,UAAU;AAEhB,gBAAQ,OAAO;AACf,gBAAQ,OAAO,KAAK;AACpB,gBAAQ,QAAQ;AAChB,gBAAQ,YAAY;AACpB,gBAAQ,UAAU,SAAS;AAE3B,aAAK,MAAM,MACT;AAAA,UACE,SAAS;AAAA,UACT,SAAS;AAAA,UACT,OAAO,KAAK;AAAA,UACZ,KAAK,KAAK;AAAA,WAEZ,OAAO;AAAA,MAEX;AAEAA,eAAA,UAAA,gBAAA,SAAc,MAAc,OAAgB,SAAqB;AAC/D,YAAM,UAAU;AAEhB,gBAAQ,OAAO;AACf,gBAAQ,OAAO,KAAK;AACpB,gBAAQ,QAAQ;AACR,gBAAA,YAAY,KAAK;AACzB,gBAAQ,YAAY;AAEhB,YAAA,SAAS,eAAe,SAAS,aAAa;AAChDA,mBAAQ,SAAS,QAAQ,SAAS,QAAQ,MAAM,yBAAyB,SAAS,YAAA,QAAA,8BAAA,QAAS,MAAM;AAAA,QAAA;AAGnG,YAAI,SAAS;AACX,iBAAO,QAAQ,QAAQ;AACf,gBAAAT,aAAY,QAAQ;AAC1B,gBAAI,KAAK,SAASA,YAAW,OAAO,GAAG;AACrC;AAAA,YAAA;AAAA,UACF;AAEF,kBAAQ,SAAS;AAAA,QAAA,OACZ;AACL,eAAK,MAAM,MACT;AAAA,YACE,SAAS;AAAA,YACT,SAAS;AAAA,YACT,OAAO,KAAK;AAAA,YACZ,KAAK,KAAK;AAAA,aAEZ,OAAO;AAAA,QAAA;AAAA,MAGb;AA7KOS,eAAK,QAAG;AAwNhBA,aAAAA;AAAAA,IAAA,EAAA;AAAA;AAGM,MAAM,QAAQ;AAAA,IACnB,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,KAAK;AAAA,IACL,QAAQ;AAAA;ACpTO,MAAM,QAAgB;WAEvB,QAAK;AACnB,aAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AACpC,YAAA,CAAC,EAAE;;EAEb;WAEgB,SAAM;AACpB,aAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AACpC,YAAA,CAAC,EAAE;;EAEb;AAEM,WAAU,MAAM,SAAwB;AAAxB,QAAA,YAAA,QAAA;AAAA,gBAAwB,CAAA;AAAA,IAAA;AACtC,QAAA,OAAO,IAAI;AAEjB,SAAK,MAAM,OAAO;AAElB,SAAK,UAAU,IAAI,QAAA,EAAU,MAAM,MAAM,KAAK,GAAkB;AACzD,WAAA;AAAA,EACT;AA0BA,MAAA;AAAA;AAAA,IAAA,SAAA,QAAA;AAA0B,gBAASC,OAAA,MAAA;AAwBjC,eAAAA,QAAA;AACE,YAAA,QAAA,qBAAQ;AAxBV,cAAM,SAA6B;AACnC,cAAG,MAA6B;AAChC,cAAO,UAAoC;AAE1B,cAAA,aAAa;AACb,cAAA,cAAc;AACd,cAAA,aAAa;AACb,cAAA,eAAe;AACf,cAAA,gBAAgB;AAEjC,cAAO,UAAG;AACV,cAAM,SAAG;AACT,cAAK,QAAG;AAgBH,cAAA,QAAG,SAAC,SAAwB;AAAxB,cAAA,YAAA,QAAA;AAAA,sBAAwB,CAAA;AAAA,UAAA;AAC3B,cAAA,OAAO,QAAQ,WAAW,UAAU;AACtC,kBAAK,SAAS,SAAS,eAAe,QAAQ,MAAM;AAChD,gBAAA,CAAC,MAAK,QAAQ;AACR,sBAAA,MAAM,8BAA8B,QAAQ,MAAM;AAAA,YAAA;AAAA,UAC5D,WACS,QAAQ,kBAAkB,mBAAmB;AACtD,kBAAK,SAAS,QAAQ;AAAA,UAAA,WACb,QAAQ,QAAQ;AACjB,oBAAA,MAAM,6BAA6B,QAAQ,MAAM;AAAA,UAAA;AAGvD,cAAA,CAAC,MAAK,QAAQ;AAChB,kBAAK,SAAU,SAAS,eAAe,OAAO,KAC5C,SAAS,eAAe,OAAO;AAAA,UAAA;AAG/B,cAAA,CAAC,MAAK,QAAQ;AACR,oBAAA,SAAS,QAAQ,MAAM,4BAA4B;AACtD,kBAAA,SAAS,SAAS,cAAc,QAAQ;AACtC,mBAAA,OAAO,MAAK,OAAO,OAAO;AAAA,cAC/B,UAAU;AAAA,cACV,SAAS;AAAA,cACT,KAAK;AAAA,cACL,MAAM;AAAA,cACN,QAAQ;AAAA,cACR,OAAO;AAAA,cACP,OAAO;AAAA,cACP,QAAQ;AAAA,YAAA,CACT;AAED,gBAAM,OAAO,SAAS;AACtB,iBAAK,aAAa,MAAK,QAAQ,KAAK,UAAU;AAAA,UAAA;AAGhD,gBAAK,MAAM,MAAK;AAEhB,gBAAK,UAAU,MAAK,OAAO,WAAW,IAAI;AAErC,gBAAA,mBAAmB,OAAO,oBAAoB;AAC9C,gBAAA,oBACH,MAAK,QAAQ,8BAA8B,KAC3C,MAAK,QAAQ,2BAA2B,KACxC,MAAK,QAAQ,0BAA0B,KACvC,MAAK,QAAQ,yBAAyB,KACtC,MAAK,QAAQ,wBAAwB,KACrC;AAEG,gBAAA,aAAa,MAAK,mBAAmB,MAAK;AAM/C,gBAAK,UAAU;AACf,gBAAM,KAAK,KAAI;AACf,gBAAK,aAAY;AAAA,QACnB;AAEiB,cAAA,iBAAiB;AAGlC,cAAA,eAAe,WAAA;AAET,cAAA,CAAC,MAAK,gBAAgB;AACxB,kBAAK,iBAAiB;AACtB,kCAAsB,MAAK,OAAO;AAAA,UAAA;AAAA,QAEtC;AAEiB,cAAA,iBAAiB;AACjB,cAAS,YAAkB;AAGrC,cAAA,UAAG,SAAC,KAAW;AACpB,gBAAK,iBAAiB;AAElB,cAAA,CAAC,MAAK,WAAW,CAAC,MAAK,UAAU,CAAC,MAAK,SAAS;AAClD;AAAA,UAAA;AAGF,gBAAK,aAAY;AAEX,cAAA,gBAAgB,MAAK,OAAO;AAC5B,cAAA,iBAAiB,MAAK,OAAO;AAEnC,cAAI,MAAK,eAAe,iBAAiB,MAAK,gBAAgB,gBAAgB;AAE5E,kBAAK,aAAa;AAClB,kBAAK,cAAc;AAEd,kBAAA,eAAe,gBAAgB,MAAK;AACpC,kBAAA,gBAAgB,iBAAiB,MAAK;AAEvC,gBAAA,MAAK,OAAO,UAAU,MAAK,gBAAgB,MAAK,OAAO,WAAW,MAAK,eAAe;AAEnF,oBAAA,OAAO,QAAQ,MAAK;AACpB,oBAAA,OAAO,SAAS,MAAK;AAE1B,sBAAQ,SAAS,QAAQ,MACrB,cACE,MAAK,eACL,OACA,MAAK,gBACL,SACA,MAAK,aACL,SACA,MAAK,aACL,OACA,MAAK,cACL,GAAG;AAGT,oBAAK,SAAS;AAAA,gBACZ,OAAO,MAAK;AAAA,gBACZ,QAAQ,MAAK;AAAA,gBACb,OAAO,MAAK;AAAA,cAAA,CACb;AAAA,YAAA;AAAA,UACH;AAGI,cAAA,OAAO,MAAK,kBAAkB;AACpC,cAAM,UAAU,MAAM;AAEtB,cAAI,CAAC,MAAK,WAAW,MAAK,UAAU,MAAK,OAAO;AAC9C;AAAA,UAAA;AAGF,gBAAK,iBAAiB;AAEtB,gBAAK,UAAS;AAEd,cAAM,cAAc,MAAK,MAAM,SAAS,KAAK,IAAI;AAC7C,cAAA,MAAK,aAAa,MAAK,WAAW;AAEpC,kBAAK,YAAY,MAAK;AACtB,kBAAK,QAAQ;AAEb,gBAAI,MAAK,eAAe,KAAK,MAAK,gBAAgB,GAAG;AACnD,oBAAK,QAAQ,aAAa,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC1C,oBAAK,QAAQ,UAAU,GAAG,GAAG,MAAK,cAAc,MAAK,aAAa;AAC9D,kBAAA,MAAK,gBAAgB,GAAG;AACrB,sBAAA,YAAY,MAAK,OAAO;AAAA,cAAA;AAE1B,oBAAA,OAAO,MAAK,OAAO;AAAA,YAAA;AAAA,qBAEjB,aAAa;AAEtB,kBAAK,QAAQ;AAAA,UAAA,OACR;AAEL,kBAAK,QAAQ;AAAA,UAAA;AAGT,gBAAA,MAAM,UAAU,MAAO,UAAU;AAAA,QACzC;AAGA,cAAa,gBAAG;AAjKd,cAAK,MAAM,MAAM;;;AAmKXA,YAAW,UAAA,cAAnB,SAAoB,SAAiC;AACnD,YAAM,OAAO,OAAO,KAAK,kBAAkB,WAAW,KAAK,gBAAgB;AACrE,YAAA,IAAI,KAAK;AACf,gBAAQ,aAAa,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAC3C,YAAA,YAAY,IAAI,EAAE;AAExB,gBAAQ,UAAS;AACT,gBAAA,OAAO,GAAG,CAAC;AACX,gBAAA,OAAO,GAAG,MAAM,IAAI;AAC5B,gBAAQ,OAAO,OAAO,MAAM,MAAM,IAAI;AAC9B,gBAAA,OAAO,GAAG,IAAI;AACtB,gBAAQ,OAAO,MAAO,MAAM,MAAM,IAAI;AAC9B,gBAAA,OAAO,GAAG,MAAM,IAAI;AAC5B,gBAAQ,cAAc;AACtB,gBAAQ,WAAW;AACnB,gBAAQ,UAAU;AAClB,gBAAQ,YAAY;AACpB,gBAAQ,OAAM;AAEd,gBAAQ,UAAS;AACT,gBAAA,OAAO,GAAG,CAAC;AACX,gBAAA,OAAO,MAAM,MAAM,CAAC;AAC5B,gBAAQ,OAAO,MAAM,MAAM,OAAO,IAAI;AAC9B,gBAAA,OAAO,MAAM,CAAC;AACtB,gBAAQ,OAAO,MAAM,MAAM,MAAO,IAAI;AAC9B,gBAAA,OAAO,MAAM,MAAM,CAAC;AAC5B,gBAAQ,cAAc;AACtB,gBAAQ,WAAW;AACnB,gBAAQ,UAAU;AAClB,gBAAQ,YAAY;AACpB,gBAAQ,OAAM;AAAA,MAChB;AAEAA,YAAA,UAAA,SAAA,WAAA;AACM,YAAA,KAAK,SAAS,KAAK,QAAQ;AAC7B,eAAK,aAAY;AAAA,QAAA;AAEnB,aAAK,SAAS;AACd,aAAK,QAAQ;AACb,aAAK,QAAQ,QAAQ;AACd,eAAA;AAAA,MACT;AAEAA,YAAA,UAAA,QAAA,WAAA;AACM,YAAA,CAAC,KAAK,QAAQ;AAChB,eAAK,QAAQ,OAAO;AAAA,QAAA;AAEtB,aAAK,SAAS;AACP,eAAA;AAAA,MACT;AAGAA,YAAA,UAAA,QAAA,WAAA;AACM,YAAA,KAAK,SAAS,KAAK,QAAQ;AAC7B,eAAK,aAAY;AAAA,QAAA;AAEnB,aAAK,QAAQ;AACb,eAAO,OAAK,UAAC,MAAK,KAAA,IAAA;AAAA,MACpB;AAEAA,YAAA,UAAA,UAAA,WAAA;;AACE,aAAK,UAAU;AACT,YAAA,QAAQ,MAAM,QAAQ,IAAI;AAChC,YAAI,SAAS,GAAG;AACR,gBAAA,OAAO,OAAO,CAAC;AAAA,QAAA;AAGvB,SAAA,KAAA,KAAK,aAAS,QAAA,OAAA,SAAA,SAAA,GAAA;AACP,eAAA;AAAA,MACT;AAEAA,YAAU,UAAA,aAAV,SAAW,OAAa;AACtB,YAAI,KAAK,KAAK;AACP,eAAA,IAAI,MAAM,kBAAkB;AAAA,QAAA;AAE5B,eAAA;AAAA,MACT;AAWAA,YAAA,UAAA,WAAA,SAAS,OAA2B,QAAiB,OAAc;AAC7D,YAAA,OAAO,UAAU,aAAa;AAEhC,iBAAO,OAAO,OAAO,IAAI,KAAK,SAAS;AAAA,QAAA;AAGrC,YAAA,OAAO,UAAU,UAAU;AAC7B,cAAM,UAAU;AAChB,kBAAQ,QAAQ;AAChB,mBAAS,QAAQ;AACjB,kBAAQ,QAAQ;AAAA,QAAA;AAGlB,YAAI,OAAO,UAAU,YAAY,OAAO,WAAW,UAAU;AAC3D,eAAK,YAAY;AAAA,YACf;AAAA,YACA;AAAA,YACA,OAAO,OAAO,UAAU,WAAW,QAAQ;AAAA;AAE7C,eAAK,QAAO;AACZ,cAAM,SAAO,OAAO,OAAO,CAAA,GAAI,KAAK,SAAS;AAC7C,eAAK,MAAM;AAAA,YACT,OAAO,SAAUV,YAAS;AACxB,kBAAI,CAACA,WAAU,MAAM,UAAU,GAAG;AACzB,uBAAA;AAAA,cAAA;AAET,cAAAA,WAAU,QAAQ,YAAY,CAAC,MAAI,CAAC;AAAA,YAAA;AAAA,UACtC,CACD;AAAA,QAAA;AAGI,eAAA;AAAA,MACT;AAOAU,YAAA,UAAA,UAAA,SAAQ,OAA0B,QAAiB,MAAc;AAG/D,YAAI,OAAO,UAAU,YAAY,OAAO,WAAW,UAAU;AAC3D,eAAK,WAAW;AAAA,YACd;AAAA,YACA;AAAA,YACA;AAAA;QAEO,WAAA,OAAO,UAAU,YAAY,UAAU,MAAM;AACtD,eAAK,WACA,SAAA,CAAA,GAAA,KAAK;AAAA,QAAA;AAIZ,aAAK,QAAO;AAEL,eAAA;AAAA,MACT;AAEAA,YAAM,UAAA,SAAN,SAAO,QAAc;AACnB,aAAK,UAAU;AACf,aAAK,QAAO;AACL,eAAA;AAAA,MACT;AAGAA,YAAA,UAAA,UAAA,WAAA;AACE,YAAM,UAAU,KAAK;AACrB,YAAM,WAAW,KAAK;AACtB,YAAM,SAAS,KAAK;AACpB,YAAI,YAAY,SAAS;AACvB,cAAM,gBAAgB,SAAS;AAC/B,cAAM,iBAAiB,SAAS;AAChC,cAAM,cAAc,eAAe,QAAQ,IAAI,IAAI,QAAQ,OAAO;AAClE,cAAM,eAAe,QAAQ;AAC7B,cAAM,gBAAgB,QAAQ;AAE9B,eAAK,IAAI;AAAA,YACP,OAAO;AAAA,YACP,QAAQ;AAAA,UAAA,CACT;AACI,eAAA,IAAI,eAAe,gBAAgB,WAAW;AAE7C,cAAA,WAAW,QAAQ,KAAK;AACxB,cAAA,WAAW,QAAQ,KAAK;AAE9B,cAAM,eAAc,WAAA,QAAA,WAAA,SAAA,SAAA,OAAQ,MAAK;AACjC,cAAM,eAAc,WAAA,QAAA,WAAA,SAAA,SAAA,OAAQ,MAAK;AACjC,cAAM,WAAU,WAAA,QAAA,WAAA,SAAA,SAAA,OAAQ,MAAK;AAC7B,cAAM,WAAU,WAAA,QAAA,WAAA,SAAA,SAAA,OAAQ,MAAK;AAEvB,cAAA,SAAS,KAAK,IAAI,QAAQ;AAC1B,cAAA,SAAS,KAAK,IAAI,QAAQ;AAE3B,eAAA,IAAI,UAAU,SAAS,WAAW;AAClC,eAAA,IAAI,UAAU,SAAS,WAAW;AAEvC,eAAK,IAAI,WAAW,UAAU,WAAW,SAAS,WAAW;AAC7D,eAAK,IAAI,WAAW,UAAU,WAAW,SAAS,WAAW;AAAA,mBACpD,UAAU;AACnB,eAAK,IAAI;AAAA,YACP,OAAO,SAAS;AAAA,YAChB,QAAQ,SAAS;AAAA,UAAA,CAClB;AAAA,QAAA;AAGI,eAAA;AAAA,MACT;AAGAA,YAAK,UAAA,QAAL,SAAM,GAAU;AACT,aAAA,KAAK,cAAc,IAAI,KAAK;AAC1B,eAAA;AAAA,MACT;AAGAA,YAAK,UAAA,QAAL,SAAM,GAAU;AACT,aAAA,KAAK,cAAc,IAAI,KAAK;AAC1B,eAAA;AAAA,MACT;AACDA,aAAAA;AAAAA,IAAA,EA5YyB,SAAS;AAAA;ACjDnB,WAAA,KAAK,QAA6C,KAAY;AACtEC,QAAAA,QAAO,IAAI;AACjBA,UAAK,OAAO,MAAM,EAAE,UAAU,CAAC;AACxBA,WAAAA,MAAK,IAAI,GAAG;AACZA,WAAAA;AAAAA,EACT;AAGiB,MAAM,MAAM;AAE7B,MAAA;AAAA;AAAA,IAAA,SAAA,QAAA;AAA0B,gBAASC,OAAA,MAAA;AAcjC,eAAAA,QAAA;AACE,YAAA,QAAA,qBAAQ;AAdO,cAAA,WAA2B;AAE3B,cAAA,UAAqB,CAAA;AAKrB,cAAA,QAAgB;AAChB,cAAA,UAAkB;AAClB,cAAA,SAAiB;AAsB1B,cAAiB,oBAAG;AAEpB,cAAA,YAAY,SAAC,GAAW,KAAa,MAAY;AACvD,cAAI,MAAK,QAAQ,KAAK,MAAK,QAAQ,UAAU,GAAG;AAC9C;AAAA,UAAA;AAII,cAAA,SAAS,MAAK,qBAAqB;AACzC,gBAAK,oBAAoB;AACzB,cAAI,QAAQ;AACH,mBAAA;AAAA,UAAA;AAGT,gBAAK,SAAS;AACV,cAAA,MAAK,QAAQ,MAAK,KAAK;AAClB,mBAAA;AAAA,UAAA;AAET,cAAM,IAAK,MAAK,QAAQ,MAAK,MAAO;AAC/B,gBAAA,SAAS,IAAI,MAAK;AACvB,gBAAK,UAAU,CAAC;AAChB,cAAI,MAAK,UAAU,MAAM,MAAK,WAAW,MAAM,GAAG;AAChD,kBAAK,KAAI;AACJ,kBAAA,aAAa,MAAK;AAChB,mBAAA;AAAA,UAAA;AAEF,iBAAA;AAAA,QACT;AA3CE,cAAK,MAAM,MAAM;AAEjB,cAAK,OAAO;AACP,cAAA,MAAM,MAAO,MAAK;AAElB,cAAA,KAAK,MAAK,WAAW,KAAK;;;AAIjCA,YAAa,UAAA,gBAAb,SAAc,SAAiC;AAC7C,YAAI,CAAC,KAAK;AAAU;AAEf,aAAA,SAAS,KAAK,OAAO;AAAA,MAC5B;AAgCAA,YAAG,UAAA,MAAH,SAAI,KAAY;AACV,YAAA,OAAO,QAAQ,aAAa;AAC9B,iBAAO,KAAK;AAAA,QAAA;AAET,aAAA,OAAO,MAAM,IAAI,MAAM;AACvB,aAAA,MAAM,MAAO,KAAK;AAChB,eAAA;AAAA,MACT;AAGAA,YAAS,UAAA,YAAT,SAAU,QAA2C;AAC5C,eAAA,KAAK,OAAO,MAAM;AAAA,MAC3B;AAEAA,YAAM,UAAA,SAAN,SAAO,QAA2C;AAChD,aAAK,SAAS;AACd,aAAK,UAAU,QAAQ,MAAM,EAAE,MAAK;AACpC,aAAK,MAAK;AACH,eAAA;AAAA,MACT;AAEAA,YAAA,UAAA,SAAA,WAAA;AACE,eAAO,KAAK,UAAU,KAAK,QAAQ,SAAS;AAAA,MAC9C;AAEAA,YAAA,UAAA,YAAA,SAAU,OAAe,QAAc;AAAd,YAAA,WAAA,QAAA;AAAc,mBAAA;AAAA,QAAA;AACrC,aAAK,SAAS,KAAK,KAAK,OAAO,KAAK,QAAQ,MAAM,IAAI;AAC7C,iBAAA,UAAU,CAAC,KAAK;AACzB,aAAK,WAAW,KAAK,QAAQ,KAAK,MAAM;AACxC,YAAI,QAAQ;AACV,eAAK,IAAI,SAAS,KAAK,SAAS,UAAU;AAC1C,eAAK,IAAI,UAAU,KAAK,SAAS,WAAW;AAAA,QAAA;AAE9C,aAAK,MAAK;AACH,eAAA;AAAA,MACT;AAEAA,YAAS,UAAA,YAAT,SAAU,MAAY;AACpB,eAAO,KAAK,UAAU,KAAK,SAAS,IAAI;AAAA,MAC1C;AAEAA,YAAA,UAAA,SAAA,SAAO,QAAgB,UAAqB;AAC1C,aAAK,UAAU,SAAS,KAAK,QAAQ,SAAS;AAC9C,aAAK,YAAY;AACjB,aAAK,KAAI;AACF,eAAA;AAAA,MACT;AAEAA,YAAI,UAAA,OAAJ,SAAK,OAAc;AACb,YAAA,OAAO,UAAU,aAAa;AAChC,eAAK,UAAU,KAAK;AACpB,eAAK,QAAQ;AAAA,QAAA,WACJ,KAAK,QAAQ,GAAG;AACzB,eAAK,QAAQ;AAAA,QAAA;AAGf,aAAK,MAAK;AACH,eAAA;AAAA,MACT;AAEAA,YAAI,UAAA,OAAJ,SAAK,OAAc;AACjB,aAAK,QAAQ;AACT,YAAA,OAAO,UAAU,aAAa;AAChC,eAAK,UAAU,KAAK;AAAA,QAAA;AAEf,eAAA;AAAA,MACT;AACDA,aAAAA;AAAAA,IAAA,EAhIyB,SAAS;AAAA;ACX7B,WAAU,SAAS,OAAqE;AAC5F,WAAO,IAAI,SAAA,EAAW,OAAO,KAAK;AAAA,EACpC;AAEA,MAAA;AAAA;AAAA,IAAA,SAAA,QAAA;AAA8B,gBAASC,WAAA,MAAA;AAMrC,eAAAA,YAAA;AACE,YAAA,QAAA,qBAAQ;AANO,cAAA,YAAuB,CAAA;AAOtC,cAAK,MAAM,UAAU;;;AAIvBA,gBAAa,UAAA,gBAAb,SAAc,SAAiC;AAC7C,YAAI,CAAC,KAAK,aAAa,CAAC,KAAK,UAAU;AAAQ;AAEtC,iBAAA,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,IAAI,GAAG,KAAK;AACrD,eAAK,UAAU,CAAC,EAAE,KAAK,OAAO;AAAA,QAAA;AAAA,MAElC;AAGAA,gBAAO,UAAA,UAAP,SAAQ,QAAsE;AACrE,eAAA,KAAK,OAAO,MAAM;AAAA,MAC3B;AAEAA,gBAAM,UAAA,SAAN,SAAO,QAAsE;AAC3E,aAAK,YAAY,CAAA;AACb,YAAA,OAAO,UAAU,UAAU;AACvB,cAAA,cAAY,QAAQ,MAAM;AAC3B,eAAA,QAAQ,SAAU,OAAa;AAC3B,mBAAA,YAAU,IAAI,KAAK;AAAA,UAC5B;AAAA,QAAA,WACS,OAAO,WAAW,UAAU;AAChC,eAAA,QAAQ,SAAU,OAAa;AAClC,mBAAO,OAAO,KAAK;AAAA,UACrB;AAAA,QAAA,WACS,OAAO,WAAW,YAAY;AACvC,eAAK,QAAQ;AAAA,QAAA;AAER,eAAA;AAAA,MACT;AAGAA,gBAAQ,UAAA,WAAR,SAAS,OAA4C;AAC5C,eAAA,KAAK,MAAM,KAAK;AAAA,MACzB;AAGAA,gBAAK,UAAA,QAAL,SAAM,OAA4C;AAC5C,YAAA,OAAO,UAAU,aAAa;AAChC,iBAAO,KAAK;AAAA,QAAA;AAEV,YAAA,KAAK,WAAW,OAAO;AAClB,iBAAA;AAAA,QAAA;AAET,aAAK,SAAS;AAEd,YAAI,UAAU,MAAM;AACV,kBAAA;AAAA,QAAA,WACC,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,KAAK,GAAG;AAC7D,kBAAQ,MAAM;;AAGX,aAAA,WAAW,KAAK,YAAY;AAEjC,YAAI,QAAQ;AACZ,YAAI,SAAS;AACb,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAC/B,cAAA,IAAI,MAAM,CAAC;AACjB,cAAM,YAAW,KAAK,UAAU,CAAC,IAAI,KAAK,MAAM,OAAO,MAAM,WAAW,IAAI,IAAI,EAAE;AACzE,mBAAA,IAAI,IAAI,KAAK,WAAW;AACzB,oBAAA,yBAAyB,OAAO,CAAC;AACjC,kBAAA,QAAQ,UAAQ;AACxB,mBAAS,KAAK,IAAI,QAAQ,UAAQ,WAAW;AAAA,QAAA;AAE1C,aAAA,IAAI,SAAS,KAAK;AAClB,aAAA,IAAI,UAAU,MAAM;AACpB,aAAA,UAAU,SAAS,MAAM;AACvB,eAAA;AAAA,MACT;AACDA,aAAAA;AAAAA,IAAA,EAhF6B,SAAS;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "x_google_ignoreList": [2]}