name: Build

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: npm-install
        run: npm install

      - name: npm-test
        run: npm run test

      - name: npm-lint
        run: npm run lint

      - name: npm-build
        run: npm run build

      - name: npm-test-types
        run: npm run test:types

      - name: npm-typedoc
        run: npm run typedoc
