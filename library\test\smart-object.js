// @ts-check

/**
 * @template T
 * @typedef {T} SmartObjectValue
*/

/**
 * @template T
 * @typedef {Readonly<T>} SmartObjectConst
*/

/**
 * @template T
 * @typedef {{ value: T }} SmartObjectRef
*/

/** @template T */
export default class SmartObject {

  static Const;
  static Ref;

  #object;

  /** @param {T} object */
  constructor(object) {
    this.#object = object;
  }
  /** @returns {SmartObjectValue<T>} */
  value() {
    return this.#object;
  }
  /** @returns {SmartObjectConst<T>} */
  get const() {
    if(typeof this.#object !== "object" || this.#object === null) {
      return this.#object;
    }
    return new Proxy(this.#object, {
      set() {
        throw new Error("Cannot set const");
      }
    });
  }
  /** @returns {SmartObjectRef<T>} */
  get ref() {
    return new SmartObject.Ref(this);
  }

  static {

    /** @template T */
    this.Ref = class Ref {
      #smartObject;
      /** @param {SmartObject<T>} smartObject */
      constructor(smartObject) {
        this.#smartObject = smartObject;
      }
      get value() {
        return this.#smartObject.#object;
      }
      /** @param {T} object */
      set value(object) {
        this.#smartObject.#object = object;
      }
    };

  }

};

class Vector {
  x = 10;
  y = 10;
  constructor(x, y) {
    this.x = x;
    this.y = y;
  }
}

/**
 * @param {SmartObjectConst<Vector>} a
 * @param {SmartObjectConst<Vector>} b
 * @param {SmartObjectRef<Vector>} c
*/
function sum(a, b, c) {
  c.value.x = a.x + b.x;
  c.value.y = a.y + b.y;
  return c;
}

const a = new SmartObject(new Vector(10, 20));
const v = new SmartObject(new Vector);
sum(a.const, a.const, v.ref);