# planck

## 1.3.0

### Minor Changes

- bb9bb87: Testbed rendering rewrite
- 56193e7: Add DataDriver (experimental for demo use-case)

### Patch Changes

- ce1c486: No pointer interaction when mouseForce===0

## 1.2.0

### Minor Changes

- f0127f4: Add world.queueUpdate() to queue and defer updates after current simulation step

### Patch Changes

- 97bb79e: Improve world.queueUpdate

## 1.1.6

### Patch Changes

- f31114b: Add static Vec2.normalize
- bee0e16: Change clampVec2 arg to Vec2Value

## 1.1.5

### Patch Changes

- fbd0021: Un-hidden style field type
