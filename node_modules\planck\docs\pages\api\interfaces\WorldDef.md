# Interface: WorldDef

## Properties

### allowSleep?

> `optional` **allowSleep**: `boolean`

[default: true]

***

### blockSolve?

> `optional` **blockSolve**: `boolean`

[default: true]

***

### continuousPhysics?

> `optional` **continuousPhysics**: `boolean`

[default: true]

***

### gravity?

> `optional` **gravity**: [`Vec2Value`](/api/interfaces/Vec2Value)

[default: { x : 0, y : 0}]

***

### subStepping?

> `optional` **subStepping**: `boolean`

[default: false]

***

### warmStarting?

> `optional` **warmStarting**: `boolean`

[default: true]
