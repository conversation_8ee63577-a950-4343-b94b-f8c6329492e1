# Type Alias: EvaluateFunction()

> **EvaluateFunction**: (`manifold`, `xfA`, `fixtureA`, `indexA`, `xfB`, `fixtureB`, `indexB`) => `void`

## Parameters

• **manifold**: [`Manifold`](/api/classes/Manifold)

• **xfA**: [`TransformValue`](/api/type-aliases/TransformValue)

• **fixtureA**: [`Fixture`](/api/classes/Fixture)

• **indexA**: `number`

• **xfB**: [`TransformValue`](/api/type-aliases/TransformValue)

• **fixtureB**: [`Fixture`](/api/classes/Fixture)

• **indexB**: `number`

## Returns

`void`
