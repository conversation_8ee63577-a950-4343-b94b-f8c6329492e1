// @ts-check

function createClass() {
  return class MyClass1Local {};
}
const MyClass1 = createClass();

class MyClass2 {};

/** @param {MyClass1} instance */ // instance MyClass1 = /*unresolved*/ any
function updateMyClass1Instance(instance) {
}
// defined as: function updateMyClass1Instance(instance: typeof MyClass1Local): void

/** @param {MyClass2} instance */ // class MyClass2
function updateMyClass2Instance(instance) {}
// defined as: function updateMyClass2Instance(instance: MyClass2): void


const c2 = new MyClass2();
updateMyClass2Instance(c2);

const c1 = new MyClass1();
updateMyClass1Instance(c1);