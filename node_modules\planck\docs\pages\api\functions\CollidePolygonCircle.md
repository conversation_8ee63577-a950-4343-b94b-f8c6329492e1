# Function: CollidePolygonCircle()

> **CollidePolygonCircle**(`manifold`, `polygonA`, `xfA`, `circleB`, `xfB`): `void`

## Parameters

• **manifold**: [`Manifold`](/api/classes/Manifold)

• **polygonA**: [`PolygonShape`](/api/classes/PolygonShape)

• **xfA**: [`TransformValue`](/api/type-aliases/TransformValue)

• **circleB**: [`CircleShape`](/api/classes/CircleShape)

• **xfB**: [`TransformValue`](/api/type-aliases/TransformValue)

## Returns

`void`
