// @ts-check

/**
 * @template {number} X
 * @template {number} Y
 * @template T
 */
export default class Array2D {

  /** @type {T[][]} */
  #data;
  /** @type {[ x: X, y: Y ]} */
  #length;
  #filler;

  /**
   * @param {X} sizeX
   * @param {Y} sizeY
   * @param {(x: number, y: number) => T} [filler]
   */
  constructor(sizeX, sizeY, filler) {
    this.#length = [ sizeX, sizeY ];
    this.#filler = filler ?? (() => null);
    this.#fill();
  }

  /** @returns {[ x: X, y: Y ]} */
  get length() {
    return this.#length;
  }

  /**
   * @template {0 | 1} K
   * @param {K} dimension
   */
  getLength(dimension) {
    return this.#length[dimension];
  }

  /**
   * @param {number} x
   * @param {number} y
   */
  get(x, y) {
    return this.#data[x][y];
  }

  /**
   * @param {number} x
   * @param {number} y
   */
  getSafe(x, y) {
    return this.#data[x]?.[y] ?? null;
  }

  /**
   * @param {number} x
   * @param {number} y
   * @param {T} value
   */
  set(x, y, value) {
    this.#data[x][y] = value;
  }

  #fill() {
    this.#data = new Array(this.#length[0]);
    for(let x = 0; x < this.#length[0]; x++) {
      const column = new Array(this.#length[1]);
      this.#data[x] = column;
      for(let y = 0; y < this.#length[1]; y++) {
        column[y] = this.#filler(x, y);
      }
    }
  }

  normalise() {
    this.#data = new Array(this.#length[0]);
    for(let x = 0; x < this.#length[0]; x++) {
      const column = new Array(this.#length[1]);
      this.#data[x] = column;
      for(let y = 0; y < this.#length[1]; y++) {
        column[y] = this.#filler(x, y);
      }
    }
  }

  /**
   * @template {number} NX
   * @template {number} NY
   * @param {NX} sizeX
   * @param {NY} sizeY
   */
  toResized(sizeX, sizeY) {
    return new Array2D(sizeX, sizeY, (x, y) => {
      if(x in this.#data && y in this.#data[x]) {
        return this.#data[x][y];
      }
      return this.#filler(x, y);
    });
  }

  /**
   * @template {Array2D<number, number, T>} A
   * @param {A} array
   * @param {{ x?: number, y?: number }} offset
   */
  replace(array, offset) {
    const startX = offset?.x ?? 0;
    const startY = offset?.y ?? 0;
    const endX = Math.min(this.#length[0], array.#length[0] + startX);
    const endY = Math.min(this.#length[1], array.#length[1] + startY);
    for(let x = startX; x < endX; x++) {
      for(let y = startY; y < endY; y++) {
        this.#data[x][y] = array.#data[x][y];
      }
    }
  }

  /** @returns {Generator<[ x: number, y: number ]>} */
  *keys() {
    for(const x of this.#data.keys()) {
      for(const y of this.#data[x].keys()) {
        yield [x, y];
      }
    }
  }

  *values() {
    for(const x of this.#data.keys()) {
      for(const value of this.#data[x]) {
        yield value;
      }
    }
  }

  /** @returns {Generator<[ x: number, [ y: number, value: T ] ]>} */
  *entries() {
    for(const x of this.#data.keys()) {
      for(const entry of this.#data[x].entries()) {
        yield [x, entry];
      }
    }
  }

  /** @returns {Generator<[ x: number, y: number, value: T ]>} */
  *[Symbol.iterator]() {
    for(const x of this.#data.keys()) {
      for(const y of this.#data[x].keys()) {
        yield [x, y, this.#data[x][y]];
      }
    }
  }

  /**
   * @template {number} X
   * @template {number} Y
   * @template T
   * @template {T} [R=T]
   * @param {X} sizeX
   * @param {Y} sizeY
   * @param {Iterable<[x: number, y: number, T]>} iterable
   * @param {(value: T) => R} mapper
   */
  static from(sizeX, sizeY, iterable, mapper = value => /** @type {R} */ (value)) {
    /** @type {Array2D<X, Y, R>} */
    const instance = new Array2D(sizeX, sizeY);
    for(const entry of iterable) {
      instance.set(entry[0], entry[1], mapper(entry[2]));
    }
    return instance;
  }

};