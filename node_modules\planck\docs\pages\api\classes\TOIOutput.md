# Class: TOIOutput

Output parameters for TimeOfImpact.

## Constructors

### new TOIOutput()

> **new TOIOutput**(): [`TOIOutput`](/api/classes/TOIOutput)

#### Returns

[`TOIOutput`](/api/classes/TOIOutput)

## Properties

### state

> **state**: [`TOIOutputState`](/api/enumerations/TOIOutputState) = `TOIOutputState.e_unset`

***

### t

> **t**: `number` = `-1`

## Methods

### recycle()

> **recycle**(): `void`

#### Returns

`void`
