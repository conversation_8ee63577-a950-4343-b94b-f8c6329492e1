// @ts-check

import { elementsById } from "../library/util/dom.js";
import { main } from "../library/util/util.js";
import VariableManager from "../library/variable-manager/variable-manager.js";
import { Bot } from "./entities/bot.js";
import Game from "./game.js";
import Renderer from "./renderer.js";

const $main = await main({
  version: String
});

const $E = /** @type {{ "gameCanvas": HTMLCanvasElement }} */ (elementsById(true));

/** @type {VariableManager<"title" | "version">} */
const $V = new VariableManager(document, true);

const version = $main.args.params.version;

$V.set("version", version);

const renderingContext = $E.gameCanvas.getContext("2d");
if(renderingContext === null) {
  throw new Error("Failed to get rendering context");
}
const renderer = new Renderer(renderingContext);
const game = new Game(renderer);

$main.actions.expose({
  game
});

game.start();

const bot = new Bot;
bot.position.set(100, 100);

game.addBot(bot);