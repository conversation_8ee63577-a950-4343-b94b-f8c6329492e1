# Planck.js

Planck.js is JavaScript/TypeScript rewrite of Box2D physics engine for cross-platform HTML5 game development.

#### Motivations

- Taking advantage of Box2D's efforts and achievements
- Developing readable and editable code in JavaScript/TypeScript
- Providing idiomatic JavaScript/TypeScript API
- Optimizing the library for web and mobile platforms

#### [Documentation](https://piqnt.com/planck.js/docs/)

#### [Examples](https://piqnt.com/planck.js/)

#### [Discord](https://discord.com/invite/znjh6J7)

#### [Made with Planck.js](https://github.com/piqnt/planck.js/wiki/)

#### [Report Issues](https://github.com/piqnt/planck.js/issues)
To speed up resolving issues, please provide [testbed](https://piqnt.com/planck.js/docs/testbed) code to reproduce the issue.
