# Function: CollideCircles()

> **CollideCircles**(`manifold`, `circleA`, `xfA`, `circleB`, `xfB`): `void`

## Parameters

• **manifold**: [`Manifold`](/api/classes/Manifold)

• **circleA**: [`CircleShape`](/api/classes/CircleShape)

• **xfA**: [`TransformValue`](/api/type-aliases/TransformValue)

• **circleB**: [`CircleShape`](/api/classes/CircleShape)

• **xfB**: [`TransformValue`](/api/type-aliases/TransformValue)

## Returns

`void`
