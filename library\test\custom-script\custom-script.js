// @ts-check

import AST from "./ast.js";

/** @param {string} code */
function codeToAST(code) {
  const ast = new AST;
  return ast;
}

/** @param {string} code */
function evalScript(code) {
  const parsedCode = codeToAST(code);
}

const observer = new MutationObserver(function(mutations) {
  for(const mutation of mutations) {
    if(mutation.type !== "childList") {
      continue;
    }
    for(const node of mutation.addedNodes) {
      if(node instanceof HTMLScriptElement === false || node.type !== "text/script") {
        continue;
      }
      eval(node.textContent);
    }
  }
});

observer.observe(document, {
  attributes: true,
  childList: true,
  subtree: true
});