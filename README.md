# Circuit Wars

A real-time strategy game where players write scripts to control units in a deterministic virtual machine environment. Build, program, and command your forces in epic battles where code is your weapon.

## 🎮 Game Overview

Circuit Wars is a unique RTS game that combines traditional strategy gameplay with programming mechanics. Players write JavaScript code to control their units, creating sophisticated AI behaviors, automated resource management, and complex battle strategies.

### Key Features

- **Script-Controlled Units**: Write JavaScript code to control bots and define their behavior
- **Deterministic VM**: All code execution is deterministic, ensuring fair and reproducible gameplay
- **Real-time Physics**: Built on Planck.js physics engine for realistic unit movement and collisions
- **Multiple Game Modes**: Various scenarios and challenges for different skill levels
- **Visual Programming**: Real-time visualization of unit behavior and decision-making
- **Minimap**: Strategic overview of the battlefield

## 🚀 Getting Started

### Prerequisites

- Node.js (v14 or higher)
- Python 3.4+ (for development server)
- Modern web browser with ES6 module support

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd circuit-wars
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
python server.py
```

4. Open your browser and navigate to:
```
http://localhost:7000
```

## 🏗️ Project Structure

```
circuit-wars/
├── library/                 # Core utility libraries
│   ├── canvas/             # Camera and rendering utilities
│   ├── color/              # Color management
│   ├── math/               # Vector math, shapes, rectangles
│   ├── time/               # Game loop, timing, and interpolation
│   ├── util/               # DOM utilities, promises, functions
│   └── variable-manager/   # Template variable management
├── modules/                # Main game modules
│   ├── entities/           # Game entities and physics
│   │   ├── base-entity.js  # Base entity class
│   │   ├── bot.js          # Bot entity implementation
│   │   ├── bots/           # Specialized bot types
│   │   └── physics-world/  # Physics simulation
│   ├── game.js             # Main game class and loop
│   ├── main.js             # Application entry point
│   └── renderer.js         # Canvas rendering system
├── styles/                 # CSS stylesheets
├── index.html              # Main HTML file
├── server.py               # Development server
└── package.json            # Node.js dependencies
```

## 🎯 Core Concepts

### Entities

- **BaseEntity**: Foundation class for all game objects with position, rotation, and basic physics
- **Bot**: Programmable units that can move, rotate, and interact with the environment
- **Specialized Bots**: Different bot types like HarvesterBot for resource gathering

### Physics System

Built on Planck.js, the physics system provides:
- Realistic collision detection
- Force-based movement
- Zero-gravity space environment
- Deterministic simulation

### Rendering

Custom 2D canvas renderer supporting:
- Geometric primitives (circles, polygons, lines)
- Camera system with zoom and pan
- Minimap for strategic overview
- Real-time visual feedback

## 🤖 Programming Your Bots

Bots are controlled through JavaScript code that defines their behavior. Each bot has access to:

- **Movement**: Control velocity, acceleration, and rotation
- **Sensors**: Detect nearby objects, resources, and threats
- **Communication**: Share information between units
- **Decision Making**: Implement AI logic and strategies

### Example Bot Code

```javascript
// Basic bot that moves in a circle
class CircleBot extends Bot {
  update(delta) {
    this.rotate(0.1); // Rotate slowly
    this.velocity.setFrom(this.direction.scale(this.speed));
    super.update(delta);
  }
}
```

## 🛠️ Development

### Architecture

The game follows a modular architecture with clear separation of concerns:

- **Game Loop**: Handles update/render cycles at 60 FPS
- **Entity System**: Component-based entity management
- **Physics Integration**: Seamless integration with Planck.js
- **Rendering Pipeline**: Efficient 2D canvas rendering

### Key Technologies

- **JavaScript ES6+**: Modern JavaScript with modules
- **Planck.js**: 2D physics engine (Box2D port)
- **Canvas 2D**: Hardware-accelerated rendering
- **Web Workers**: (Planned) Isolated script execution

## 🎮 Game Modes

- **Sandbox**: Free-form environment for testing and experimentation
- **Campaign**: Progressive challenges teaching programming concepts
- **Multiplayer**: Compete against other players' programmed armies
- **Tournament**: Automated battles between submitted AI scripts

## 🔧 Configuration

### Physics Settings

- Gravity: Zero (space environment)
- Time step: 1/60 seconds
- Velocity iterations: 8
- Position iterations: 3

### Rendering Settings

- Canvas size: 800x600 (configurable)
- Target FPS: 60
- Coordinate system: Center origin

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the ISC License - see the LICENSE file for details.

## 🎯 Roadmap

- [ ] Script editor with syntax highlighting
- [ ] Multiplayer networking
- [ ] Tournament system
- [ ] Resource management mechanics
- [ ] Advanced AI behaviors
- [ ] Performance optimization
- [ ] Mobile support

## 🐛 Known Issues

- Physics simulation may have slight variations on different hardware
- Large numbers of entities can impact performance
- Script debugging tools are limited

## 📞 Support

For questions, bug reports, or feature requests, please open an issue on the project repository.

---

*Circuit Wars - Where Code Meets Combat* ⚔️🤖
