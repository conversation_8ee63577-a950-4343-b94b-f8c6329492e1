# Function: Distance()

> **Distance**(`output`, `cache`, `input`): `void`

Compute the closest points between two shapes. Supports any combination of:
CircleShape, PolygonShape, EdgeShape. The simplex cache is input/output. On
the first call set SimplexCache.count to zero.

## Parameters

• **output**: [`DistanceOutput`](/api/classes/DistanceOutput)

• **cache**: [`SimplexCache`](/api/classes/SimplexCache)

• **input**: [`DistanceInput`](/api/classes/DistanceInput)

## Returns

`void`
