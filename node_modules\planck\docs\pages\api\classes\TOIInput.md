# Class: TOIInput

Input parameters for TimeOfImpact.

## Constructors

### new TOIInput()

> **new TOIInput**(): [`TOIInput`](/api/classes/TOIInput)

#### Returns

[`TOIInput`](/api/classes/TOIInput)

## Properties

### proxyA

> **proxyA**: [`DistanceProxy`](/api/classes/DistanceProxy)

***

### proxyB

> **proxyB**: [`DistanceProxy`](/api/classes/DistanceProxy)

***

### sweepA

> **sweepA**: [`Sweep`](/api/classes/Sweep)

***

### sweepB

> **sweepB**: [`Sweep`](/api/classes/Sweep)

***

### tMax

> **tMax**: `number`

defines sweep interval [0, tMax]

## Methods

### recycle()

> **recycle**(): `void`

#### Returns

`void`
