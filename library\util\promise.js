// @ts-check

/// <reference path="../../global.d.ts" />

Object.defineProperty(EventTarget.prototype, "until", {
  /** @type {EventTarget["until"]} */
  value(eventType, options = {}) {

    return new Promise((resolve, reject) => {

      if(options.signal instanceof AbortSignal) {
        const abort = () => {
          if(options.rejectOnAbort === true) {
            return reject();
          }
          // @ts-ignore
          resolve(null);
        };
        options.signal.addEventListener("abort", abort, { once: true });
        if(options.signal.aborted) {
          return abort();
        }
      }

      this.addEventListener(eventType, resolve, { once: true, signal: options.signal });

    });

  }
});