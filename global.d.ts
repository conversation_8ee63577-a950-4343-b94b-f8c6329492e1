declare type UntilOptions<R extends boolean> = {
  signal?: AbortSignal,
  rejectOnAbort?: R
};

declare interface EventTarget {
  declare until<R extends boolean = false> (
    eventType: string,
    options?: UntilOptions<R>
  ): Promise<R extends false ? Event | null : Event>
}

declare interface Window {
  declare until<K extends keyof WindowEventMap, R extends boolean = false> (
    eventType: K,
    options?: UntilOptions<R>
  ): Promise<R extends false ? WindowEventMap[K] | null : WindowEventMap[K]>
}

declare interface HTMLElement {
  declare until<K extends keyof HTMLElementEventMap, R extends boolean = false> (
    eventType: K,
    options?: UntilOptions<R>
  ): Promise<R extends false ? HTMLElementEventMap[K] | null : HTMLElementEventMap[K]>
}

declare interface Function {
  declare bind<
    T,
    F extends (...args: any[]) => any,
    A extends any[]
  >(this: F, thisArg: T, ...args: A)
    : F extends (...args: infer P) => infer R
      ? (this: T, ...args: P extends A ? never[] : P ) => R
      : F;
}