# API Reference

## Classes

- [Anim](/api/classes/Anim)
- [Atlas](/api/classes/Atlas)
- [CanvasTexture](/api/classes/CanvasTexture)
- [Component](/api/classes/Component)
- [ImageTexture](/api/classes/ImageTexture)
- [Matrix](/api/classes/Matrix)
- [Monotype](/api/classes/Monotype)
- [Pin](/api/classes/Pin)
- [PipeTexture](/api/classes/PipeTexture)
- [ResizableTexture](/api/classes/ResizableTexture)
- [Root](/api/classes/Root)
- [Sprite](/api/classes/Sprite)
- [Texture](/api/classes/Texture)
- [TextureSelection](/api/classes/TextureSelection)
- [Transition](/api/classes/Transition)

## Interfaces

- [AtlasDefinition](/api/interfaces/AtlasDefinition)
- [AtlasTextureDefinition](/api/interfaces/AtlasTextureDefinition)
- [MatrixValue](/api/interfaces/MatrixValue)
- [Vec2Value](/api/interfaces/Vec2Value)

## Type Aliases

- [ComponentEventListener](/api/type-aliases/ComponentEventListener)
- [ComponentTickListener](/api/type-aliases/ComponentTickListener)
- [FitMode](/api/type-aliases/FitMode)
- [ResizableTextureMode](/api/type-aliases/ResizableTextureMode)
- [TextureSelectionInput](/api/type-aliases/TextureSelectionInput)
- [TextureSelectionInputArray](/api/type-aliases/TextureSelectionInputArray)
- [TextureSelectionInputFactory](/api/type-aliases/TextureSelectionInputFactory)
- [TextureSelectionInputMap](/api/type-aliases/TextureSelectionInputMap)
- [TextureSelectionInputOne](/api/type-aliases/TextureSelectionInputOne)
- [TransitionEndListener](/api/type-aliases/TransitionEndListener)
- [TransitionOptions](/api/type-aliases/TransitionOptions)
- [Viewbox](/api/type-aliases/Viewbox)
- [Viewport](/api/type-aliases/Viewport)

## Variables

- [math](/api/variables/math)
- [POINTER\_CANCEL](/api/variables/POINTER_CANCEL)
- [POINTER\_CLICK](/api/variables/POINTER_CLICK)
- [POINTER\_DOWN](/api/variables/POINTER_DOWN)
- [POINTER\_MOVE](/api/variables/POINTER_MOVE)
- [POINTER\_UP](/api/variables/POINTER_UP)

## Functions

- [anim](/api/functions/anim)
- [atlas](/api/functions/atlas)
- [canvas](/api/functions/canvas)
- [column](/api/functions/column)
- [component](/api/functions/component)
- [maximize](/api/functions/maximize)
- [minimize](/api/functions/minimize)
- [monotype](/api/functions/monotype)
- [mount](/api/functions/mount)
- [pause](/api/functions/pause)
- [resume](/api/functions/resume)
- [row](/api/functions/row)
- [sprite](/api/functions/sprite)
- [texture](/api/functions/texture)
