/**
 * @template {{ [key: boolean]: any }} M
 * @param {M} map
 * @returns {M[keyof M]}
 * @example
 * AND({ true: 10, false: 20 }, true, false); // = 20
 */
export function AND(map, ...vars) {
  return map[vars.every(value => value)];
}

/**
 * @template {{ [key: boolean]: any }} M
 * @param {M} map
 * @returns {M[keyof M]}
 * @example
 * OR({ true: 10, false: 20 }, true, false); // = 10
 */
export function OR(map, ...vars) {
  return map[vars.some(value => value)];
}