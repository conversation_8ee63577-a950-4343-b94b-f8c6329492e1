# Function: CollideEdgeCircle()

> **CollideEdgeCircle**(`manifold`, `edgeA`, `xfA`, `circleB`, `xfB`): `void`

## Parameters

• **manifold**: [`Manifold`](/api/classes/Manifold)

• **edgeA**: [`EdgeShape`](/api/classes/EdgeShape)

• **xfA**: [`TransformValue`](/api/type-aliases/TransformValue)

• **circleB**: [`CircleShape`](/api/classes/CircleShape)

• **xfB**: [`TransformValue`](/api/type-aliases/TransformValue)

## Returns

`void`
